import type { CustomTemplateFunctions } from '@4-sure/ui-platform';

declare module '@4-sure/ui-platform' {
  interface CustomTemplateFunctions {
    fieldOps: {
      testAddFunc: (a: number, b: number) => number;
      getOnboardingState: (current_state_id: number) => string;
      getAvailableOnboardingStateOptions: (
        current_state_id: number
      ) => Record<string, string>;
      calculateSPCommission: (amount: number, rate: number) => number;
      formatCurrency: (value: number, currency: string) => string;
      getSPStatusLabel: (statusId: number) => string;
      formatSPAddress: (address: SPAddress) => string;
      getSPCertificationDocuments: <T extends object, U extends object>(
        company_documentation: T[],
        sp_enums: U
      ) => any[];
      getSPClients: <T extends object, U extends object>(
        sp_profile: T,
        sp_enums: U
      ) => any[];
    };
  }
}
