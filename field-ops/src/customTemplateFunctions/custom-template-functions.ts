export const testAddFunc = (a: number, b: number) => a + b;
const ONBOARDING_STATES = {
  3: 'Active',
  6: 'Suspended',
  11: 'Disabled',
  10: 'Training',
} as const;

export const getOnboardingState = (current_state_id: number) => {
  return ONBOARDING_STATES[current_state_id];
};
export const getAvailableOnboardingStateOptions = (current_state: number) => {
  return Object.entries(ONBOARDING_STATES)
    .filter(([key]) => Number(key) !== current_state)
    .map(([key, value]) => ({ id: key, name: value }))
    .reduce((acc, item) => {
      acc[item.id] = item.name;
      return acc;
    }, {});
};

type SPAddress = {
  line1?: string;
  city?: string;
  province?: string;
  postalCode?: string;
};

/** Calculate service provider commission from amount and rate */
export const calculateSPCommission = (amount: number, rate: number) =>
  Number(amount) * Number(rate);
/** Format a number as currency string e.g., ZAR 123.45 */
export const formatCurrency = (value: number, currency: string) =>
  `${currency} ${Number(value).toFixed(2)}`;
/** Map a status id to a human readable label */
export const getSPStatusLabel = (statusId: number) => {
  const map: Record<number, string> = ONBOARDING_STATES;
  return map[Number(statusId)] ?? `Status ${statusId}`;
};
/** Format an SP address object into a single string */
export const formatSPAddress = (address: SPAddress) => {
  if (!address) return '';
  const parts = [
    address.line1,
    address.city,
    address.province,
    address.postalCode,
  ]
    .filter(Boolean)
    .join(', ');
  return parts;
};

interface DocumentWithMeta {
  created: string;
  meta: {
    certification: {
      skill: number;
      issuer: number;
      issued_at: string;
      valid_until: string;
      certificate_number: string;
    };
  };
}
interface SPEnum {
  issuers: {
    id: string | number;
    name: string;
  }[];
  companies: SPClient[];
}

export const getSPCertificationDocuments = <T extends object, U extends object>(
  company_documentation: T[],
  sp_enums: U
) => {
  const isCertificateDocument = (
    document: any
  ): document is DocumentWithMeta => {
    return document.meta && document.meta.certification;
  };
  const isSPEnum = (enumObj: any): enumObj is SPEnum => {
    return enumObj && enumObj.issuers && Array.isArray(enumObj.issuers);
  };
  const skill_certifications = company_documentation
    .filter((item) => {
      if (!isCertificateDocument(item)) return false;
      return true;
    })
    .reduce((acc, item) => {
      if (!isCertificateDocument(item)) return acc;
      const cert = item.meta.certification;
      const key = cert.skill + '-' + cert.issuer;
      if (
        !acc.has(key) ||
        new Date(item.created) > new Date(acc.get(key).created)
      ) {
        acc.set(key, item);
      }

      return acc;
    }, new Map())
    .values();
  const certificatesArray = Array.from(skill_certifications).map((item) => {
    const issuers = isSPEnum(sp_enums) ? sp_enums.issuers : [];
    return {
      ...item,
      meta: {
        'Certificate Issuer': issuers.find(
          (issuer) => issuer.id === item.meta.certification.issuer
        )?.name,
        'Certificate Number': item.meta.certification.certificate_number,
        'Certificate Issue Date': new Date(
          item.meta.certification.issued_at
        ).toLocaleDateString(),
        'Certificate Expiry Date': new Date(
          item.meta.certification.valid_until
        ).toLocaleDateString(),
      },
    };
  });
  if (
    typeof certificatesArray !== 'object' &&
    !Array.isArray(certificatesArray)
  )
    return [];
  return certificatesArray || [];
};

interface SPClient {
  client_id: string | number;
  active: number;
  name: string;
  id: string | number;
  short_name: string;
}

interface SPProfile {
  companies: SPClient[];
}

const isSPProfile = (profile: any): profile is SPProfile => {
  return profile && profile.companies && Array.isArray(profile.companies);
};
const isClientsList = (enumObj: any): enumObj is SPEnum => {
  return enumObj && enumObj.companies && Array.isArray(enumObj.companies);
};

export const getSPClients = <T extends object, U extends object>(
  sp_profile: T,
  sp_enums: U
) => {
  if (!isSPProfile(sp_profile)) return [];
  if (!isClientsList(sp_enums)) return [];
  return sp_enums.companies
    .filter((company) =>
      sp_profile.companies.find((org) => org.client_id === company.id)
    )
    .map((org) => {
      const hasShortName = isValidClient(org) && org.short_name && org.name;
      return {
        ...org,
        label: org.name,
        tenant_id: org.id,
        name: hasShortName
          ? 'panel_activation_status_' + org?.short_name + '_active'
          : '',
      };
    });
};

const isValidClient = (company: any): company is SPClient => {
  return company && company.id && company.name && company.short_name;
};
