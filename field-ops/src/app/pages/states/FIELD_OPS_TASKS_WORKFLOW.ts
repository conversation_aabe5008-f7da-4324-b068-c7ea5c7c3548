import { StateConfig } from '@4-sure/ui-platform';

/*
 * SECTION: TASKS STATE
 * Tasks state which is the main section of the application
 */
// #region TASKS STATE
export const FIELD_OPS_TASKS_WORKFLOW_STATE = {
  title: { template: 'field-ops-tasks-workflow' },
  // #region TASKS STATE LEVEL FETCH CALLS
  fetchCalls: [
    {
      key: 'tasks',
      method: 'POST',
      body: {},
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_tasks',
      slicePath: 'payload',
    },
  ],
  // #endregion
  defaultScreen: 'list-view',
  screens: {
    /*
     * SECTION: TASKS LIST VIEW
     * Tasks list screen configuration
     */
    // #region TASKS LIST VIEW SCREEN
    'list-view': {
      layout: {},
      fetchCalls: [],
      // #region TASKS LIST FRAGMENTS
      fragments: [
        {
          component: 'TeamMemberCardList',
          debug: true,
          debounceMs: 2000,
          props: {
            // linkUrl: '',
            linkUrlParams: [{ key: 'task_id', value: 'id' }],
            data: `js:{
              const urlLinkMap = ${JSON.stringify({
                1: '/field-ops/tasks/edit/details',
                2: '/field-ops/tasks/edit/details',
                6: '/field-ops/tasks/edit/details',
                7: '/field-ops/tasks/pre-training/overview',
                8: '/field-ops/tasks/post-training/overview',
                9: '/field-ops/tasks/review-suspension/details',
              })};
              const daysRemaining = (dateString) => {
                const MS_PER_DAY = 1000 * 60 * 60 * 24;
                const today = Date.now();
                const due_date = new Date(dateString);
                return (due_date.valueOf() - today)/MS_PER_DAY;
              }
              return tasks.map(_task => ({
                ..._task,
                navigate_to_url: urlLinkMap[_task.task_type_id]
                }))
                .filter(_task => !_task.due_date || daysRemaining(_task.due_date) < 5);
                }`,
            config: [
              {
                type: 'linkColumn',
                showAvatar: true,
                showTitleText: true,
                showRating: false,
                showStatusIndicator: false,
                titleKey: 'task_type',
              },
              { type: 'textColumn', titleKey: 'content_object' },
              { type: 'textColumn', titleKey: 'status' },
              { type: 'textColumn', titleKey: 'sla' },
              { type: 'textColumn', titleKey: 'created' },
              { type: 'textColumn', titleKey: 'due_date' },
            ],
          },
          layout: {},
        },
      ],
      // #endregion
      navs: [],
    },
    // #endregion
  },
  /*
   * SECTION: TASKS ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region TASKS ACTION PANELS
  actionPanels: [
    /*
     * SECTION: TASKS SCRATCH PAD
     * Configuration for the scratch pad in the tasks state
     */
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
  // #endregion
} satisfies StateConfig;
// #endregion
