import { bbbeee, StateConfig, validationRegex } from '@4-sure/ui-platform';

/*
 * SECTION: PRE-TRAINING STATE
 * The pre-traing state is a read only view for a FO agent to plan training with a sp after evaluation
 */
// #region PRE-TRAINING STATE
export const FIELD_OPS_REVIEW_SUSPENSION_STATE = {
  title: { template: 'field-ops-task-review-suspension' },
  fetchCalls: [
    // #region TASKS EDIT STATE FETCHCALLS
    // fetchcalls that will be made the state level
    {
      key: 'sp_enums',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    // TODO: additional logic to be added to determine nature of task and respective api call
    {
      key: 'task_details',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_task_details',
      slicePath: 'payload',
      successFetchCalls: [
        {
          key: 'sp_profile',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
        {
          key: 'sp_history',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp_history',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
      ],
    },
  ],
  // #endregion
  defaultScreen: 'details',
  screens: {
    // #region TASKS/EDIT/OVERVIEW SCREEN
    details: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Platform Suspension',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'KeyValueList',
          props: {
            numbering: false,
            data: {
              'Suspension End Date':
                'js:{task_details.due_date.split("T")[0].replace(/[-]/g, "/")}',
              // 'Reason': '',
              // 'Agent Notes': ''
            },
            width: 'auto',
            itemMargin: '10px',
            align: 'default',
            colouredHeading: {
              headingString: 'Details',
              headingColour: 'alternative',
            },
            colour: 'default',
            size: 'medium',
            textTransform: 'default',
          },
          layout: {
            display: 'grid',
            gridAutoFlow: 'column',
            justifyItems: 'center',
            gap: '58px',
            width: '100%',
            paddingTop: '2rem',
            paddingLeft: '1rem',
            paddingRight: '1rem',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/field-ops/tasks'],
            },
          ],
        },
        // #region DEACTIVATE SP BUTTON
        {
          label: `Extend Suspension`,
          position: 'right',
          onClick: [
            {
              type: 'clientAction',
              action: 'triggerModal',
              payload: [
                {
                  display: true,
                  type: 'warning',
                  heading: 'Suspend SP',
                  headingType: 'page-heading',
                  layout: {},
                  onEnter: [],
                  onLeave: [],
                  onClose: [
                    {
                      type: 'clientAction',
                      action: 'resetFields',
                      payload: {
                        fields: [
                          {
                            fieldName: 'platform_status_change_reason',
                            defaultValue: undefined,
                          },
                          {
                            fieldName: 'platform_status_change_notes',
                            defaultValue: undefined,
                          },
                          {
                            fieldName: 'platform_status',
                            defaultValue: '#{sp_profile.onboarding_state}',
                          },
                          {
                            fieldName: 'suspension_duration',
                            defaultValue: undefined,
                          },
                        ],
                      },
                    },
                    {
                      type: 'clientAction',
                      action: 'clearStore',
                      payload: ['platform_status_selected'],
                    },
                  ],
                  fragments: [
                    {
                      component: 'FormBuilder',
                      props: {
                        defaultValues: {
                          platform_status_change_reason: '',
                          platform_status_change_notes: '',
                          suspension_duration: '',
                        },
                        config: {
                          style: {
                            display: 'grid',
                            gridAutoFlow: 'row',
                            rowGap: '2rem',
                            columnGap: '1rem',
                            justifyItems: 'center',
                            alignContent: 'space-around',
                            height: 'auto',
                            paddingTop: '2rem',
                            paddingBottom: '2rem',
                            width: '100%',
                          },
                          controls: [
                            {
                              type: 'single-select',
                              name: 'suspension_duration',
                              label: 'Length of suspension',
                              placeholder: 'Select a time frame',
                              labelProp: 'name',
                              valueProp: 'days',
                              validation: {
                                required: {
                                  value: true,
                                  message:
                                    'Please provide reason for disabling SP.',
                                },
                              },
                              options: {
                                source: 'store',
                                storeDataPath: 'sp_enums.suspend_periods',
                              },
                              notSearchable: true,
                              css: {
                                wrapper: {
                                  width: '100%',
                                },
                              },
                            },
                            {
                              type: 'single-select',
                              name: 'platform_status_change_reason',
                              label: 'Reason for suspension',
                              placeholder: 'Select a Reason',
                              labelProp: 'name',
                              valueProp: 'id',
                              validation: {
                                required: {
                                  value: true,
                                  message:
                                    'Please provide reason for suspending SP.',
                                },
                              },
                              options: {
                                source: 'literal',
                                data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 11)}',
                              },
                              notSearchable: true,
                              css: {
                                wrapper: {
                                  width: '100%',
                                },
                              },
                            },
                            {
                              type: 'textarea',
                              name: 'platform_status_change_notes',
                              label: 'Notes',
                              rows: 10,
                              validation: {
                                required: {
                                  value: true,
                                  message: 'This field is required',
                                },
                              },
                              css: {
                                wrapper: {
                                  width: '100%',
                                },
                              },
                            },
                          ],
                        },
                      },
                      layout: {
                        justifyItems: 'center',
                        display: 'grid',
                      },
                    },
                    {
                      component: 'ButtonRow',
                      layout: {
                        width: 'fit-content',
                        margin: 'auto',
                      },
                      props: {
                        buttons: [
                          {
                            btnValue: 'Cancel suspension',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'resetFields',
                                payload: {
                                  fields: [
                                    {
                                      fieldName:
                                        'platform_status_change_reason',
                                      defaultValue: undefined,
                                    },
                                    {
                                      fieldName: 'platform_status_change_notes',
                                      defaultValue: undefined,
                                    },

                                    {
                                      fieldName: 'platform_status',
                                      defaultValue:
                                        '#{sp_profile.onboarding_state}',
                                    },
                                    {
                                      fieldName: 'suspension_duration',
                                      defaultValue: undefined,
                                    },
                                  ],
                                },
                                async: true,
                              },
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                                async: true,
                              },
                              {
                                type: 'clientAction',
                                action: 'clearStore',
                                payload: ['platform_status_selected'],
                                async: true,
                              },
                            ],
                          },
                          {
                            btnValue: 'Suspend SP',
                            disabledWhen:
                              '!$store.formDataRaw.platform_status_change_reason',
                            onClick: [
                              {
                                type: 'clientAction',
                                action: 'triggerFetchCall',
                                payload: [
                                  {
                                    key: 'complete_task',
                                    method: 'POST',
                                    url: '{VITE_SP_SERVER}/api/v1/task_actions/complete_task',
                                    body: {
                                      task_id: '{task_details.id}',
                                    },
                                    slicePath: 'payload',
                                  },
                                  {
                                    key: 'sp_profile',
                                    method: 'POST',
                                    url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                    body: {
                                      onboarding_state_id: 11,
                                      sp_id: '{sp_profile.id}',
                                      reason_id:
                                        '{formDataRaw.platform_status_change_reason}',
                                      detailed_reason:
                                        '{formDataRaw.platform_status_change_notes|""}',
                                      suspend_period:
                                        '#{formDataRaw.suspension_duration}',
                                    },
                                    slicePath: 'payload',
                                  },
                                ],
                                async: true,
                                asyncLoadStart: true,
                              },
                              {
                                type: 'clientAction',
                                action: 'closeModal',
                                async: true,
                                asyncLoadEnd: true,
                              },
                            ],
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        // #endregion
        // #region CONTACTING COMPLETE BUTTON
        {
          label: `Change SP State`,
          position: 'right',
          toScreen: '../change-sp-state',
        },
        // #endregion
      ],
    },
    // #endregion
    // #region TASKS/EDIT/COMPANY INFORMATION SCREEN
    history: {
      layout: {},
      fetchCalls: [
        // {
        //   key: 'sp_history',
        //   method: 'POST',
        //   url: '{VITE_SP_SERVER}12345/api/v1/spaas_actions/get_sp_history',
        //   body: { sp_id: '#{task_details.content_object.id}' },
        //   slicePath: 'payload',
        // },
      ],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'History',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Review all changes to this Service Provider below',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '4rem',
          },
        },
        {
          component: 'ArrayMapWithDivider',
          // props: {
          //   events: '$sp_history',
          // },
          props: {
            events: `js:{
              return sp_history.map(item => ({
                'Date & Time': item.timestamp.split('T')[0] + ' - ' + (item.timestamp.split('T')[1]?.split(':').slice(0,2).join(':') || ''),
                'Action': item.event_source,
                'Agent Name': item.event_initiator?.split(':').slice(1).join(':').trim() || item.event_initiator,
              }));
            }`,
          },
          layout: {},
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../details',
        },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/COMPANY INFORMATION SCREEN
    'change-sp-state': {
      layout: {},
      fetchCalls: [
        // {
        //   key: 'sp_history',
        //   method: 'POST',
        //   url: '{VITE_SP_SERVER}12345/api/v1/spaas_actions/get_sp_history',
        //   body: { sp_id: '#{task_details.content_object.id}' },
        //   slicePath: 'payload',
        // },
      ],
      fragments: [
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Services',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              platform_status: '$sp_profile.onboarding_state',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'single-select',
                  name: 'platform_status',
                  label: 'Connexa platform status',
                  instructions: ['Which is the current status of the company'],
                  valueProp: 'id',
                  labelProp: 'name',
                  options: {
                    source: 'literal',
                    data: `js:{const data = [
                      {
                        id: 6,
                        name: 'Disable',
                      },
                      {
                        id: 11,
                        name: 'Suspend',
                      },
                      {
                        id: 10,
                        name: 'Move to Training',
                      },
                      {
                        id: 3,
                        name: 'Activate',
                      },
                    ]
                    return data.filter(state => (sp_profile?.onboarding_state || 3) !== state.id)}`,
                  },
                  notSearchable: true,
                  placeholder: `js:{
                      const onboarding_states = {
                        3:'ACTIVE',
                        6: 'DISABLED',
                        11: 'SUSPENDED',
                        10: 'TRAINING'
                      };
                      return onboarding_states[sp_profile.onboarding_state]
                    }`,
                  onDropdownSelectChange: [
                    {
                      type: 'clientAction',
                      action: 'triggerFetchCall',
                      payload: [
                        {
                          key: 'complete_task',
                          method: 'POST',
                          url: '{VITE_SP_SERVER}/api/v1/task_actions/complete_task',
                          body: {
                            task_id: '{task_details.id}',
                          },
                          slicePath: 'payload',
                        },
                      ],
                      async: true,
                      asyncLoadStart: true,
                      asyncLoadEnd: true,
                    },
                    {
                      type: 'clientAction',
                      action: 'switch',
                      payload: {
                        pathToValue: 'formDataRaw.platform_status',
                        cases: [
                          {
                            caseName: 3,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Activating this SP will restore their ability to receive pings and they will become active on the system.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to enable this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Activate SP',
                                                      headingType:
                                                        'page-heading',
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for activating sp',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 3)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel activating SP',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Activate SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 3,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    asyncLoadStart:
                                                                      true,
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 6,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Disabling this SP will remove their ability to receive pings and they will be removed from the system.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to disable this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },

                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Disable SP',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for disabling sp',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for disabling SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 6)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'This field is required',
                                                                    },
                                                                  },
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel disabling SP',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Disable SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 6,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 10,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Moving this SP to training will remove their ability to receive pings, if they had been previously actiove. Until they complete their re-training, they will not receive new jobs although they will still have access to the SP Management and their workflow.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to move SP to training?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading:
                                                        'Move SP to Training',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for moving SP to training',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for moving SP to training.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 10)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel Training',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Move SP to Training',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 10,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 11,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Suspending this SP will remove their ability to receive pings and they will not get any new jobs for the duration of the suspension.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                  maxWidth: '712px',
                                                  textAlign: 'center',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Are you sure you want to suspend this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                  maxWidth: '712px',
                                                  textAlign: 'center',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Suspend SP',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                              suspension_duration:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'suspension_duration',
                                                                  label:
                                                                    'Length of suspension',
                                                                  placeholder:
                                                                    'Select a time frame',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'days',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for disabling SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'store',
                                                                    storeDataPath:
                                                                      'sp_enums.suspend_periods',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for suspension',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for suspending SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 11)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'This field is required',
                                                                    },
                                                                  },
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel suspension',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },

                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                    async: true,
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Suspend SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 11,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                          suspend_period:
                                                                            '#{formDataRaw.suspension_duration}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            caseName: 'default',
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'log',
                                payload: [
                                  'No action can be performed for this option...',
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      async: true,
                    },
                  ],
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '70%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/field-ops'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
      ],
    },
    // #endregion
  },
  /*
   * SECTION: TASKS/EDIT ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region TASKS/EDIT ACTION PANELS
  actionPanels: [
    // #region SCRATCH PAD
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
    // #endregion
  ],
  // #endregion
} satisfies StateConfig;
// #endregion
