import { StateConfig, validationRegex } from '@4-sure/ui-platform';

/*
 * SECTION: PERSONAL SETTINGS STATE
 * Settings state section for managing profile
 */
// #region PERSONAL SETTINGS STATE
export const PERSONAL_SETTINGS_STATE = {
  title: { template: '' },
  fetchCalls: [
    {
      key: 'my_profile',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_profile',
      body: {},
      slicePath: 'payload',
      successFetchCalls: [
        {
          key: 'profile_picture',
          method: 'POST',
          url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_file',
          body: { file_id: '$profile_pic' },
          slicePath: 'payload',
        },
      ],
    },
    {
      key: 'staff_enums',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    {
      key: 'profile_documents',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/file_actions/get_files',
      body: {
        purpose: 'Identity',
      },
      slicePath: 'payload',
    },
    {
      key: 'staffFieldAccess',
      method: 'POST',
      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/staff_field_access',
      body: {},
      slicePath: 'payload',
    },
  ],
  defaultScreen: 'my-profile',
  screens: {
    // screen
    'my-profile': {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'ProfileHero',
          layout: {
            width: '50%',
            minWidth: '712px',
            margin: '0 auto 2rem',
          },
          props: {
            fullname: '$my_profile.full_name',
            username: '$my_profile.username',
            active: '$my_profile.active.active_state',
            activeState: '$my_profile.active.active_state_name',
            activeStateReason: '$my_profile.active.reason',
            image: '$profile_picture.file',
            url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
            action: '/settings/personal/my-profile',
          },
        },
        {
          component: 'ButtonRow',
          layout: {
            display: 'grid',
            gridAutoFlow: 'column',
            gap: '2rem',
            gridTemplateColumns: '1/3fr',
            justifyItems: 'center',
            alignContent: 'space-around',
          },
          props: {
            buttons: [
              {
                btnValue: 'change password',
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'triggerModal',
                    payload: [
                      {
                        display: true,
                        type: 'warning',
                        heading: 'Change Password',
                        headingType: 'page-heading',
                        layout: {},
                        onEnter: [],
                        onLeave: [],
                        fragments: [
                          {
                            component: 'FormBuilder',
                            layout: {
                              width: '55%',
                              margin: '0 auto',
                            },
                            props: {
                              config: {
                                style: {
                                  display: 'grid',
                                  gridAutoFlow: 'row',
                                  gap: '2rem',
                                },
                                controls: [
                                  {
                                    type: 'plain-text',
                                    name: 'old_password',
                                    label: 'Old Password',
                                    inputType: 'password',
                                    validation: {
                                      required: {
                                        value: true,
                                        message: 'Old password is required',
                                      },
                                    },
                                    css: { wrapper: { width: '100%' } },
                                  },
                                  {
                                    type: 'plain-text',
                                    name: 'new_password',
                                    label: 'New Password',
                                    inputType: 'password',
                                    validation: {
                                      pattern: {
                                        value: validationRegex.password.pattern,
                                        message:
                                          validationRegex.password.message,
                                      },
                                      required: {
                                        value: true,
                                        message: 'New password is required',
                                      },
                                      minLength: {
                                        value: 12,
                                        message:
                                          'New password must be at least 12 characters',
                                      },
                                      maxLength: {
                                        value: 30,
                                        message:
                                          'New password must not exceed 30 characters',
                                      },
                                    },
                                    css: { wrapper: { width: '100%' } },
                                    instructions:
                                      'Password minimum characters 12, one uppercase, one lowercase, one special character, one number',
                                  },
                                  {
                                    type: 'plain-text',
                                    name: 'confirm_password',
                                    inputType: 'password',
                                    validation: {
                                      match: {
                                        value: 'new_password',
                                        message: 'Passwords do not match',
                                      },
                                    },
                                    label: 'Confirm New Password',
                                    css: { wrapper: { width: '100%' } },
                                  },
                                ],
                              },
                            },
                          },
                          {
                            component: 'ButtonRow',
                            layout: {
                              width: 'fit-content',
                              margin: '0 auto',
                            },
                            props: {
                              buttons: [
                                {
                                  btnValue: 'Cancel Change',
                                  disabledWhen:
                                    '!$formState.dirtyFields.old_password && !$formState.dirtyFields.new_password && !$formState.dirtyFields.confirm_password || !(Object.keys($formState.touchedFields).length > 0)',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'resetFields',
                                      payload: {
                                        fields: [
                                          'old_password',
                                          'new_password',
                                          'confirm_password',
                                        ],
                                      },
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                  ],
                                },
                                {
                                  btnValue: 'Change Password',
                                  disabledWhen:
                                    '!$formState.dirtyFields.old_password || !$formState.dirtyFields.new_password || !$formState.dirtyFields.confirm_password || !!$formState.errors.confirm_password || !!$formState.errors.new_password ||!!$formState.errors.old_password || !(Object.keys($formState.touchedFields).length > 0)',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'log',
                                      payload: ['Change Password clicked'],
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'submitAndNavigate',
                                      payload: [
                                        {
                                          old_password:
                                            '$formDataRaw.old_password',
                                          new_password:
                                            '$formDataRaw.new_password',
                                        },
                                        {
                                          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/change_password',
                                          headers: {},
                                          redirect:
                                            '/settings/personal/my-profile',
                                        },
                                        {
                                          method: 'post',
                                          action:
                                            '/settings/personal/my-profile',
                                        },
                                      ],
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                        ],
                        navs: [],
                      },
                    ],
                  },
                ],
                actiontype: 'alternative',
              },
            ],
          },
        },
        {
          component: 'FormBuilder',
          props: {
            proof_of_id: '$profile_documents?find:item.purpose==="Identity"',
            defaultValues: {
              email_address: '$my_profile.email_address',
              contact_number: '$my_profile.contact_number',
              full_name: '$my_profile.full_name',
              roles: '$my_profile.roles',
              id_type: '$my_profile.id_type',
              id_passport: '$my_profile.id_passport',
              proof_of_id: undefined,
            },
            fieldAccessPermissions: '#{staffFieldAccess}',
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gridTemplateRows: 'repeat(3, 1fr)',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'full_name',
                  label: 'Name',
                  validation: {
                    pattern: {
                      value: validationRegex.name.pattern,
                      message: validationRegex.name.message,
                    },
                    minLength: {
                      value: 2,
                      message: 'Name must be at least 2 characters',
                    },
                    maxLength: {
                      value: 30,
                      message: 'You cant go past 30 characters',
                    },
                  },
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'drag-and-drop-in-memory',
                  name: 'proof_of_id',
                  label: 'Proof of ID',
                  url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                  action: '/settings/personal/my-profile',
                  purpose: 'Identity', // expected by file sent to server
                  list: 'False', //expected by file sent to server
                  emptyText: 'No files uploaded',
                  fileTypesAllowed: ['pdf', 'image'],
                  fileSizeLimit: 5242880,
                  pathToRefId: 'my_profile.sso_id',
                  disabledWhen: true,
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: '1 / span 2',
                      gridRow: 3,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_number',
                  label: 'Cell Number',
                  validation: {
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 4,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'email_address',
                  label: 'Email',
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  validation: {
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 4,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'id_passport',
                  label: 'Identity Number',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.id_passport.pattern,
                      message: validationRegex.id_passport.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 1,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'id_type',
                  label: 'ID Types',
                  placeholder: 'Select ID Type',
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'staff_enums.id_types',
                  },
                  validation: {
                    required: true,
                    message: 'This field is required',
                  },
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 2,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multi-select',
                  name: 'roles',
                  label: 'Role(s)',
                  placeholder: 'Select roles',
                  labelProp: 'description',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'staff_enums.roles_1',
                  },
                  validation: {
                    required: true,
                    message: 'This field is required',
                  },
                  fieldAccessPath: {
                    view: 'staffmember',
                    edit: 'staffmember',
                    special: 'staffmember',
                  },
                  css: {
                    wrapper: {
                      gridColumn: 2,
                      gridRow: 1,
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: 'You have unsaved changes. What would you like to do?',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    actiontype: 'warning',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks'],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    actiontype: 'proceed',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'conditional',
                                        payload: {
                                          condition:
                                            '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                                          actions: {
                                            whenTrue: [
                                              {
                                                type: 'clientAction',
                                                action: 'submitAndNavigate',
                                                payload: [
                                                  {
                                                    full_name:
                                                      '$formDataRaw.full_name',
                                                    contact_number:
                                                      '$formDataRaw.contact_number',
                                                    email_address:
                                                      '$formDataRaw.email_address',
                                                    id_type:
                                                      '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                    id_passport:
                                                      '$formDataRaw.id_passport',
                                                    roles: '$formDataRaw.roles',
                                                    staff_id:
                                                      '$my_profile.sso_id',
                                                  },
                                                  {
                                                    url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                    headers: {},
                                                    redirect:
                                                      '/field-ops/tasks',
                                                  },
                                                  {
                                                    method: 'post',
                                                    action:
                                                      '/settings/personal/my-profile',
                                                  },
                                                ],
                                              },
                                            ],
                                            whenFalse: [
                                              {
                                                type: 'clientAction',
                                                action: 'conditional',
                                                payload: {
                                                  condition:
                                                    '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                                                  actions: {
                                                    whenTrue: [
                                                      {
                                                        type: 'clientAction',
                                                        action: 'submitAsync',
                                                        payload: {
                                                          calls: [
                                                            {
                                                              key: 'update_proof_of_id',
                                                              url: `${
                                                                import.meta.env
                                                                  .VITE_STAFF_SERVER
                                                              }/api/v1/file_actions/upload_file`,
                                                              data: {
                                                                file: '$proof_of_id.file',
                                                                purpose:
                                                                  '$proof_of_id.purpose',
                                                                list: '$proof_of_id.list',
                                                                staff_id:
                                                                  '$proof_of_id.ref_id',
                                                              },
                                                            },
                                                          ],
                                                          onFinish: {
                                                            type: 'clientAction',
                                                            action: 'navigate',
                                                            payload: [
                                                              '/field-ops/tasks',
                                                            ],
                                                          },
                                                          redirect:
                                                            '/settings/personal/my-profile',
                                                        },
                                                      },
                                                      {
                                                        type: 'clientAction',
                                                        action: 'resetFields',
                                                        payload: {
                                                          fields: [
                                                            {
                                                              fieldName:
                                                                'proof_of_id',
                                                              defaultValue:
                                                                undefined,
                                                            },
                                                          ],
                                                        },
                                                      },
                                                    ],
                                                    whenFalse: [
                                                      {
                                                        type: 'clientAction',
                                                        action: 'submitAsync',
                                                        payload: {
                                                          calls: [
                                                            {
                                                              key: 'update_proof_of_id',
                                                              url: `${
                                                                import.meta.env
                                                                  .VITE_STAFF_SERVER
                                                              }/api/v1/file_actions/upload_file`,
                                                              data: {
                                                                file: '$proof_of_id.file',
                                                                purpose:
                                                                  '$proof_of_id.purpose',
                                                                list: '$proof_of_id.list',
                                                                staff_id:
                                                                  '$proof_of_id.ref_id',
                                                              },
                                                            },
                                                          ],
                                                          onFinish: {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'postData',
                                                              'filePostData',
                                                            ],
                                                          },
                                                          redirect:
                                                            '/settings/profile/my-profile',
                                                        },
                                                      },
                                                      {
                                                        type: 'clientAction',
                                                        action:
                                                          'submitAndNavigate',
                                                        payload: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'submitAndNavigate',
                                                            payload: [
                                                              {
                                                                full_name:
                                                                  '$formDataRaw.full_name',
                                                                contact_number:
                                                                  '$formDataRaw.contact_number',
                                                                email_address:
                                                                  '$formDataRaw.email_address',
                                                                id_type:
                                                                  '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                                                id_passport:
                                                                  '$formDataRaw.id_passport',
                                                                roles:
                                                                  '$formDataRaw.roles',
                                                                staff_id:
                                                                  '$my_profile.sso_id',
                                                              },
                                                              {
                                                                url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                                headers: {},
                                                                redirect:
                                                                  '/field-ops/tasks',
                                                              },
                                                              {
                                                                method: 'post',
                                                                action:
                                                                  '/settings/personal/my-profile',
                                                              },
                                                            ],
                                                          },
                                                          {
                                                            url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                                            headers: {},
                                                            redirect:
                                                              '/field-ops/tasks',
                                                          },
                                                          {
                                                            method: 'post',
                                                            action:
                                                              '/settings/personal/my-profile',
                                                          },
                                                        ],
                                                      },
                                                      {
                                                        type: 'clientAction',
                                                        action: 'resetFields',
                                                        payload: {
                                                          fields: [
                                                            {
                                                              fieldName:
                                                                'proof_of_id',
                                                              defaultValue:
                                                                undefined,
                                                            },
                                                          ],
                                                        },
                                                      },
                                                      {
                                                        type: 'clientAction',
                                                        action: 'clearStore',
                                                        payload: ['postData'],
                                                      },
                                                    ],
                                                  },
                                                },
                                              },
                                            ],
                                          },
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0) || !$store?.postData',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'full_name',
                    defaultValue: '$my_profile.full_name',
                  },
                  {
                    fieldName: 'contact_number',
                    defaultValue: '$my_profile.contact_number',
                  },
                  {
                    fieldName: 'email_address',
                    defaultValue: '$my_profile.email_address',
                  },
                  {
                    fieldName: 'roles',
                    defaultValue: '$my_profile.roles',
                  },
                  {
                    fieldName: 'id_type',
                    defaultValue: '$my_profile.id_type',
                  },
                  {
                    fieldName: 'id_passport',
                    defaultValue: '$my_profile.id_passport',
                  },
                  {
                    fieldName: 'proof_of_id',
                    defaultValue: undefined,
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0) || !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store.filePostData && (Object.keys($store.filePostData) < 1)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'submitAndNavigate',
                      payload: [
                        {
                          full_name: '$formDataRaw.full_name',
                          contact_number: '$formDataRaw.contact_number',
                          email_address: '$formDataRaw.email_address',
                          id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                          id_passport: '$formDataRaw.id_passport',
                          roles: '$formDataRaw.roles',
                          staff_id: '$my_profile.sso_id',
                        },
                        {
                          url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                          headers: {},
                          redirect: '/field-ops/tasks',
                        },
                        {
                          method: 'post',
                          action: '/settings/personal/my-profile',
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'conditional',
                      payload: {
                        condition:
                          '$store.formPostData && (Object.keys($store.formPostData) < 1)',
                        actions: {
                          whenTrue: [
                            {
                              type: 'clientAction',
                              action: 'submitAsync',
                              payload: {
                                calls: [
                                  {
                                    key: 'update_proof_of_id',
                                    url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                    data: {
                                      file: '$proof_of_id.file',
                                      purpose: '$proof_of_id.purpose',
                                      list: '$proof_of_id.list',
                                      staff_id: '$proof_of_id.ref_id',
                                    },
                                  },
                                ],
                                onFinish: {
                                  type: 'clientAction',
                                  action: 'navigate',
                                  payload: ['/field-ops/tasks'],
                                },
                                redirect: '/settings/personal/my-profile',
                              },
                            },
                            {
                              type: 'clientAction',
                              action: 'resetFields',
                              payload: {
                                fields: [
                                  {
                                    fieldName: 'proof_of_id',
                                    defaultValue: undefined,
                                  },
                                ],
                              },
                            },
                          ],
                          whenFalse: [
                            {
                              type: 'clientAction',
                              action: 'submitAsync',
                              payload: {
                                calls: [
                                  {
                                    key: 'update_proof_of_id',
                                    url: '{VITE_STAFF_SERVER}/api/v1/file_actions/upload_file',
                                    data: {
                                      file: '$proof_of_id.file',
                                      purpose: '$proof_of_id.purpose',
                                      list: '$proof_of_id.list',
                                      staff_id: '$proof_of_id.ref_id',
                                    },
                                  },
                                ],
                                onFinish: {
                                  type: 'clientAction',
                                  action: 'clearStore',
                                  payload: ['postData', 'filePostData'],
                                },
                                redirect: '/settings/profile/my-profile',
                              },
                            },
                            {
                              type: 'clientAction',
                              action: 'submitAndNavigate',
                              payload: [
                                {
                                  type: 'clientAction',
                                  action: 'submitAndNavigate',
                                  payload: [
                                    {
                                      full_name: '$formDataRaw.full_name',
                                      contact_number:
                                        '$formDataRaw.contact_number',
                                      email_address:
                                        '$formDataRaw.email_address',
                                      id_type: '$formDataRaw.id_type', // SA id or drivers license = 1, passport = 2
                                      id_passport: '$formDataRaw.id_passport',
                                      roles: '$formDataRaw.roles',
                                      staff_id: '$my_profile.sso_id',
                                    },
                                    {
                                      url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                      headers: {},
                                      redirect: '/field-ops/tasks',
                                    },
                                    {
                                      method: 'post',
                                      action: '/settings/personal/my-profile',
                                    },
                                  ],
                                },
                                {
                                  url: '{VITE_STAFF_SERVER}/api/v1/profile_actions/update_profile',
                                  headers: {},
                                  redirect: '/field-ops/tasks',
                                },
                                {
                                  method: 'post',
                                  action: '/settings/personal/my-profile',
                                },
                              ],
                            },
                            {
                              type: 'clientAction',
                              action: 'resetFields',
                              payload: {
                                fields: [
                                  {
                                    fieldName: 'proof_of_id',
                                    defaultValue: undefined,
                                  },
                                ],
                              },
                            },
                            {
                              type: 'clientAction',
                              action: 'clearStore',
                              payload: ['postData'],
                            },
                          ],
                        },
                      },
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
    },
    // end screen
  },
  formOriginalValues: {
    // Orignals for diffing to find changed fields (from clientDataObject to store)
    'my_profile.email_address': 'email_address',
    'my_profile.contact_number': 'contact_number',
    'my_profile.full_name': 'full_name',
    'my_profile.id_type': 'id_type',
    'my_profile.id_passport': 'id_passport',
    'my_profile.roles': 'roles',
    derivedproof_of_id: 'proof_of_id',
  },
  formTransformMapper: {
    // Actual mapper to get final object shaped for server
    email_address: 'email_address',
    contact_number: 'contact_number',
    full_name: 'full_name',
    id_type: 'id_type',
    id_passport: 'id_passport',
    roles: 'roles',
    proof_of_id: 'proof_of_id',
  },
  actionPanels: [
    // Scratch Pad
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
  ],
} satisfies StateConfig;
// #endregion
