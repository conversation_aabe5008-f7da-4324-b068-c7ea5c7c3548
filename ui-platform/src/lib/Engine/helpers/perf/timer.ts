export interface MeasureResult {
  label: string;
  iterations: number;
  totalMs: number;
  avgMs: number;
  p95Ms?: number;
}

function nowMs(): number {
  if (typeof performance !== 'undefined' && performance.now)
    return performance.now();
  if (typeof process !== 'undefined' && process.hrtime) {
    const [s, ns] = process.hrtime();
    return s * 1_000 + ns / 1_000_000;
  }
  return Date.now();
}

export async function measureAsync(
  label: string,
  iterations: number,
  fn: (i: number) => Promise<void> | void
): Promise<MeasureResult> {
  const samples: number[] = [];
  const t0 = nowMs();
  for (let i = 0; i < iterations; i++) {
    const s0 = nowMs();
    // eslint-disable-next-line no-await-in-loop
    await fn(i);
    samples.push(nowMs() - s0);
  }
  const totalMs = nowMs() - t0;
  const avgMs = totalMs / iterations;
  const sorted = [...samples].sort((a, b) => a - b);
  const p95Ms = sorted[Math.floor(sorted.length * 0.95)] ?? undefined;
  return { label, iterations, totalMs, avgMs, p95Ms };
}

export function logMeasure(result: MeasureResult) {
  const { label, iterations, totalMs, avgMs, p95Ms } = result;
  // Use console.info to keep CI output light but visible
  // Consumers can grep for [perf] label
  console.info(
    `[perf] ${label}: iterations=${iterations}, totalMs=${totalMs.toFixed(
      2
    )}, avgMs=${avgMs.toFixed(4)}${p95Ms ? `, p95Ms=${p95Ms.toFixed(4)}` : ''}`
  );
}

export interface ReplicatedResult extends MeasureResult {
  replicates: number;
  meanAvgMs: number;
  stdevAvgMs: number;
}

function stdev(nums: number[]): number {
  if (!nums.length) return 0;
  const mean = nums.reduce((a, b) => a + b, 0) / nums.length;
  const variance =
    nums.reduce((acc, n) => acc + (n - mean) ** 2, 0) / nums.length;
  return Math.sqrt(variance);
}

export async function measureReplicates(
  label: string,
  replicates: number,
  iterations: number,
  fn: (i: number) => Promise<void> | void
): Promise<ReplicatedResult> {
  const results: MeasureResult[] = [];
  for (let r = 0; r < replicates; r++) {
    const res = await measureAsync(`${label}#${r + 1}`, iterations, fn);
    logMeasure(res);
    results.push(res);
  }
  const avgList = results.map((r) => r.avgMs);
  const meanAvgMs = avgList.reduce((a, b) => a + b, 0) / avgList.length;
  const stdevAvgMs = stdev(avgList);
  return {
    label,
    iterations,
    totalMs: results.reduce((a, b) => a + b.totalMs, 0),
    avgMs: meanAvgMs,
    p95Ms: undefined,
    replicates,
    meanAvgMs,
    stdevAvgMs,
  };
}
