export interface MemorySample {
  usedMB?: number;
  rssMB?: number;
}

export function sampleMemory(): MemorySample {
  try {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const mem = (performance as any).memory;
      return { usedMB: mem.usedJSHeapSize / 1024 / 1024 };
    }
    if (typeof process !== 'undefined' && (process as any).memoryUsage) {
      const mu = (process as any).memoryUsage();
      return { usedMB: mu.heapUsed / 1024 / 1024, rssMB: mu.rss / 1024 / 1024 };
    }
  } catch {
    // ignore
  }
  return {};
}

export function formatMemory(sample: MemorySample): string {
  const parts: string[] = [];
  if (sample.usedMB !== undefined) parts.push(`heap=${sample.usedMB.toFixed(2)}MB`);
  if (sample.rssMB !== undefined) parts.push(`rss=${sample.rssMB.toFixed(2)}MB`);
  return parts.join(', ');
}

