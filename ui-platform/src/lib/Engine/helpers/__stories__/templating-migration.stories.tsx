import { renderTemplateWithJS } from 'js-in-strings';
import React, { useMemo, useState } from 'react';
import { QueryEngine } from '../QueryEngine';
import { renderTemplate, renderTemplateObject } from '../render-template';
import { templateFunctions } from '../render-template-functions';
import {
  registerTemplateFunctions,
  templateFunctionRegistry,
} from '../template-function-registry';

export default {
  title: 'Engine/Templating/Migration Guide',
  parameters: {
    docs: {
      description: {
        component:
          'Migration guide demonstrating the unified templating system: #{}, js:, q:, ${}, and the Template Function Registry with namespacing.',
      },
    },
  },
};

const storeBase = {
  user: { name: 'Alice', age: 30 },
  order: { amount: 100, vat: 0.15 },
  company: '4SURE',
  list: [1, 2, 3],
};

export const Overview = () => {
  const [store] = useState(storeBase);
  const [template, setTemplate] = useState(
    'Hello #{user.name}! Total: {order.amount}'
  );

  const fns = useMemo(() => templateFunctions(store, {}), [store]);
  const processed = useMemo(() => {
    // Preprocess #{} -> {} for display using renderTemplate
    const pre = template.replace(/#\{([^}]*)\}/g, '{$1}');
    return renderTemplate(pre, store, fns);
  }, [template, store, fns]);

  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h2>Unified Templating Overview</h2>
      <p>
        This story illustrates the unified templating system and the Template
        Function Registry. Use the tabs to see specific contexts.
      </p>

      <label>Template: </label>
      <input
        style={{ width: 480 }}
        value={template}
        onChange={(e) => setTemplate(e.target.value)}
      />
      <pre>Output: {String(processed)}</pre>
      <p>
        Examples: Hello #{'{user.name}'}! Total: {'{order.amount}'}
      </p>
    </div>
  );
};

export const TemplateFunctionRegistry = () => {
  const [store] = useState(storeBase);
  useMemo(() => {
    templateFunctionRegistry.clear();
    registerTemplateFunctions(
      { calc: (v: number, r: number) => v * r },
      { namespace: 'myApp', override: true }
    );
  }, []);
  const fns = useMemo(() => templateFunctions(store, {}), [store]);

  const t1 = '{myApp.calc(order.amount, order.vat)}';
  const js = 'js:{myApp.calc(order.amount, order.vat)}';
  const qe = new QueryEngine();
  const q = qe.query(store, 'order.amount[|myApp.calc(order.vat)]', {
    customFunctions: fns as any,
  });

  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h3>Registry with Namespacing</h3>
      <p>Register once, then use across all contexts.</p>
      <ul>
        <li>
          In renderTemplate: <code>{t1}</code> =&gt;{' '}
          {String(renderTemplate(t1, store, fns))}
        </li>
        <li>
          In renderTemplateWithJS: <code>{js}</code> =&gt;{' '}
          {String(
            renderTemplateWithJS(js, store, {
              contextExtensions: fns,
              sandbox: true,
              returnRawValues: true,
            })
          )}
        </li>
        <li>
          In QueryEngine (q:): <code>order.amount[|myApp.calc(vat)]</code> =&gt;{' '}
          {String(q[0]?.value)}
        </li>
      </ul>
    </div>
  );
};

export const Contexts = () => {
  const [store] = useState(storeBase);
  const fns = useMemo(() => templateFunctions(store, {}), [store]);

  // #{ } via preprocessing for renderTemplate
  const hashPre = 'Hello #{user.name}, VAT: #{order.vat}';
  const preprocessed = hashPre.replace(/#\{([^}]*)\}/g, '{$1}');

  // js:
  const js = 'js:{`Hey ${user.name}, lines: ${order.amount}`}' as any;
  const jsOut = renderTemplateWithJS(js, store, {
    contextExtensions: fns,
    sandbox: true,
    returnRawValues: true,
  });

  // q:
  const qe = new QueryEngine();
  const qRes = qe.query(store, 'order.amount');

  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h3>Contexts</h3>
      <p>
        #{'{}'} (preprocessed for renderTemplate):{' '}
        {String(renderTemplate(preprocessed, store, fns))}
      </p>
      <p>js:: {String(jsOut)}</p>
      <p>q:: {String(qRes[0]?.value)}</p>
      <p>
        ${'{}'}: Supported in other processing paths that treat $-prefixed store
        lookups (e.g., evaluateStringExpression)
      </p>
    </div>
  );
};

export const BeforeAfter = () => {
  const [store] = useState(storeBase);
  useMemo(() => {
    templateFunctionRegistry.clear();
    // Before: ad-hoc functions, no namespacing – shown as plain inline fns
  }, []);
  const fnsBefore = {};

  useMemo(() => {
    registerTemplateFunctions(
      { calc: (v: number, r: number) => v * r },
      { namespace: 'ns', override: true }
    );
  }, []);
  const fnsAfter = useMemo(() => templateFunctions(store, {}), [store]);

  const beforeTpl = '{calc(order.amount, 0.2)}';
  const afterTpl = '{ns.calc(order.amount, 0.2)}';

  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h3>Before vs After</h3>
      <div style={{ display: 'flex', gap: 24 }}>
        <div>
          <h4>Before</h4>
          <p>Inline/unscoped functions (example)</p>
          <p>
            Template: <code>{beforeTpl}</code>
          </p>
          <p>Output: {String(renderTemplate(beforeTpl, store, fnsBefore))}</p>
        </div>
        <div>
          <h4>After</h4>
          <p>Registered, namespaced functions via registry</p>
          <p>
            Template: <code>{afterTpl}</code>
          </p>
          <p>Output: {String(renderTemplate(afterTpl, store, fnsAfter))}</p>
        </div>
      </div>
    </div>
  );
};

export const PerformanceSummary = () => {
  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h3>Performance Improvements</h3>
      <p>
        The new templating system centralizes function lookup and enables
        consistent usage across renderTemplate, renderTemplateObject,
        renderTemplateWithJS, and q: (QueryEngine) paths.
      </p>
      <p>
        Run perf tests locally and paste summary numbers here for your project.
        Look for labels prefixed with [perf] in test output, such as{' '}
        <code>[perf] renderTemplate/basic</code> and{' '}
        <code>[perf] LoaderV2</code> runs.
      </p>
    </div>
  );
};
