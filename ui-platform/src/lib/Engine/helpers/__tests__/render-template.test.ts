import { describe, expect, mock, test } from 'bun:test';
import { renderTemplate, renderTemplateObject } from '../render-template';
const random = mock(() => Math.random());

describe('renderTemplate', () => {
  // Simple variable substitution
  test('simple variable substitution', () => {
    const template = 'Hello {name}!';
    const data = { name: 'World' };
    expect(renderTemplate(template, data)).toBe('Hello World!');
  });

  test('multiple variable substitutions', () => {
    const template = '{greeting} {name}! How is the {time}?';
    const data = { greeting: 'Hello', name: 'Alice', time: 'morning' };
    expect(renderTemplate(template, data)).toBe(
      'Hello Alice! How is the morning?'
    );
  });

  // Nested object access
  test('nested object access', () => {
    const template = '{user.profile.name} works at {user.company.name}';
    const data = {
      user: {
        profile: { name: '<PERSON>' },
        company: { name: 'Tech Corp' },
      },
    };
    expect(renderTemplate(template, data)).toBe('<PERSON> works at Tech Corp');
  });

  // Method calls on objects
  test('method calls on objects', () => {
    const template = '{user.getName()} - {array.join(",")}';
    const data = {
      user: {
        getName: () => 'Charlie',
      },
      array: {
        join: (separator: string) => ['a', 'b', 'c'].join(separator),
      },
    };
    expect(renderTemplate(template, data)).toBe('Charlie - a,b,c');
  });

  // Custom function calls
  test('custom function calls', () => {
    const template = '{uppercase(name)} - {multiply(num, 2)}';
    const data = { name: 'dave', num: 5 };
    const functions = {
      uppercase: (str: string) => str.toUpperCase(),
      multiply: (a: number, b: number) => a * b,
    };
    expect(renderTemplate(template, data, functions)).toBe('DAVE - 10');
  });

  // Nested function calls
  test('nested function calls', () => {
    const template = '{format(user.getName(), uppercase(type))}';
    const data = {
      user: {
        getName: () => 'Eve',
      },
      type: 'admin',
    };
    const functions = {
      format: (name: string, type: string) => `${name} (${type})`,
      uppercase: (str: string) => str.toUpperCase(),
    };
    expect(renderTemplate(template, data, functions)).toBe('Eve (ADMIN)');
  });

  // Complex nested method calls
  test('complex nested method calls', () => {
    const template =
      '{data.process(user.getName(), math.sum(1, math.multiply(2, 3)))}';
    const data = {
      user: {
        getName: () => 'Frank',
      },
      math: {
        sum: (a: number, b: number) => a + b,
        multiply: (a: number, b: number) => a * b,
      },
      data: {
        process: (name: string, num: number) => `${name}: ${num}`,
      },
    };
    expect(renderTemplate(template, data)).toBe('Frank: 7');
  });

  // Edge cases
  test('undefined values', () => {
    const template = 'Hello {user.name}! {user.age}';
    const data = { user: { name: 'Grace' } };
    expect(renderTemplate(template, data)).toBe('Hello Grace! {user.age}');
  });

  test('empty template', () => {
    expect(renderTemplate('', {})).toBe('');
  });

  test('template with no substitutions', () => {
    const template = 'Hello World!';
    expect(renderTemplate(template, {})).toBe('Hello World!');
  });

  test('undefined function', () => {
    const template = '{nonexistent(value)}';
    const data = { value: 'test' };
    expect(renderTemplate(template, data)).toBe('{nonexistent(value)}');
  });

  test('nested parentheses in function calls', () => {
    const template = '{format(concat(a, b), wrap(value))}';
    const data = { a: 'Hello', b: 'World', value: 'test' };
    const functions = {
      format: (str: string, wrapped: string) => `${str} - ${wrapped}`,
      concat: (a: string, b: string) => `${a} ${b}`,
      wrap: (str: string) => `[${str}]`,
    };
    expect(renderTemplate(template, data, functions)).toBe(
      'Hello World - [test]'
    );
  });

  test('function with no arguments', () => {
    const template = '{getCurrentDate()}';
    const functions = {
      getCurrentDate: () => '2025-02-07',
    };
    expect(renderTemplate(template, {}, functions)).toBe('2025-02-07');
  });

  // Default value tests
  test('default values for undefined variables', () => {
    const template = 'Hello {name|Anonymous}!';
    const data = {};
    expect(renderTemplate(template, data)).toBe('Hello Anonymous!');
  });

  test('default values with different types', () => {
    const template = '{count|0} items, {isActive|true}, {price|9.99}';
    const data = {};
    expect(renderTemplate(template, data)).toBe('0 items, true, 9.99');
  });

  test('default values are not used when value exists', () => {
    const template = '{name|Anonymous} has {count|0} items';
    const data = { name: 'John', count: 5 };
    expect(renderTemplate(template, data)).toBe('John has 5 items');
  });

  test('default values in nested paths', () => {
    const template = '{user.name|Guest} from {user.location.city|Unknown City}';
    const data = { user: { name: 'Alice' } };
    expect(renderTemplate(template, data)).toBe('Alice from Unknown City');
  });

  // test('default values with function calls', () => {
  //   const template = '{uppercase(name|anonymous)}';
  //   const data = {};
  //   const functions = {
  //     uppercase: (str: string) => str.toUpperCase()
  //   };
  //   expect(renderTemplate(template, data, functions)).toBe('ANONYMOUS');
  // });

  test('default values preserve string literals', () => {
    const template =
      '{name|"true"} - {age|"0"} - {status|"null"} - {flag|true}';
    const data = {};
    expect(renderTemplate(template, data)).toBe('true - 0 - null - true');
  });

  // test('escaped curly braces', () => {
  //   const template = 'Value: {value}, Literal: {{literal}}';
  //   const data = { value: 'test' };
  //   expect(renderTemplate(template, data)).toBe('Value: test, Literal: {{literal}}');
  // });
  test('return value from multiple optional object query chaining using fallback logic for default value', () => {
    const template = '{name|account.a.username|account.profile.name|"anon"}';
    const dataSample = {
      name: 'Jalome',
      account: {
        username: 'Felix28',
        profile: {
          status: 'When yolo meets sigma',
          name: 'FelixLikesStuff',
          followers: 99,
        },
        professions: [
          {
            title: 'Software Engineer',
            level: 'Senior',
            yearsExperience: 3,
            professionalName: 'PhD. Felix',
            companyName: 'Felix Ltd.',
          },
        ],
      },
    };
    let data: any = { account: dataSample.account };
    expect(renderTemplate(template, data)).toBe('FelixLikesStuff');
    data = { account: {}, name: 'Jalome' };
    expect(renderTemplate(template, data)).toBe('Jalome');
    data = {
      account: { a: { username: dataSample.account.username } },
    };
    expect(renderTemplate(template, data)).toBe('Felix28');
  });

  // Raw value return tests
  test('return raw number for single expression', () => {
    const template = '{person.age}';
    const data = { person: { age: 25 } };
    const result = renderTemplate(template, data);
    expect(result).toBe(25);
    expect(typeof result).toBe('number');
  });

  test('return raw boolean for single expression', () => {
    const template = '{user.isActive}';
    const data = { user: { isActive: true } };
    const result = renderTemplate(template, data);
    expect(result).toBe(true);
    expect(typeof result).toBe('boolean');
  });

  test('return raw array for single expression', () => {
    const template = '{user.roles}';
    const data = { user: { roles: ['admin', 'user'] } };
    const result = renderTemplate(template, data);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['admin', 'user']);
  });

  test('return raw array from function for single expression', () => {
    const template = '{transform(user.attendance)}';
    const data = { user: { attendance: [1, 0, 1, 1] } };
    const functions = {
      transform: (arr: number[]) =>
        arr.map((n) => (n === 1 ? 'present' : 'absent')),
    };
    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['present', 'absent', 'present', 'present']);
  });

  test('return raw object for single expression', () => {
    const template = '{user.profile}';
    const data = { user: { profile: { name: 'John', age: 30 } } };
    const result = renderTemplate(template, data);
    expect(typeof result).toBe('object');
    expect(result).toEqual({ name: 'John', age: 30 });
  });

  test('convert to string for multiple expressions', () => {
    const template = 'Age: {person.age}, Active: {user.isActive}';
    const data = { person: { age: 25 }, user: { isActive: true } };
    const result = renderTemplate(template, data);
    expect(typeof result).toBe('string');
    expect(result).toBe('Age: 25, Active: true');
  });

  test('type casting with :number', () => {
    const template = '{stringNumber:number}';
    const data = { stringNumber: '42' };
    const result = renderTemplate(template, data);
    expect(result).toBe(42);
    expect(typeof result).toBe('number');
  });

  test('type casting with :boolean', () => {
    const template = '{stringBool:boolean}';
    const data = { stringBool: 'true' };
    const result = renderTemplate(template, data);
    expect(result).toBe(true);
    expect(typeof result).toBe('boolean');
  });

  test('type casting with :array', () => {
    const template = '{commaSeparated:array}';
    const data = { commaSeparated: 'a,b,c' };
    const result = renderTemplate(template, data);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['a', 'b', 'c']);
  });

  test('type casting with :json', () => {
    const template = '{jsonString:json}';
    const data = { jsonString: '{"name":"John","age":30}' };
    const result = renderTemplate(template, data);
    expect(typeof result).toBe('object');
    expect(result).toEqual({ name: 'John', age: 30 });
  });

  test('type casting with nested properties', () => {
    const template = '{user.age:number}';
    const data = { user: { age: '25' } };
    const result = renderTemplate(template, data);
    expect(result).toBe(25);
    expect(typeof result).toBe('number');
  });

  test('type casting with function result', () => {
    const template = '{getNumber():number}';
    const functions = {
      getNumber: () => '123',
    };
    const result = renderTemplate(template, {}, functions);
    expect(result).toBe(123);
    expect(typeof result).toBe('number');
  });

  test('type casting with default values', () => {
    const template = '{missingValue|"42":number}';
    const data = {};
    const result = renderTemplate(template, data);
    expect(result).toBe(42);
    expect(typeof result).toBe('number');
  });

  test('type casting in multiple expressions is preserved in string', () => {
    const template = 'Count: {count:number}, Active: {active:boolean}';
    const data = { count: '10', active: 'true' };
    const result = renderTemplate(template, data);
    expect(typeof result).toBe('string');
    expect(result).toBe('Count: 10, Active: true');
  });

  describe('type casting', () => {
    test('should cast values to number', () => {
      expect(renderTemplate('{value:number}', { value: '42' })).toBe(42);
    });

    test('should cast values to boolean', () => {
      expect(renderTemplate('{value:boolean}', { value: 'true' })).toBe(true);
      expect(renderTemplate('{value:boolean}', { value: '1' })).toBe(true);
      expect(renderTemplate('{value:boolean}', { value: 'yes' })).toBe(true);
      expect(renderTemplate('{value:boolean}', { value: 'false' })).toBe(false);
    });

    test('should cast values to string', () => {
      expect(renderTemplate('{value:string}', { value: 42 })).toBe('42');
    });

    test('should cast values to array', () => {
      // String to array
      expect(renderTemplate('{value:array}', { value: 'a,b,c' })).toEqual([
        'a',
        'b',
        'c',
      ]);

      // Single value to array
      expect(renderTemplate('{value:array}', { value: 5 })).toEqual([5]);

      // Array remains array
      expect(renderTemplate('{value:array}', { value: [1, 2, 3] })).toEqual([
        1, 2, 3,
      ]);
    });

    test('should cast values to JSON', () => {
      expect(
        renderTemplate('{value:json}', { value: '{"name":"John"}' })
      ).toEqual({ name: 'John' });
    });

    test('should cast values to Date', () => {
      const date = new Date('2023-01-01');
      const result = renderTemplate('{value:date}', { value: '2023-01-01' });
      expect(result instanceof Date).toBe(true);
      expect(result.toISOString().startsWith('2023-01-01')).toBe(true);
    });

    test('should handle nested paths with type casting', () => {
      const data = { foo: { bar: 5 } };
      expect(renderTemplate('{foo.bar:array}', data)).toEqual([5]);
    });
  });
});

describe('renderTemplateObject', () => {
  test('simple object with template strings', () => {
    const templateObj = {
      name: 'Hello {user}!',
      greeting: 'Good {time} {user}',
    };
    const data = { user: 'Alice', time: 'morning' };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      name: 'Hello Alice!',
      greeting: 'Good morning Alice',
    });
  });

  test('nested object with template strings', () => {
    const templateObj = {
      user: {
        greeting: 'Hello {name}!',
        details: {
          info: 'Age: {age}, Role: {role}',
          contact: '{email}',
        },
      },
      summary: '{name} is a {role}',
    };
    const data = {
      name: 'Bob',
      age: 30,
      role: 'developer',
      email: '<EMAIL>',
    };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      user: {
        greeting: 'Hello Bob!',
        details: {
          info: 'Age: 30, Role: developer',
          contact: '<EMAIL>',
        },
      },
      summary: 'Bob is a developer',
    });
  });

  test('object with array of template strings', () => {
    const templateObj = {
      users: [
        'User {index}: {names.0}',
        'User {index}: {names.1}',
        'User {index}: {names.2}',
      ],
      summary: 'Total users: {names.length}',
    };
    const data = {
      names: ['Alice', 'Bob', 'Charlie'],
      index: 1,
    };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      users: ['User 1: Alice', 'User 1: Bob', 'User 1: Charlie'],
      summary: 'Total users: 3',
    });
  });

  test('object with mixed values', () => {
    const templateObj = {
      name: 'Project {name}',
      id: 123,
      active: true,
      tags: ['tag1', '{tag}', 'tag3'],
      nested: {
        description: 'Created by {author}',
        timestamp: null,
        config: {
          path: '/users/{userId}/profile',
        },
      },
    };
    const data = {
      name: 'Alpha',
      tag: 'important',
      author: 'admin',
      userId: 'abc123',
    };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      name: 'Project Alpha',
      id: 123,
      active: true,
      tags: ['tag1', 'important', 'tag3'],
      nested: {
        description: 'Created by admin',
        timestamp: null,
        config: {
          path: '/users/abc123/profile',
        },
      },
    });
  });

  test('object with function calls in templates', () => {
    const templateObj = {
      user: {
        name: '{formatName(firstName, lastName)}',
        role: '{uppercase(role)}',
        stats: {
          score: 'Score: {calculate(points, multiplier)}',
        },
      },
    };
    const data = {
      firstName: 'John',
      lastName: 'Doe',
      role: 'admin',
      points: 100,
      multiplier: 1.5,
    };
    const functions = {
      formatName: (first: string, last: string) => `${first} ${last}`,
      uppercase: (str: string) => str.toUpperCase(),
      calculate: (base: number, mult: number) => base * mult,
    };
    expect(renderTemplateObject(templateObj, data, functions)).toEqual({
      user: {
        name: 'John Doe',
        role: 'ADMIN',
        stats: {
          score: 'Score: 150',
        },
      },
    });
  });

  test('empty object and undefined/null values', () => {
    const templateObj = {
      empty: {},
      nullValue: null,
      undefinedValue: undefined,
      nested: {
        template: 'Value: {missing}',
        nullTemplate: null,
        emptyObject: {},
      },
    };
    const data = {};
    expect(renderTemplateObject(templateObj, data)).toEqual({
      empty: {},
      nullValue: null,
      undefinedValue: undefined,
      nested: {
        template: 'Value: {missing}',
        nullTemplate: null,
        emptyObject: {},
      },
    });
  });

  test('array with mixed types and nested objects', () => {
    const templateObj = {
      list: [
        '{name}',
        123,
        { nested: 'User: {name}' },
        ['{name}', '{role}'],
        null,
        undefined,
      ],
    };
    const data = { name: 'Alice', role: 'admin' };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      list: [
        'Alice',
        123,
        { nested: 'User: Alice' },
        ['Alice', 'admin'],
        null,
        undefined,
      ],
    });
  });

  test('object with nested method calls in array', () => {
    const templateObj = {
      results: [
        '{math.sum(values.0, values.1)}',
        '{math.multiply(values.1, 2)}',
        '{format(math.sum(values.0, math.multiply(values.1, 2)))}',
      ],
    };
    const data = {
      values: [10, 5],
      math: {
        sum: (a: number, b: number) => a + b,
        multiply: (a: number, b: number) => a * b,
      },
    };
    const functions = {
      format: (num: number) => `Result: ${num}`,
    };
    expect(renderTemplateObject(templateObj, data, functions)).toEqual({
      results: [15, 10, 'Result: 20'],
    });
  });

  test('object with Date and RegExp values', () => {
    const date = new Date('2025-01-01');
    const regex = /test/;
    const templateObj = {
      date: date,
      regex: regex,
      nested: {
        formattedDate: '{formatDate(date)}',
        rawDate: '{date}',
      },
    };
    const data = { date };
    const functions = {
      formatDate: (d: Date) => d.toISOString(),
    };
    expect(renderTemplateObject(templateObj, data, functions)).toEqual({
      date: date,
      regex: regex,
      nested: {
        formattedDate: date.toISOString(),
        rawDate: date,
      },
    });
  });

  test('object with circular reference handling', () => {
    const templateObj = {
      user: '{user.name}',
      nested: {
        circular: '{user.nested.circular}',
        self: '{self}',
      },
    };
    const data = {
      user: {
        name: 'Alice',
        nested: { circular: 'test' },
      },
      self: 'reference',
    };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      user: 'Alice',
      nested: {
        circular: 'test',
        self: 'reference',
      },
    });
  });

  // test('object with special characters in templates', () => {
  //   const templateObj = {
  //     special: 'Hello {user.name!}',
  //     escaped: 'Escaped \\{not.a.template\\}',
  //     mixed: '{user.name} has {user.points}% {status}!',
  //     symbols: '{{{{user.name}}}}',
  //     complex: '{format(user.name, "@", user.domain)}'
  //   };
  //   const data = {
  //     user: {
  //       name: 'test<user>',
  //       points: 99.9,
  //       domain: 'test.com'
  //     },
  //     status: 'active'
  //   };
  //   const functions = {
  //     format: (name: string, sep: string, domain: string) => `${name}${sep}${domain}`
  //   };
  //   expect(renderTemplateObject(templateObj, data, functions)).toEqual({
  //     special: 'Hello test<user>!',
  //     escaped: 'Escaped \\{not.a.template\\}',
  //     mixed: 'test<user> has 99.9% active!',
  //     symbols: '{{test<user>}}',
  //     complex: 'test<user>@test.com'
  //   });
  // });

  test('object with type casting in templates', () => {
    const templateObj = {
      count: '{stringCount:number}',
      active: '{stringActive:boolean}',
      items: '{commaSeparated:array}',
      nested: {
        config: '{jsonConfig:json}',
      },
    };
    const data = {
      stringCount: '42',
      stringActive: 'true',
      commaSeparated: 'a,b,c',
      jsonConfig: '{"debug":true,"timeout":30}',
    };
    expect(renderTemplateObject(templateObj, data)).toEqual({
      count: 42,
      active: true,
      items: ['a', 'b', 'c'],
      nested: {
        config: { debug: true, timeout: 30 },
      },
    });
  });
});

describe('Advanced type casting scenarios', () => {
  test('chained type casting with function calls', () => {
    const template = '{formatData(user.input):number}';
    const data = { user: { input: '42.5px' } };
    const functions = {
      formatData: (str: string) => str.replace(/[^0-9.]/g, ''),
    };
    const result = renderTemplate(template, data, functions);
    expect(result).toBe(42.5);
    expect(typeof result).toBe('number');
  });

  test('type casting with array of objects', () => {
    const template = '{jsonArray:json}';
    const data = {
      jsonArray: '[{"id":1,"name":"Item 1"},{"id":2,"name":"Item 2"}]',
    };
    const result = renderTemplate(template, data);
    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBe(2);
    expect(result[0].id).toBe(1);
    expect(result[1].name).toBe('Item 2');
  });

  test('type casting with nested expressions', () => {
    const templateObj = {
      user: {
        stats: {
          score: '{calculateScore(user.rawScore):number}',
          lastLogin: '{user.lastLoginStr}',
          settings: '{user.settingsJson:json}',
        },
      },
    };

    const data = {
      user: {
        rawScore: '85.5',
        lastLoginStr: '2023-05-15T10:30:00Z',
        settingsJson: '{"theme":"dark","notifications":true}',
      },
    };

    const functions = {
      calculateScore: (score: string) => parseFloat(score) * 1.1,
    };

    const result = renderTemplateObject(templateObj, data, functions);

    expect(typeof result.user.stats.score).toBe('number');
    expect(result.user.stats.score).toBeCloseTo(94.05);
    expect(result.user.stats.settings.theme).toBe('dark');
    expect(result.user.stats.settings.notifications).toBe(true);
  });

  test('type casting with conditional expressions', () => {
    const template = '{conditional(isExpensive, "expensive", "cheap")}';
    const data = { isExpensive: true };
    const functions = {
      conditional: (condition: boolean, trueValue: any, falseValue: any) =>
        condition ? trueValue : falseValue,
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe('expensive');

    const falseTemplate = '{conditional(isCheap, "cheap", "expensive")}';
    const falseData = { isCheap: false };
    const falseResult = renderTemplate(falseTemplate, falseData, functions);
    expect(falseResult).toBe('expensive');
  });

  test('type casting with array transformation', () => {
    const template = '{mapToNumbers(commaSeparated)}';
    const data = { commaSeparated: '10,20,30' };
    const functions = {
      mapToNumbers: (str: string) => str.split(',').map(Number),
    };

    const result = renderTemplate(template, data, functions);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([10, 20, 30]);
    expect(typeof result[0]).toBe('number');
  });
});

describe('renderTemplateObject with complex type casting', () => {
  test('object with nested type casting and transformations', () => {
    // Create a very simple test case
    const templateObj = {
      name: '{userProfile:json}',
      average: '{calculateAverage(userScores):number}',
    };

    const data = {
      userProfile: '{"name":"Alice","age":28}',
      userScores: '75,82,90,68',
    };

    const functions = {
      calculateAverage: (scores: string) => {
        const nums = scores.split(',').map(Number);
        return nums.reduce((sum, num) => sum + num, 0) / nums.length;
      },
    };

    const result = renderTemplateObject(templateObj, data, functions);

    // Test each property individually
    expect(result.name).toEqual({ name: 'Alice', age: 28 });
    expect(typeof result.average).toBe('number');
    expect(result.average).toBeCloseTo(78.75);
  });

  test('object with array of templates with type casting', () => {
    const templateObj = {
      items: [
        {
          id: '{ids.0:number}',
          name: '{names.0}',
          active: '{statuses.0:boolean}',
        },
        {
          id: '{ids.1:number}',
          name: '{names.1}',
          active: '{statuses.1:boolean}',
        },
        {
          id: '{ids.2:number}',
          name: '{names.2}',
          active: '{statuses.2:boolean}',
        },
      ],
      summary: {
        count: '{items.length:number}',
        activeCount: '{countActive(statuses):number}',
      },
    };

    const data = {
      ids: ['1', '2', '3'],
      names: ['Item A', 'Item B', 'Item C'],
      statuses: ['true', 'false', 'true'],
      items: [1, 2, 3],
    };

    const functions = {
      countActive: (statuses: string[]) =>
        statuses.filter((status) => status === 'true').length,
    };

    const result = renderTemplateObject(templateObj, data, functions);

    expect(result.items.length).toBe(3);
    expect(typeof result.items[0].id).toBe('number');
    expect(result.items[0].id).toBe(1);
    expect(result.items[1].name).toBe('Item B');
    expect(typeof result.items[2].active).toBe('boolean');
    expect(result.items[2].active).toBe(true);
    expect(result.summary.count).toBe(3);
    expect(result.summary.activeCount).toBe(2);
  });
});

describe('Enhanced template features', () => {
  test('array operations - distinct', () => {
    const template = '{distinct(numbers)}';
    const data = { numbers: [1, 2, 2, 3, 3, 3, 4] };
    const functions = {
      distinct: (arr: any[]) => [...new Set(arr)],
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([1, 2, 3, 4]);
  });

  test('array operations - take and skip', () => {
    const template = '{take(numbers, 3)}';
    const data = { numbers: [1, 2, 3, 4, 5, 6] };
    const functions = {
      take: (arr: any[], count: number) => arr.slice(0, count),
      skip: (arr: any[], count: number) => arr.slice(count),
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toEqual([1, 2, 3]);

    const skipTemplate = '{skip(numbers, 4)}';
    const skipResult = renderTemplate(skipTemplate, data, functions);
    expect(skipResult).toEqual([5, 6]);
  });

  test('string operations - trim and replace', () => {
    const template = '{trim(text)}';
    const data = { text: '  hello world  ' };
    const functions = {
      trim: (str: string) => str.trim(),
      replace: (str: string, search: string, replacement: string) =>
        str.replace(new RegExp(search, 'g'), replacement),
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe('hello world');

    const replaceTemplate = '{replace(text, "world", "earth")}';
    const replaceResult = renderTemplate(replaceTemplate, data, functions);
    expect(replaceResult).toBe('  hello earth  ');
  });

  test('math operations', () => {
    const template = '{round(value)}';
    const data = { value: 3.75 };
    const functions = {
      round: (num: number) => Math.round(num),
      abs: (num: number) => Math.abs(num),
      pow: (num: number, exp: number) => Math.pow(num, exp),
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe(4);

    const powTemplate = '{pow(value, 2)}';
    const powResult = renderTemplate(powTemplate, data, functions);
    expect(powResult).toBeCloseTo(14.0625);
  });

  test('conditional operations', () => {
    const template = '{conditional(isExpensive, "expensive", "cheap")}';
    const data = { isExpensive: true };
    const functions = {
      conditional: (condition: boolean, trueValue: any, falseValue: any) =>
        condition ? trueValue : falseValue,
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe('expensive');

    const falseTemplate = '{conditional(isCheap, "cheap", "expensive")}';
    const falseData = { isCheap: false };
    const falseResult = renderTemplate(falseTemplate, falseData, functions);
    expect(falseResult).toBe('expensive');
  });

  test('grouping operations', () => {
    const template = '{groupBy(sales, "category")}';
    const data = {
      sales: [
        { category: 'A', amount: 100 },
        { category: 'B', amount: 200 },
        { category: 'A', amount: 300 },
        { category: 'B', amount: 400 },
      ],
    };
    const functions = {
      groupBy: (items: any[], key: string) => {
        const result: Record<string, any[]> = {};
        items.forEach((item) => {
          const groupKey = item[key];
          if (!result[groupKey]) {
            result[groupKey] = [];
          }
          result[groupKey].push(item);
        });
        return result;
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toHaveProperty('A');
    expect(result).toHaveProperty('B');
    expect(result.A).toHaveLength(2);
    expect(result.B).toHaveLength(2);
    expect(result.A[0].amount).toBe(100);
    expect(result.B[1].amount).toBe(400);
  });

  test('complex object template with multiple operations', () => {
    // Simplify the test to focus on the core functionality
    const templateObj = {
      summary: {
        items: '{getActiveItems(items)}',
        totalValue: '{calculateTotal(items):number}',
      },
      report: {
        highValueItems: '{getHighValueItems(items)}',
        formattedDate: '{formatDate(currentDate)}',
      },
    };

    const data = {
      items: [
        { id: 1, name: 'Item 1', quantity: 5, price: 10, category: 'A' },
        { id: 2, name: 'Item 2', quantity: 0, price: 20, category: 'B' },
        { id: 3, name: 'Item 3', quantity: 3, price: 15, category: 'A' },
      ],
      currentDate: new Date('2024-01-15'),
    };

    const functions = {
      getActiveItems: (items: any[]) =>
        items.filter((item) => item.quantity > 0),
      getHighValueItems: (items: any[]) =>
        items.filter((item) => item.price > 15),
      calculateTotal: (items: any[]) =>
        items.reduce((sum, item) => sum + item.quantity * item.price, 0),
      formatDate: (date: Date) => date.toISOString().split('T')[0],
    };

    const result = renderTemplateObject(templateObj, data, functions);

    // Test summary section
    expect(result.summary.items).toHaveLength(2);
    expect(result.summary.items[0].id).toBe(1);
    expect(typeof result.summary.totalValue).toBe('number');
    expect(result.summary.totalValue).toBe(5 * 10 + 3 * 15);

    // Test report section
    expect(result.report.highValueItems).toHaveLength(1);
    expect(result.report.highValueItems[0].name).toBe('Item 2');
    expect(result.report.formattedDate).toBe('2024-01-15');
  });
});

describe('Array operations', () => {
  test('array access by index', () => {
    const template = '{users[0].name}';
    const data = {
      users: [
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 30 },
        { name: 'Charlie', age: 35 },
      ],
    };

    const result = renderTemplate(template, data);
    expect(result).toBe('Alice');
  });

  test('array access with wildcard', () => {
    const template = '{getNames(users)}';
    const data = {
      users: [
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 30 },
        { name: 'Charlie', age: 35 },
      ],
    };
    const functions = {
      getNames: (users: any[]) => users.map((user) => user.name),
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['Alice', 'Bob', 'Charlie']);
  });

  test('array slicing', () => {
    const template = '{slice(strings, 1, 3)}';
    const data = { strings: ['hello', 'world', 'test', 'example'] };
    const functions = {
      slice: (arr: any[], start: number, end?: number) => arr.slice(start, end),
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['world', 'test']);

    const openEndedTemplate = '{slice(strings, 2)}';
    const openEndedResult = renderTemplate(openEndedTemplate, data, functions);
    expect(openEndedResult).toEqual(['test', 'example']);
  });

  test('array filtering', () => {
    const template = '{filter(users, "age", 30)}';
    const data = {
      users: [
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 30 },
        { name: 'Charlie', age: 35 },
      ],
    };
    const functions = {
      filter: (arr: any[], prop: string, minValue: number) =>
        arr.filter((item) => item[prop] >= minValue),
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('Bob');
    expect(result[1].name).toBe('Charlie');
  });

  test('array sorting', () => {
    const template = '{sort(users, "age", "desc")}';
    const data = {
      users: [
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 30 },
        { name: 'Charlie', age: 35 },
      ],
    };
    const functions = {
      sort: (arr: any[], prop: string, direction = 'asc') => {
        return [...arr].sort((a, b) => {
          if (direction.toLowerCase() === 'desc') {
            return b[prop] - a[prop];
          }
          return a[prop] - b[prop];
        });
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result.map((u) => u.name)).toEqual(['Charlie', 'Bob', 'Alice']);

    const ascTemplate = '{sort(users, "age")}';
    const ascResult = renderTemplate(ascTemplate, data, functions);
    expect(ascResult.map((u) => u.name)).toEqual(['Alice', 'Bob', 'Charlie']);
  });
});

describe('Advanced array operations', () => {
  test('array aggregation', () => {
    const template = '{sum(numbers)}';
    const data = { numbers: [1, 2, 3, 4, 5] };
    const functions = {
      sum: (arr: number[]) => arr.reduce((a, b) => a + b, 0),
      avg: (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length,
      count: (arr: any[]) => arr.length,
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe(15);

    const avgTemplate = '{avg(numbers)}';
    const avgResult = renderTemplate(avgTemplate, data, functions);
    expect(avgResult).toBe(3);

    const countTemplate = '{count(numbers)}';
    const countResult = renderTemplate(countTemplate, data, functions);
    expect(countResult).toBe(5);
  });

  test('array mapping', () => {
    const template = '{map(numbers, "double")}';
    const data = { numbers: [1, 2, 3, 4] };
    const functions = {
      map: (arr: any[], operation: string) => {
        switch (operation) {
          case 'double':
            return arr.map((n) => n * 2);
          case 'square':
            return arr.map((n) => n * n);
          default:
            return arr;
        }
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toEqual([2, 4, 6, 8]);

    const squareTemplate = '{map(numbers, "square")}';
    const squareResult = renderTemplate(squareTemplate, data, functions);
    expect(squareResult).toEqual([1, 4, 9, 16]);
  });

  test('array flattening', () => {
    const template = '{flatten(nestedArray)}';
    const data = {
      nestedArray: [
        [1, 2],
        [3, 4],
        [5, 6],
      ],
    };
    const functions = {
      flatten: (arr: any[][]) => arr.flat(),
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toEqual([1, 2, 3, 4, 5, 6]);
  });

  test('array combination operations', () => {
    const template = '{pipe(numbers, "filter", "map", "sum")}';
    const data = { numbers: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10] };
    const functions = {
      pipe: (arr: number[], ...operations: string[]) => {
        let result = [...arr];

        for (const op of operations) {
          switch (op) {
            case 'filter':
              result = result.filter((n) => n % 2 === 0); // Keep even numbers
              break;
            case 'map':
              result = result.map((n) => n * 2); // Double each number
              break;
            case 'sum':
              result = [result.reduce((a, b) => a + b, 0)]; // Sum all numbers
              break;
          }
        }

        return result.length === 1 ? result[0] : result;
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe(60); // (2+4+6+8+10) * 2 = 60
  });
});

describe('Advanced query features', () => {
  test('filtering with comparison operators', () => {
    const template = '{filterItems(users, "age", ">", 30)}';
    const data = {
      users: [
        { id: 1, name: 'Alice', age: 25 },
        { id: 2, name: 'Bob', age: 30 },
        { id: 3, name: 'Charlie', age: 35 },
        { id: 4, name: 'Dave', age: 40 },
      ],
    };
    const functions = {
      filterItems: (
        items: any[],
        prop: string,
        operator: string,
        value: any
      ) => {
        return items.filter((item) => {
          switch (operator) {
            case '>':
              return item[prop] > value;
            case '>=':
              return item[prop] >= value;
            case '<':
              return item[prop] < value;
            case '<=':
              return item[prop] <= value;
            case '==':
              return item[prop] == value;
            case '!=':
              return item[prop] != value;
            default:
              return true;
          }
        });
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toHaveLength(2);
    expect(result.map((u) => u.name)).toEqual(['Charlie', 'Dave']);

    // Test with equality
    const eqTemplate = '{filterItems(users, "age", "==", 30)}';
    const eqResult = renderTemplate(eqTemplate, data, functions);
    expect(eqResult).toHaveLength(1);
    expect(eqResult[0].name).toBe('Bob');
  });

  test('enhanced logical operators', () => {
    const template =
      '{complexFilter(users, "age", ">", 25, "AND", "country", "==", "USA")}';
    const data = {
      users: [
        { id: 1, name: 'Alice', age: 25, country: 'USA' },
        { id: 2, name: 'Bob', age: 30, country: 'Canada' },
        { id: 3, name: 'Charlie', age: 35, country: 'USA' },
        { id: 4, name: 'Dave', age: 40, country: 'UK' },
      ],
    };
    const functions = {
      complexFilter: (
        items: any[],
        prop1: string,
        op1: string,
        val1: any,
        logicOp: string,
        prop2: string,
        op2: string,
        val2: any
      ) => {
        return items.filter((item) => {
          let cond1, cond2;

          // Evaluate first condition
          switch (op1) {
            case '>':
              cond1 = item[prop1] > val1;
              break;
            case '>=':
              cond1 = item[prop1] >= val1;
              break;
            case '<':
              cond1 = item[prop1] < val1;
              break;
            case '<=':
              cond1 = item[prop1] <= val1;
              break;
            case '==':
              cond1 = item[prop1] == val1;
              break;
            case '!=':
              cond1 = item[prop1] != val1;
              break;
            default:
              cond1 = true;
          }

          // Evaluate second condition
          switch (op2) {
            case '>':
              cond2 = item[prop2] > val2;
              break;
            case '>=':
              cond2 = item[prop2] >= val2;
              break;
            case '<':
              cond2 = item[prop2] < val2;
              break;
            case '<=':
              cond2 = item[prop2] <= val2;
              break;
            case '==':
              cond2 = item[prop2] == val2;
              break;
            case '!=':
              cond2 = item[prop2] != val2;
              break;
            default:
              cond2 = true;
          }

          // Apply logical operator
          switch (logicOp.toUpperCase()) {
            case 'AND':
              return cond1 && cond2;
            case 'OR':
              return cond1 || cond2;
            case 'XOR':
              return (cond1 || cond2) && !(cond1 && cond2);
            case 'NOT':
              return !cond1;
            default:
              return cond1;
          }
        });
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Charlie');

    // Test with OR operator
    const orTemplate =
      '{complexFilter(users, "age", ">", 35, "OR", "country", "==", "Canada")}';
    const orResult = renderTemplate(orTemplate, data, functions);
    expect(orResult).toHaveLength(2);
    expect(orResult.map((u) => u.name)).toEqual(['Bob', 'Dave']);
  });

  test('path navigation with deep properties', () => {
    const template = '{getDeepValue(user, "address.city")}';
    const data = {
      user: {
        name: 'John Doe',
        address: {
          street: '123 Main St',
          city: 'New York',
          country: {
            name: 'USA',
            code: 'US',
          },
        },
      },
    };
    const functions = {
      getDeepValue: (obj: any, path: string) => {
        return path.split('.').reduce((acc, part) => {
          if (acc === undefined || acc === null) return undefined;
          return acc[part];
        }, obj);
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(result).toBe('New York');

    // Test with deeper path
    const deeperTemplate = '{getDeepValue(user, "address.country.name")}';
    const deeperResult = renderTemplate(deeperTemplate, data, functions);
    expect(deeperResult).toBe('USA');
  });

  test('combining filtering with path navigation', () => {
    const template =
      '{findAndExtract(items, "category", "electronics", "details.manufacturer")}';
    const data = {
      items: [
        {
          id: 1,
          name: 'Laptop',
          category: 'electronics',
          details: {
            manufacturer: 'Dell',
            model: 'XPS',
          },
        },
        {
          id: 2,
          name: 'Chair',
          category: 'furniture',
          details: {
            manufacturer: 'IKEA',
            material: 'wood',
          },
        },
        {
          id: 3,
          name: 'Phone',
          category: 'electronics',
          details: {
            manufacturer: 'Apple',
            model: 'iPhone',
          },
        },
      ],
    };
    const functions = {
      findAndExtract: (
        items: any[],
        filterProp: string,
        filterValue: any,
        extractPath: string
      ) => {
        const filtered = items.filter(
          (item) => item[filterProp] === filterValue
        );
        return filtered.map((item) => {
          return extractPath.split('.').reduce((acc, part) => {
            if (acc === undefined || acc === null) return undefined;
            return acc[part];
          }, item);
        });
      },
    };

    const result = renderTemplate(template, data, functions);
    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['Dell', 'Apple']);
  });
});

describe('renderTemplateObject with array type casting', () => {
  test('object with single value to array casting', () => {
    const templateObj = {
      user: {
        id: '{userId:number}',
        roles: '{role:array}',
        tags: '{tag:array}',
      },
      settings: {
        preferences: '{preference:array}',
      },
    };

    const data = {
      userId: '123',
      role: 'admin',
      tag: 5,
      preference: null,
    };

    const result = renderTemplateObject(templateObj, data);

    // Test that single values are properly cast to arrays
    expect(result.user.id).toBe(123);
    expect(Array.isArray(result.user.roles)).toBe(true);
    expect(result.user.roles).toEqual(['admin']);
    expect(Array.isArray(result.user.tags)).toBe(true);
    expect(result.user.tags).toEqual([5]);
    expect(Array.isArray(result.settings.preferences)).toBe(true);
    expect(result.settings.preferences).toEqual([null]);
  });

  test('object with mixed array casting types', () => {
    const templateObj = {
      items: [
        { value: '{number:array}' },
        { value: '{string:array}' },
        { value: '{boolean:array}' },
        { value: '{existingArray:array}' },
        { value: '{emptyString:array}' },
      ],
    };

    const data = {
      number: 42,
      string: 'hello',
      boolean: true,
      existingArray: [1, 2, 3],
      emptyString: '',
    };

    const result = renderTemplateObject(templateObj, data);

    expect(Array.isArray(result.items[0].value)).toBe(true);
    expect(result.items[0].value).toEqual([42]);

    expect(Array.isArray(result.items[1].value)).toBe(true);
    expect(result.items[1].value).toEqual(['hello']);

    expect(Array.isArray(result.items[2].value)).toBe(true);
    expect(result.items[2].value).toEqual([true]);

    expect(Array.isArray(result.items[3].value)).toBe(true);
    expect(result.items[3].value).toEqual([1, 2, 3]);

    expect(Array.isArray(result.items[4].value)).toBe(true);
    expect(result.items[4].value).toEqual(['']);
  });

  test('object with nested path array casting', () => {
    const templateObj = {
      data: {
        singleValue: '{nested.value:array}',
        nestedObject: '{nested.object:array}',
      },
    };

    const data = {
      nested: {
        value: 100,
        object: { id: 1, name: 'test' },
      },
    };

    const result = renderTemplateObject(templateObj, data);

    expect(Array.isArray(result.data.singleValue)).toBe(true);
    expect(result.data.singleValue).toEqual([100]);

    expect(Array.isArray(result.data.nestedObject)).toBe(true);
    expect(result.data.nestedObject).toEqual([{ id: 1, name: 'test' }]);
  });
});

describe('Array of templates', () => {
  test('simple array with one template', () => {
    const template = ['{formDataRow.data.id:number}'];
    const data = { formDataRow: { data: { id: '567' } } };

    const result = renderTemplate(template, data);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([567]);
  });

  test('simple object template with array with templates in object', () => {
    const templateObj = {
      payload: [
        {
          name: 'test',
          val: '{formDataRow.data.id:array}',
        },
      ],
    };
    const data = { formDataRow: { data: { id: 567 } } };

    const result = renderTemplateObject(templateObj, data);

    expect(result.payload[0].val).toEqual([567]);
  });

  test('simple array with one template casting to array type', () => {
    const template = '{formDataRow.data.id:array}';
    const data = { formDataRow: { data: { id: 567 } } };

    const result = renderTemplate(template, data);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([567]);
  });

  test('process array of templates', () => {
    const templates = [
      '{foo.bar:number}',
      '{foo.baz}',
      'Static text',
      '{foo.qux|"default"}',
    ];
    const data = { foo: { bar: '5', baz: true } };

    const result = renderTemplate(templates, data);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([5, true, 'Static text', 'default']);
  });

  test('process array of templates with functions', () => {
    const templates = [
      '{uppercase(foo.name)}',
      '{multiply(foo.value, 2):number}',
      '{conditional(foo.active, "Yes", "No")}',
    ];

    const data = {
      foo: {
        name: 'test',
        value: '10',
        active: true,
      },
    };

    const functions = {
      uppercase: (str: string) => str.toUpperCase(),
      multiply: (a: number, b: number) => a * b,
      conditional: (condition: boolean, trueVal: string, falseVal: string) =>
        condition ? trueVal : falseVal,
    };

    const result = renderTemplate(templates, data, functions);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual(['TEST', 20, 'Yes']);
  });
});

describe('Template objects with arrays', () => {
  test('object with array of template expressions', () => {
    const templateObj = {
      title: 'Report',
      items: [
        '{numbers[0]:number}',
        '{numbers[1]:number}',
        '{numbers[2]:number}',
      ],
      summary: 'Total: {sum(numbers):number}',
    };

    const data = {
      numbers: ['10', '20', '30'],
    };

    const functions = {
      sum: (arr: string[]) => arr.reduce((a, b) => Number(a) + Number(b), 0),
    };

    const result = renderTemplateObject(templateObj, data, functions);

    expect(result.title).toBe('Report');
    expect(Array.isArray(result.items)).toBe(true);
    expect(result.items).toEqual([10, 20, 30]);
    expect(result.summary).toBe('Total: 60');
  });

  test('nested arrays of templates', () => {
    const templateObj = {
      categories: [
        {
          name: 'Category A',
          values: ['{data.a[0]:number}', '{data.a[1]:number}'],
        },
        {
          name: 'Category B',
          values: ['{data.b[0]:number}', '{data.b[1]:number}'],
        },
      ],
    };

    const data = {
      data: {
        a: ['1', '2'],
        b: ['3', '4'],
      },
    };

    const result = renderTemplateObject(templateObj, data);

    expect(Array.isArray(result.categories)).toBe(true);
    expect(result.categories.length).toBe(2);
    expect(result.categories[0].name).toBe('Category A');
    expect(result.categories[0].values).toEqual([1, 2]);
    expect(result.categories[1].name).toBe('Category B');
    expect(result.categories[1].values).toEqual([3, 4]);
  });

  test('array of objects with template expressions', () => {
    const templateObj = {
      users: [
        {
          id: '{ids[0]:number}',
          name: '{names[0]}',
          active: '{statuses[0]:boolean}',
        },
        {
          id: '{ids[1]:number}',
          name: '{names[1]}',
          active: '{statuses[1]:boolean}',
        },
        {
          id: '{ids[2]:number}',
          name: '{names[2]}',
          active: '{statuses[2]:boolean}',
        },
      ],
      summary: {
        count: '{users.length:number}',
        activeCount: '{countActive(statuses):number}',
      },
    };

    const data = {
      ids: ['1', '2', '3'],
      names: ['Item A', 'Item B', 'Item C'],
      statuses: ['true', 'false', 'true'],
      users: [1, 2, 3],
    };

    const functions = {
      countActive: (statuses: string[]) =>
        statuses.filter((status) => status === 'true').length,
    };

    const result = renderTemplateObject(templateObj, data, functions);

    expect(result.users.length).toBe(3);
    expect(typeof result.users[0].id).toBe('number');
    expect(result.users[0].id).toBe(1);
    expect(result.users[1].name).toBe('Item B');
    expect(typeof result.users[2].active).toBe('boolean');
    expect(result.users[2].active).toBe(true);
    expect(result.summary.count).toBe(3);
    expect(result.summary.activeCount).toBe(2);
  });

  test('dynamic array generation with template expressions', () => {
    const templateObj = {
      dynamicArray: '{generateArray(count):array}',
      processedArray: '{processItems(items)}',
    };

    const data = {
      count: 3,
      items: [{ value: 10 }, { value: 20 }, { value: 30 }],
    };

    const functions = {
      generateArray: (count: number) =>
        Array.from({ length: count }, (_, i) => i + 1),
      processItems: (items: any[]) => items.map((item) => item.value * 2),
    };

    const result = renderTemplateObject(templateObj, data, functions);

    expect(Array.isArray(result.dynamicArray)).toBe(true);
    expect(result.dynamicArray).toEqual([1, 2, 3]);
    expect(Array.isArray(result.processedArray)).toBe(true);
    expect(result.processedArray).toEqual([20, 40, 60]);
  });
});

describe('Array type preservation', () => {
  test('array type should be preserved with :array', () => {
    const template = '{value:array}';
    const data = { value: 5 };

    const result = renderTemplate(template, data);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([5]);
    // Check that it's not an object with numeric keys
    expect(Object.keys(result)).toEqual(['0']);
    expect(result.length).toBe(1);
  });

  test('array template should preserve array type', () => {
    const template = ['{value}'];
    const data = { value: 5 };

    const result = renderTemplate(template, data);

    expect(Array.isArray(result)).toBe(true);
    expect(result).toEqual([5]);
    // Check that it's not an object with numeric keys
    expect(Object.keys(result)).toEqual(['0']);
    expect(result.length).toBe(1);
  });

  test('array in template object should preserve array type', () => {
    const templateObj = {
      values: ['{value}'],
    };
    const data = { value: 5 };

    const result = renderTemplateObject(templateObj, data);

    expect(Array.isArray(result.values)).toBe(true);
    expect(result.values).toEqual([5]);
    // Check that it's not an object with numeric keys
    expect(Object.keys(result.values)).toEqual(['0']);
    expect(result.values.length).toBe(1);
  });
});

describe('renderTemplateObject with spreading', () => {
  test('should spread object properties using $ key', () => {
    const template = {
      details: {
        name: 'Fred',
        $: '...{names}',
      },
    };

    const data = {
      names: {
        fname: 'Enoch',
        lname: 'Abu',
      },
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      details: {
        name: 'Fred',
        fname: 'Enoch',
        lname: 'Abu',
      },
    });
  });

  test('should not override existing properties when spreading', () => {
    const template = {
      details: {
        name: 'Fred',
        fname: 'Already Set',
        $: '...{names}',
      },
    };

    const data = {
      names: {
        fname: 'Enoch',
        lname: 'Abu',
      },
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      details: {
        name: 'Fred',
        fname: 'Already Set', // This should not be overridden
        lname: 'Abu',
      },
    });
  });

  test('should handle spreading arrays', () => {
    const template = {
      items: {
        first: 'Primary',
        $: '...{list}',
      },
    };

    const data = {
      list: ['apple', 'banana', 'cherry'],
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      items: {
        first: 'Primary',
        '0': 'apple',
        '1': 'banana',
        '2': 'cherry',
      },
    });
  });

  test('should handle nested spreading', () => {
    const template = {
      user: {
        id: 123,
        $: '...{userDetails}',
      },
    };

    const data = {
      userDetails: {
        profile: {
          $: '...{personalInfo}',
        },
      },
      personalInfo: {
        name: 'John',
        age: 30,
      },
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      user: {
        id: 123,
        profile: {
          name: 'John',
          age: 30,
        },
      },
    });
  });

  test('should handle multiple spread operations in the same object', () => {
    const template = {
      combined: {
        $: '...{firstSet}',
        $spread: '...{secondSet}',
      },
    };

    const data = {
      firstSet: {
        a: 1,
        b: 2,
      },
      secondSet: {
        c: 3,
        d: 4,
      },
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      combined: {
        a: 1,
        b: 2,
        c: 3,
        d: 4,
      },
    });
  });

  test('should spread into deeply nested object', () => {
    const template = {
      id: '{id}',
      details: {
        additional: {
          $: '...{details.additional}',
          feedback_data: 'This is some feedback data',
        },
      },
    };

    const data = {
      id: 'e784b2c1-7a7c-4883-bd6d-676154c3450d',
      onboarding_state: 2,
      details: {
        id: 'e784b2c1-7a7c-4883-bd6d-676154c3450d',
        name: 'Stellar Solutions',
        trading_as: 'Stellar',
        co_reg: '*************',
        company_type: 2,
        bbeee: 9,
        additional: {
          checklist: {
            company_tax_number_verified_valid: 'unchecked',
            company_vat_number_verified_valid: 'unchecked',
            company_bbbeee_level_verified_valid: 'unchecked',
            company_documentation_verified_valid: 'unchecked',
            director_id_documents_verified_valid: 'unchecked',
            company_banking_details_verified_valid: 'unchecked',
            company_registration_number_verified_valid: 'valid',
          },
          requester_id: 'c7631cbe-ce2b-4be2-a65f-555afb78a2d5',
          linkedAccount: {},
          linkedClients: [],
          subscriptionData: {
            subscriptionTCs: null,
            subscriptionConfirmation: true,
            subscriptionConfirmationTimeStamp:
              'Fri May 02 2025 12:26:39 GMT+0200 (South Africa Standard Time)',
          },
          field_ops_feedback:
            'Banking details not matching proof of account\nBBBEEE level does not match what is on company profile',
        },
        onboarding_state: 2,
      },
      after_hours: false,
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      id: 'e784b2c1-7a7c-4883-bd6d-676154c3450d',
      details: {
        additional: {
          checklist: {
            company_tax_number_verified_valid: 'unchecked',
            company_vat_number_verified_valid: 'unchecked',
            company_bbbeee_level_verified_valid: 'unchecked',
            company_documentation_verified_valid: 'unchecked',
            director_id_documents_verified_valid: 'unchecked',
            company_banking_details_verified_valid: 'unchecked',
            company_registration_number_verified_valid: 'valid',
          },
          requester_id: 'c7631cbe-ce2b-4be2-a65f-555afb78a2d5',
          linkedAccount: {},
          linkedClients: [],
          subscriptionData: {
            subscriptionTCs: null,
            subscriptionConfirmation: true,
            subscriptionConfirmationTimeStamp:
              'Fri May 02 2025 12:26:39 GMT+0200 (South Africa Standard Time)',
          },
          field_ops_feedback:
            'Banking details not matching proof of account\nBBBEEE level does not match what is on company profile',
          feedback_data: 'This is some feedback data',
        },
      },
    });
  });

  test('should handle non-existent paths gracefully', () => {
    const template = {
      details: {
        name: 'Fred',
        $: '...{nonExistentPath}',
      },
    };

    const data = {
      names: {
        fname: 'Enoch',
        lname: 'Abu',
      },
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      details: {
        name: 'Fred',
        // No additional properties should be added
      },
    });
  });

  test('should process spread values recursively', () => {
    const template = {
      details: {
        $: '...{user}',
      },
    };

    const data = {
      user: {
        name: '{firstName} {lastName}',
        role: '{role|guest}',
      },
      firstName: 'John',
      lastName: 'Doe',
      role: 'admin',
    };

    const result = renderTemplateObject(template, data);

    expect(result).toEqual({
      details: {
        name: 'John Doe',
        role: 'admin',
      },
    });
  });
});

// Additional tests for enhanced spreading functionality

test('should support $spread as an alternative to $ for clarity', () => {
  const template = {
    details: {
      name: 'Fred',
      $spread: '...{names}',
    },
  };

  const data = {
    names: {
      fname: 'Enoch',
      lname: 'Abu',
    },
  };

  const result = renderTemplateObject(template, data);

  expect(result).toEqual({
    details: {
      name: 'Fred',
      fname: 'Enoch',
      lname: 'Abu',
    },
  });
});

test('should handle multiple spread operators with different keys', () => {
  const template = {
    combined: {
      $: '...{firstSet}',
      $spread: '...{secondSet}',
    },
  };

  const data = {
    firstSet: {
      a: 1,
      b: 2,
    },
    secondSet: {
      c: 3,
      d: 4,
    },
  };

  const result = renderTemplateObject(template, data);

  expect(result).toEqual({
    combined: {
      a: 1,
      b: 2,
      c: 3,
      d: 4,
    },
  });
});

test('should process spread values with type casting', () => {
  const template = {
    stats: {
      $: '...{rawStats}',
    },
  };

  const data = {
    rawStats: {
      count: '42',
      active: 'true',
      scores: '10,20,30',
    },
  };

  const result = renderTemplateObject(template, data);

  // Values should be processed but not type-cast automatically
  expect(result).toEqual({
    stats: {
      count: '42',
      active: 'true',
      scores: '10,20,30',
    },
  });

  // Now test with explicit type casting
  const templateWithCasting = {
    stats: {
      $: '...{rawStats}',
      count: '{rawStats.count:number}',
      active: '{rawStats.active:boolean}',
      scores: '{rawStats.scores:array}',
    },
  };

  const resultWithCasting = renderTemplateObject(templateWithCasting, data);

  expect(resultWithCasting).toEqual({
    stats: {
      count: 42,
      active: true,
      scores: ['10', '20', '30'],
    },
  });
});

test('should handle complex nested spreading with arrays', () => {
  const template = {
    report: {
      $: '...{baseReport}',
      sections: [
        {
          title: 'Section 1',
          $: '...{section1}',
        },
        {
          title: 'Section 2',
          $: '...{section2}',
        },
      ],
    },
  };

  const data = {
    baseReport: {
      id: 'R-123',
      date: '2023-05-15',
    },
    section1: {
      items: ['Item 1A', 'Item 1B'],
      status: 'complete',
    },
    section2: {
      items: ['Item 2A', 'Item 2B'],
      status: 'pending',
    },
  };

  const result = renderTemplateObject(template, data);

  expect(result).toEqual({
    report: {
      id: 'R-123',
      date: '2023-05-15',
      sections: [
        {
          title: 'Section 1',
          items: ['Item 1A', 'Item 1B'],
          status: 'complete',
        },
        {
          title: 'Section 2',
          items: ['Item 2A', 'Item 2B'],
          status: 'pending',
        },
      ],
    },
  });
});
