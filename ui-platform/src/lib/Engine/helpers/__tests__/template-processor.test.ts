import { describe, expect, it } from 'vitest';
import {
  evalStringExpression,
  hasTemplatesRecursive,
  processTemplates,
  processUrlTemplateString,
} from '../../helpers/evaluateStringExpression';
import {
  DEFAULT_EXCLUDED_PROP_KEYS,
  processPropValueForRender,
  processPropsForRender,
} from '../template-processor';

// Minimal mock store
const store = {
  user: { name: 'Alice', age: 30 },
  sp_profile: { id: 123, details: { name: 'Co' }, onboarding_state: 5 },
  formDataRaw: { platform_status: 5 },
  sp_enums: { reasons: [{ id: 1 }, { id: 2 }] },
};

describe('processPropValueForRender - exclusions', () => {
  it('excludes root onClick/onChange/onDropdownSelectChange', () => {
    for (const key of DEFAULT_EXCLUDED_PROP_KEYS) {
      const value = [
        { action: 'submit', payload: { sp_id: '#{sp_profile.id}' } },
      ];
      const out = processPropValueForRender(key, value, store);
      expect(out).toBe(value);
    }
  });

  it('supports custom excludeKeys', () => {
    const value = [
      { action: 'submit', payload: { sp_id: '#{sp_profile.id}' } },
    ];
    const out = processPropValueForRender('onSubmit', value, store, {
      excludeKeys: ['onSubmit'],
    });
    expect(out).toBe(value);
  });
});

describe('processPropValueForRender - processing', () => {
  it('processes defaultValues always', () => {
    const dv = { platform_status: '$sp_profile.onboarding_state' };
    const out = processPropValueForRender('defaultValues', dv, store);
    expect(out.platform_status).toBe(5);
  });

  it('processes string templates: #{}, js:{}, $path, {template}', () => {
    const t1 = processPropValueForRender('title', '#{user.name}', store);
    expect(t1).toBe('Alice');

    const t2 = processPropValueForRender('calc', 'js:{user.age * 2}', store);
    expect(t2).toBe(60);

    const t3 = processPropValueForRender('path', '$sp_profile.id', store);
    expect(t3).toBe(123);

    const t4 = processPropValueForRender('mixed', 'ID: {sp_profile.id}', store);
    expect(t4).toBe('ID: 123');
  });

  it('processes nested objects/arrays recursively', () => {
    const obj = {
      a: '#{user.name}',
      b: ['js:{user.age}', '$sp_profile.details.name'],
    };
    const out = processPropValueForRender('data', obj, store);
    expect(out.a).toBe('Alice');
    expect(out.b[0]).toBe(30);
    expect(out.b[1]).toBe('Co');
  });

  it('respects nested exclusion for arrays of buttons with onClick', () => {
    const props = {
      items: [
        {
          label: 'A',
          onClick: [
            { action: 'submit', payload: { sp_id: '#{sp_profile.id}' } },
          ],
        },
      ],
    };
    const out = processPropValueForRender('items', props.items, store, {
      excludeKeys: ['onClick'],
    });
    // Should not evaluate sp_id at render; payload remains template
    expect(out[0].onClick[0].payload.sp_id).toBe('#{sp_profile.id}');
  });
});

describe('processPropsForRender', () => {
  it('iterates props and processes each according to rules', () => {
    const props = {
      title: '#{user.name}',
      defaultValues: { v: '$sp_profile.id' },
      onClick: [{ action: 'noop', payload: { v: '#{sp_profile.id}' } }],
    };
    const out = processPropsForRender(props, store);
    expect(out.title).toBe('Alice');
    expect(out.defaultValues.v).toBe(123);
    expect(out.onClick[0].payload.v).toBe('#{sp_profile.id}');
  });
});

describe('evaluateStringExpression helpers', () => {
  it('hasTemplatesRecursive edge cases', () => {
    expect(hasTemplatesRecursive(null as any)).toBe(false);
    expect(hasTemplatesRecursive(undefined as any)).toBe(false);
    expect(hasTemplatesRecursive('plain')).toBe(false);
    expect(hasTemplatesRecursive('#{x}')).toBe(true);
    expect(hasTemplatesRecursive('js:{1+1}')).toBe(true);
    expect(hasTemplatesRecursive('$x')).toBe(true);
    expect(hasTemplatesRecursive('id: {x}')).toBe(true);
  });

  it('evalStringExpression supports prefixes', () => {
    expect(evalStringExpression('#{user.name}', store)).toBe('Alice');
    expect(evalStringExpression('js:{user.age}', store)).toBe(30);
    expect(evalStringExpression('$sp_profile.id', store)).toBe(123);
    expect(evalStringExpression('Hello {user.name}', store)).toBe(
      'Hello Alice'
    );
  });

  it('processUrlTemplateString handles url patterns', () => {
    const url = '{api}/users?userId=$sp_profile.id&name=#{user.name}';
    const out = processUrlTemplateString(
      url as any,
      { ...store, api: 'https://x' } as any,
      { processBaseUrl: true }
    );
    expect(out).toContain('https://x/users');
    expect(out).toContain('userId=123');
    expect(out).toContain('name=Alice');
  });
});

// NOTE: Synthetic micro-benchmark (sanity check only)
// We just ensure centralized call does not blow up with arrays of mixed props.
describe('performance sanity: centralized vs ad-hoc', () => {
  it('processPropsForRender handles many props quickly', () => {
    const bigProps: Record<string, any> = {};
    for (let i = 0; i < 200; i++) {
      bigProps[`k${i}`] =
        i % 3 === 0
          ? '#{user.name}'
          : i % 3 === 1
          ? { v: '$sp_profile.id' }
          : [{ onClick: [{ payload: { id: '#{sp_profile.id}' } }] }];
    }
    const start = performance.now();
    const out = processPropsForRender(bigProps, store, {
      excludeKeys: ['onClick'],
    });
    const dur = performance.now() - start;
    expect(out.k0).toBe('Alice');
    expect(out.k1.v).toBe(123);
    // Execution time sanity: should be reasonable under 200ms in test env
    expect(dur).toBeLessThan(200);
  });
});
