import { describe, expect, it } from 'vitest';
import { QueryEngine } from '../QueryEngine';
import { templateFunctionRegistry } from '../template-function-registry';

const data = {
  order: { amount: 100 },
};

describe('QueryEngine with custom registry functions', () => {
  it('calls custom function via options.customFunctions', () => {
    const qe = new QueryEngine();
    const res = qe.query(data, 'order.amount[|applyRate(0.15)]', {
      customFunctions: {
        applyRate: (...args: unknown[]) => {
          if (
            args.length !== 2 ||
            typeof args[0] !== 'number' ||
            typeof args[1] !== 'number'
          ) {
            throw new Error('Invalid arguments for applyRate');
          }
          return Math.round((args[0] as number) * ((1 + args[1]) as number));
        },
      },
    });
    expect(res[0].value).toBe(115);
  });

  it('supports namespaced custom functions via registry when wired by caller', () => {
    templateFunctionRegistry.clear();
    templateFunctionRegistry.register(
      { calc: (v: number, r: number) => v * r },
      { namespace: 'myApp', override: true }
    );

    const qe = new QueryEngine();
    const funcs = templateFunctionRegistry.getAll();
    const res = qe.query(data, 'order.amount[|myApp.calc(0.2)]', {
      customFunctions: funcs,
    });
    expect(res[0].value).toBe(20);
  });

  it('gracefully ignores missing functions', () => {
    const qe = new QueryEngine();
    const res = qe.query(data, 'order.amount[|missing(1,2)]');
    // no transform applied; value unchanged
    expect(res[0].value).toBe(100);
  });
});
