import { render } from '@testing-library/react';
import React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { ClientDynamicScreenLoaderV1 } from '../../components/ClientDynamicScreenLoader/ClientDynamicScreenLoader';
import { ClientDynamicScreenLoaderV2 } from '../../components/ClientDynamicScreenLoader/ClientDynamicScreenLoaderV2';
import { formatMemory, sampleMemory } from '../perf/memory';
import { logMeasure, measureAsync } from '../perf/timer';

// Mock react-router-dom's data router navigation to avoid requiring a Data Router in perf tests
const navState: 'idle' | 'loading' = 'idle';
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>(
    'react-router-dom'
  );
  return { ...actual, useNavigation: () => ({ state: navState }) } as any;
});

const ITERS = Number(process.env.PERF_ITERS || 50);

// Minimal component to mount quickly
const Dummy: React.FC<any> = (p) =>
  React.createElement('div', { 'data-dummy': true }, String(p?.title ?? 'x'));

const componentMap = { Dummy } as any;
const baseFragment = (title: string) => ({
  component: 'Dummy',
  props: { title },
});

const buildConfig = (n: number) => ({
  fragments: Array.from({ length: n }, (_, i) => baseFragment(`T-${i}`)),
});

function mountV1(n: number) {
  render(
    React.createElement(ClientDynamicScreenLoaderV1 as any, {
      config: buildConfig(n),
      componentMap,
      callClientAction: () => {},
    })
  );
}

function mountV2(n: number) {
  render(
    React.createElement(ClientDynamicScreenLoaderV2 as any, {
      config: buildConfig(n),
      componentMap,
      callClientAction: () => {},
    })
  );
}

describe('Perf: ClientDynamicScreenLoader V1 vs V2', () => {
  it('mount performance comparison (small)', async () => {
    const res1 = await measureAsync('LoaderV1 mount x20', ITERS, () =>
      mountV1(20)
    );
    const res2 = await measureAsync('LoaderV2 mount x20', ITERS, () =>
      mountV2(20)
    );
    logMeasure(res1);
    logMeasure(res2);
    // not asserting speed strictly; just ensuring the benchmark runs
    expect(res1.iterations).toBe(ITERS);
    expect(res2.iterations).toBe(ITERS);
  });

  it('mount performance comparison (medium)', async () => {
    const res1 = await measureAsync(
      'LoaderV1 mount x60',
      Math.max(ITERS / 2, 25),
      () => mountV1(60)
    );
    const res2 = await measureAsync(
      'LoaderV2 mount x60',
      Math.max(ITERS / 2, 25),
      () => mountV2(60)
    );
    logMeasure(res1);
    logMeasure(res2);
    expect(res1.iterations).toBeGreaterThan(0);
    expect(res2.iterations).toBeGreaterThan(0);
  });

  it('Memory snapshot (best-effort)', () => {
    const mem = sampleMemory();
    console.info(`[perf] loader memory: ${formatMemory(mem)}`);
    expect(mem).toBeTypeOf('object');
  });
});
