import { QueryEngine } from '../QueryEngine';

describe('QueryEngine', () => {
  const uppercase = (value: string): string => value.toUpperCase();
  const double = (value: number): number => value * 2;
  const sum = (values: number[]): number => values.reduce((a, b) => a + b, 0);
  const avg = (values: number[]): number =>
    values.reduce((a, b) => a + b, 0) / values.length;
  const count = (values: unknown[]): number => values.length;
  let queryEngine: QueryEngine;
  let testData: unknown;

  beforeEach(() => {
    queryEngine = new QueryEngine();
    testData = {
      strings: ['hello', 'world', 'test'],
      numbers: [1, 2, 3, 4, 5],
      user: {
        name: '<PERSON>',
        age: 30,
        address: {
          street: '123 Main St',
          city: 'New York',
          country: {
            name: 'USA',
            code: 'US',
          },
        },
      },
      users: [
        { id: 1, name: '<PERSON>', age: 25, country: 'USA' },
        { id: 2, name: '<PERSON>', age: 30, country: 'Canada' },
        { id: 3, name: '<PERSON>', age: 35, country: 'UK' },
      ],
      items: [
        { id: 1, name: 'Item 1', quantity: 5, price: 10 },
        { id: 2, name: 'Item 2', quantity: 0, price: 20 },
        { id: 3, name: 'Item 3', quantity: 3, price: 15 },
      ],
    };
  });

  describe('Simple property access', () => {
    it('should access simple object properties', () => {
      const result = queryEngine.query(testData, 'user.name');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('John Doe');
      expect(result[0].path).toEqual(['user', 'name']);
    });

    it('should access nested object properties', () => {
      const result = queryEngine.query(testData, 'user.address.city');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('New York');
      expect(result[0].path).toEqual(['user', 'address', 'city']);
    });
  });

  describe('Array access', () => {
    it('should access array elements by index', () => {
      const result = queryEngine.query(testData, 'users[0].name');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('Alice');
      expect(result[0].path).toEqual(['users', '0', 'name']);
    });

    it('should handle wildcard array access', () => {
      const result = queryEngine.query(testData, 'users[*].name');
      expect(result).toHaveLength(3);
      expect(result.map((r) => r.value)).toEqual(['Alice', 'Bob', 'Charlie']);
    });
  });

  describe('Filtering', () => {
    it('should filter array elements based on numeric comparison', () => {
      const result = queryEngine.query(testData, 'users[age > 30].name');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('Charlie');
    });

    it('should filter array elements based on equality', () => {
      const result = queryEngine.query(testData, 'items[quantity == 0].name');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('Item 2');
    });

    it('should handle multiple conditions', () => {
      const result = queryEngine.query(testData, 'items[quantity > 0].price');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual([10, 15]);
    });
  });

  describe('Array slicing', () => {
    it('should handle basic array slicing', () => {
      const result = queryEngine.query(testData, 'strings[1:3]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual(['world', 'test']);
    });

    it('should handle open-ended slices', () => {
      const result = queryEngine.query(testData, 'strings[1:]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual(['world', 'test']);
      expect(result[0].path[result[0].path.length - 1]).toBe('1');
    });

    it('should handle slices from start', () => {
      const result = queryEngine.query(testData, 'strings[:2]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual(['hello', 'world']);
    });
  });

  describe('Enhanced logical operators', () => {
    it('should handle NOT operator', () => {
      const result = queryEngine.query(testData, 'users[NOT age > 30].name');
      expect(result.map((r) => r.value)).toEqual(['Alice', 'Bob']);
    });

    it('should handle parentheses grouping', () => {
      const result = queryEngine.query(
        testData,
        'users[(age > 25 AND country == "USA") OR country == "UK"].name'
      );
      expect(result.map((r) => r.value)).toEqual(['Charlie']);
    });

    it('should handle XOR operator', () => {
      const result = queryEngine.query(
        testData,
        'users[age > 30 XOR country == "USA"].name'
      );
      expect(result.map((r) => r.value)).toEqual(['Alice', 'Charlie']);
    });
  });

  describe('Type operations', () => {
    it('should check type of values', () => {
      const result = queryEngine.query(testData, 'items[quantity type number]');
      expect(result).toHaveLength(3);
    });

    it('should check if value is defined', () => {
      testData.users[0].email = undefined;
      const result = queryEngine.query(
        testData,
        'users[NOT email isDefined].name'
      );
      expect(result[0].value).toBe('Alice');
    });

    it('should check if value is null', () => {
      testData.users[1].email = null;
      const result = queryEngine.query(testData, 'users[email isNull].name');
      expect(result[0].value).toBe('Bob');
    });
  });

  describe('Path navigation', () => {
    it('should navigate to parent', () => {
      const result = queryEngine.query(
        testData,
        'user.address.country.code.^.name'
      );
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('USA');
    });

    it('should navigate to siblings', () => {
      const result = queryEngine.query(
        testData,
        'users[age == 30]~[age > 30].name'
      );
      expect(result[0].value).toBe('Charlie');
    });
  });

  describe('Enhanced string operations', () => {
    it('should split strings', () => {
      const result = queryEngine.query(testData, 'user.name[|split( )]');
      expect(result[0].value).toEqual(['John', 'Doe']);
    });

    it('should join arrays', () => {
      const result = queryEngine.query(testData, 'strings[|join(-)]');
      expect(result[0].value).toBe('hello-world-test');
    });

    it('should format strings', () => {
      testData.template = 'Hello {0}, welcome to {1}';
      const result = queryEngine.query(
        testData,
        'template[|format(John, Earth)]'
      );
      expect(result[0].value).toBe('Hello John, welcome to Earth');
    });
  });

  describe('Complex filtering', () => {
    it('should handle AND conditions', () => {
      const result = queryEngine.query(
        testData,
        'users[age >= 25 AND name startsWith "A"]'
      );
      expect(result).toHaveLength(1);
      expect((result[0].value as any).name).toBe('Alice');
    });

    it('should handle OR conditions', () => {
      const result = queryEngine.query(
        testData,
        'users[age > 30 OR name == Bob]'
      );
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value.name)).toEqual(['Bob', 'Charlie']);
    });

    it('should handle string operations', () => {
      const result = queryEngine.query(testData, 'users[name contains lie]');
      expect(result).toHaveLength(1);
      expect(result[0].value.name).toBe('Charlie');
    });
  });

  describe('Transformations', () => {
    it('should apply transformation functions', () => {
      const result = queryEngine.query(testData, 'strings[*][|uppercase]', {
        transformations: { uppercase },
      });
      expect(result).toHaveLength(3);
      expect(result.map((r) => r.value)).toEqual(['HELLO', 'WORLD', 'TEST']);
    });

    it('should chain transformations with other operations', () => {
      const result = queryEngine.query(
        testData,
        'items[quantity > 0].price[|double]',
        {
          transformations: { double },
        }
      );
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual([20, 30]);
    });
  });

  describe('Advanced string operations', () => {
    it('should support regex matching', () => {
      (testData as any).strings?.push('regex123', 'pattern');
      const result = queryEngine.query(testData, 'strings[regex(\\d+)]');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('regex123');
    });

    it('should support string length comparison', () => {
      const result = queryEngine.query(testData, 'strings[length == 5]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual(['hello', 'world']);
    });

    it('should support empty check', () => {
      // Set up test data: give one user some notes, leave others empty
      (testData as any).users[0].notes = 'Some notes';
      (testData as any).users[1].notes = undefined;
      (testData as any).users[2].notes = '';

      const result = queryEngine.query(testData, 'users[notes empty]');
      expect(result).toHaveLength(2); // users[1] and users[2] should match
    });
  });

  describe('Aggregations', () => {
    it('should support sum aggregation', () => {
      const result = queryEngine.query(testData, 'numbers[@sum]', {
        aggregations: { sum },
      });
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe(15);
    });

    it('should support average aggregation', () => {
      const result = queryEngine.query(testData, 'numbers[@avg]', {
        aggregations: { avg },
      });
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe(3);
    });

    it('should support count aggregation', () => {
      const result = queryEngine.query(testData, 'users[@count]', {
        aggregations: { count },
      });
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe(3);
    });
  });

  describe('Sorting', () => {
    it('should sort arrays by property', () => {
      const result = queryEngine.query(testData, 'users[sort(age,desc)].name');
      expect(result.map((r) => r.value)).toEqual(['Charlie', 'Bob', 'Alice']);
    });

    it('should sort arrays with default direction', () => {
      const result = queryEngine.query(testData, 'users[sort(name)].name', {
        defaultSortDirection: 'asc',
      });
      expect(result.map((r) => r.value)).toEqual(['Alice', 'Bob', 'Charlie']);
    });
  });

  describe('Path navigation', () => {
    it('should support parent navigation', () => {
      const result = queryEngine.query(
        testData,
        'user.address.country.code.^.name'
      );
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('USA');
    });

    it('should support root navigation', () => {
      const result = queryEngine.query(
        testData,
        'user.address.$root.users[0].name'
      );
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('Alice');
    });

    it('should support sibling navigation', () => {
      const result = queryEngine.query(
        testData,
        'user.address.country.code.~name'
      );
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('USA');
    });
  });

  describe('Case sensitivity', () => {
    it('should respect case sensitivity option', () => {
      const result = queryEngine.query(testData, 'users[name contains LIE]', {
        caseSensitive: false,
      });
      expect(result).toHaveLength(1);
      expect(result[0].value.name).toBe('Charlie');
    });

    it('should handle case sensitive queries', () => {
      const result = queryEngine.query(testData, 'users[name contains LIE]', {
        caseSensitive: true,
      });
      expect(result).toHaveLength(0);
    });
  });

  describe('Error handling', () => {
    it('should handle non-existent properties', () => {
      const result = queryEngine.query(testData, 'user.nonexistent');
      expect(result).toHaveLength(0);
    });

    it('should handle invalid array indices', () => {
      const result = queryEngine.query(testData, 'users[99].name');
      expect(result).toHaveLength(0);
    });

    it('should handle null data gracefully', () => {
      const result = queryEngine.query(null, 'user.name');
      expect(result).toHaveLength(0);
    });

    it('should handle undefined data gracefully', () => {
      const result = queryEngine.query(undefined, 'user.name');
      expect(result).toHaveLength(0);
    });

    it('should handle empty query string', () => {
      const result = queryEngine.query(testData, '');
      expect(result).toHaveLength(0);
    });

    it('should handle malformed query syntax', () => {
      const result = queryEngine.query(testData, 'user.[invalid].name');
      expect(result).toHaveLength(0);
    });
  });

  describe('Extended array operations', () => {
    beforeEach(() => {
      (testData as any).numbers = [1, 2, 2, 3, 3, 3, 4];
    });

    it('should handle distinct operation', () => {
      const result = queryEngine.query(testData, 'numbers[|distinct]');
      expect(result).toHaveLength(4);
      expect(result.map((r) => r.value)).toEqual([1, 2, 3, 4]);
    });

    it('should handle take operation', () => {
      const result = queryEngine.query(testData, 'numbers[|take(3)]');
      expect(result).toHaveLength(3);
      expect(result.map((r) => r.value)).toEqual([1, 2, 2]);
    });

    it('should handle skip operation', () => {
      const result = queryEngine.query(testData, 'numbers[|skip(5)]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual([3, 4]);
    });

    it('should handle reverse operation', () => {
      const result = queryEngine.query(testData, 'numbers[|reverse]');
      expect(result.map((r) => r.value)).toEqual([4, 3, 3, 3, 2, 2, 1]);
    });
  });

  describe('String manipulation', () => {
    beforeEach(() => {
      testData.strings = ['  hello  ', ' world ', 'test'];
    });

    it('should handle trim operation', () => {
      const result = queryEngine.query(testData, 'strings[|trim]');
      expect(result.map((r) => r.value)).toEqual(['hello', 'world', 'test']);
    });

    it('should handle substring operation', () => {
      const result = queryEngine.query(testData, 'strings[|substring(0,3)]');
      expect(result.map((r) => r.value)).toEqual(['  h', ' wo', 'tes']);
    });

    it('should handle replace operation', () => {
      testData.strings = ['hello world', 'world hello'];
      const result = queryEngine.query(
        testData,
        'strings[|replace(world,earth)]'
      );
      expect(result.map((r) => r.value)).toEqual([
        'hello earth',
        'earth hello',
      ]);
    });
  });

  describe('Math operations', () => {
    beforeEach(() => {
      testData.numbers = [1.6, -2.3, 3.7, -4.1];
    });

    it('should handle round operation', () => {
      const result = queryEngine.query(testData, 'numbers[|round]');
      expect(result.map((r) => r.value)).toEqual([2, -2, 4, -4]);
    });

    it('should handle abs operation', () => {
      const result = queryEngine.query(testData, 'numbers[|abs]');
      expect(result.map((r) => r.value)).toEqual([1.6, 2.3, 3.7, 4.1]);
    });

    it('should handle pow operation', () => {
      const result = queryEngine.query(testData, 'numbers[|pow(2)]');
      expect(result.map((r) => r.value)).toEqual(
        [2.56, 5.29, 13.69, 16.81].map((n) => Number(n.toFixed(2)))
      );
    });
  });

  describe('Date operations', () => {
    beforeEach(() => {
      testData.dates = ['2024-01-01', '2024-02-15', '2024-03-30'];
    });

    it('should handle date formatting', () => {
      const result = queryEngine.query(testData, 'dates[|format(YYYY-MM-DD)]');
      expect(result.map((r) => r.value)).toEqual([
        '2024-01-01',
        '2024-02-15',
        '2024-03-30',
      ]);
    });

    it('should handle date comparison', () => {
      const result = queryEngine.query(testData, 'dates[|after(2024-02-01)]');
      expect(result.map((r) => r.value)).toEqual(['2024-02-15', '2024-03-30']);
    });
  });

  describe('Grouping operations', () => {
    beforeEach(() => {
      testData.sales = [
        { category: 'A', amount: 100 },
        { category: 'B', amount: 200 },
        { category: 'A', amount: 300 },
        { category: 'B', amount: 400 },
      ];
    });

    it('should handle groupBy operation', () => {
      const result = queryEngine.query(testData, 'sales[|groupBy(category)]');
      expect(result).toHaveLength(2);
      const categoryA = result.find((r) => r.path[r.path.length - 1] === 'A');
      const categoryB = result.find((r) => r.path[r.path.length - 1] === 'B');
      expect(categoryA?.value).toHaveLength(2);
      expect(categoryB?.value).toHaveLength(2);
      expect(categoryA?.value.map((v) => v.amount)).toEqual([100, 300]);
      expect(categoryB?.value.map((v) => v.amount)).toEqual([200, 400]);
    });

    it('should handle having operation', () => {
      const result = queryEngine.query(
        testData,
        'sales[|groupBy(category)][having(amount > 500)]'
      );
      expect(result).toHaveLength(1);
      expect(result[0].path[result[0].path.length - 1]).toBe('B');
      expect(result[0].value.map((v) => v.amount)).toEqual([200, 400]);
    });
  });

  describe('Conditional operations', () => {
    it('should handle if operation', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15,expensive,cheap)]'
      );
      expect(result.map((r) => r.value)).toEqual([
        'cheap',
        'expensive',
        'cheap',
      ]);
    });
    it('should handle if operation with object values', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15, {status: expensive}, {status: cheap})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'cheap' },
        { status: 'expensive' },
        { status: 'cheap' },
      ]);
    });
    it('should handle if operation with array values', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15, [expensive], [cheap])]'
      );
      expect(result.map((r) => r.value)).toEqual([
        ['cheap'],
        ['expensive'],
        ['cheap'],
      ]);
    });
    it('should handle if operation with nested values', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15, {status: expensive, value: price}, {status: cheap, value: price})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'cheap', value: 10 },
        { status: 'expensive', value: 20 },
        { status: 'cheap', value: 15 },
      ]);
    });
    it('should handle if operation with complex values', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15, {status: expensive, value: price * 2}, {status: cheap, value: price / 2})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'cheap', value: 5 },
        { status: 'expensive', value: 40 },
        { status: 'cheap', value: 7.5 },
      ]);
    });
    it('should handle if operation with complex values and functions', () => {
      const result = queryEngine.query(
        testData,
        'items[*][|if(price > 15, {status: expensive, value: price | round}, {status: cheap, value: price | ceil})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'cheap', value: 10 },
        { status: 'expensive', value: 20 },
        { status: 'cheap', value: 15 },
      ]);
    });
    it('should handle if operation with complex values and functions and filters', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round}, {status: cheap, value: price | ceil})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'expensive', value: 20 },
        { status: 'cheap', value: 15 },
      ]);
    });
    it('should handle if operation with complex values and functions and filters and transformations', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round} [|toUpperCase], {status: cheap, value: price | ceil})]'
      );
      expect(result.map((r) => r.value)).toEqual([
        { status: 'EXPENSIVE', value: 20 },
        { status: 'cheap', value: 15 },
      ]);
    });
    it('should handle if operation with complex values and functions and filters and transformations and grouping', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round} [|toUpperCase], {status: cheap, value: price | ceil})][|groupBy(status)]'
      );
      expect(result.map((r) => r.value)).toEqual([
        [{ status: 'EXPENSIVE', value: 20 }],
        [{ status: 'cheap', value: 15 }],
      ]);
    });
    it('should handle if operation with complex values and functions and filters and transformations and grouping and having', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round} [|toUpperCase], {status: cheap, value: price | ceil})][|groupBy(status)][having(value > 10)]'
      );
      expect(result.map((r) => r.value)).toEqual([
        [{ status: 'EXPENSIVE', value: 20 }],
      ]);
    });
    it('should handle if operation with complex values and functions and filters and transformations and grouping and having and sibling', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round} [|toUpperCase], {status: cheap, value: price | ceil})][|groupBy(status)][having(value > 10)]~[0].value'
      );
      expect(result.map((r) => r.value)).toEqual([20]);
    });
    it('should handle if operation with complex values and functions and filters and transformations and grouping and having and sibling and filter', () => {
      const result = queryEngine.query(
        testData,
        'items[price > 10][|if(price > 15, {status: expensive, value: price | round} [|toUpperCase], {status: cheap, value: price | ceil})][|groupBy(status)][having(value > 10)]~[0].value[0]'
      );
      expect(result.map((r) => r.value)).toEqual(15);
    });
  });
  describe('Advanced filtering operations', () => {
    it('should handle string contains filter', () => {
      const result = queryEngine.query(testData, 'users[name contains lie]');
      expect(result).toHaveLength(1);
      expect((result[0].value as any).name).toBe('Charlie');
    });

    it('should handle string startsWith filter', () => {
      const result = queryEngine.query(testData, 'users[name startsWith A]');
      expect(result).toHaveLength(1);
      expect((result[0].value as any).name).toBe('Alice');
    });

    it('should handle string endsWith filter', () => {
      const result = queryEngine.query(testData, 'users[name endsWith e]');
      expect(result).toHaveLength(2);
      expect(result.map((r) => (r.value as any).name)).toEqual([
        'Alice',
        'Charlie',
      ]);
    });

    it('should handle type checking', () => {
      const result = queryEngine.query(testData, 'users[age type number]');
      expect(result).toHaveLength(3);
    });

    it('should handle isDefined check', () => {
      (testData as any).users[0].email = '<EMAIL>';
      const result = queryEngine.query(testData, 'users[email isDefined]');
      expect(result).toHaveLength(1);
      expect((result[0].value as any).name).toBe('Alice');
    });

    it('should handle isNull check', () => {
      (testData as any).users[1].email = null;
      const result = queryEngine.query(testData, 'users[email isNull]');
      expect(result).toHaveLength(1);
      expect((result[0].value as any).name).toBe('Bob');
    });
  });

  describe('Complex query combinations', () => {
    it('should handle chained property access with filtering', () => {
      // First, let's test the filter alone
      const filteredResult = queryEngine.query(testData, 'users[age >= 30]');
      expect(filteredResult).toHaveLength(2); // Bob and Charlie

      // Test accessing names of filtered users
      const result = queryEngine.query(testData, 'users[age >= 30].name');
      // The query should return the name properties of all filtered users
      expect(result).toHaveLength(2);
      expect(result.map((r) => r.value)).toEqual(['Bob', 'Charlie']);
    });

    it('should handle nested array access', () => {
      (testData as any).nested = {
        data: [
          { items: [{ value: 1 }, { value: 2 }] },
          { items: [{ value: 3 }, { value: 4 }] },
        ],
      };
      const result = queryEngine.query(
        testData,
        'nested.data[*].items[*].value'
      );
      expect(result).toHaveLength(4);
      expect(result.map((r) => r.value)).toEqual([1, 2, 3, 4]);
    });

    it('should handle multiple filters on same array', () => {
      // First filter: age > 25 (should get Bob and Charlie)
      const firstFilter = queryEngine.query(testData, 'users[age > 25]');
      expect(firstFilter).toHaveLength(2);

      // Test a simpler case: just filter by country
      const result = queryEngine.query(
        testData,
        'users[country == Canada].name'
      );
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('Bob');
    });
  });

  describe('Edge cases and boundary conditions', () => {
    it('should handle empty arrays', () => {
      (testData as any).emptyArray = [];
      const result = queryEngine.query(testData, 'emptyArray[*]');
      expect(result).toHaveLength(0);
    });

    it('should handle arrays with null/undefined elements', () => {
      (testData as any).mixedArray = [1, null, undefined, 'test'];
      const result = queryEngine.query(testData, 'mixedArray[*]');
      expect(result).toHaveLength(4);
      expect(result.map((r) => r.value)).toEqual([1, null, undefined, 'test']);
    });

    it('should handle deeply nested property access', () => {
      const result = queryEngine.query(testData, 'user.address.country.name');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('USA');
    });

    it('should handle numeric string indices', () => {
      const result = queryEngine.query(testData, 'strings[0]');
      expect(result).toHaveLength(1);
      expect(result[0].value).toBe('hello');
    });

    it('should handle negative array indices gracefully', () => {
      const result = queryEngine.query(testData, 'strings[-1]');
      expect(result).toHaveLength(0);
    });
  });
});
