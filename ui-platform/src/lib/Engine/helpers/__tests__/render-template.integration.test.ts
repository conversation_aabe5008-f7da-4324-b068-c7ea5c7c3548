import { renderTemplateWithJS } from 'js-in-strings';
import { beforeEach, describe, expect, it } from 'vitest';
import { renderTemplate, renderTemplateObject } from '../render-template';
import { templateFunctions } from '../render-template-functions';
import {
  registerTemplateFunctions,
  templateFunctionRegistry,
} from '../template-function-registry';

const store = {
  user: { name: 'Alice', age: 30 },
  order: { amount: 100 },
  list: [1, 2, 3],
};

const ctx = () => templateFunctions(store, {});

// Helper to simulate store-based #{...} preprocessing used by evaluateStringExpression
const preprocessHash = (s: string) => s.replace(/#\{([^}]*)\}/g, '{$1}');

beforeEach(() => {
  templateFunctionRegistry.clear();
});

describe('Template Function Registry integration across contexts', () => {
  it('renderTemplate: resolves namespaced registry functions via flat and nested access', () => {
    registerTemplateFunctions(
      { calc: (v: number, r: number) => v * r },
      {
        namespace: 'myApp',
        override: true,
      }
    );
    const fns = ctx();

    // Flat key access in renderTemplate (string function call)
    const t1 = '{myApp.calc(order.amount, 0.2)}';
    expect(renderTemplate(t1, store, fns)).toBe(20);

    // Dotted names are supported directly; parentheses-wrapped function names are not
    const t2 = '{myApp.calc(order.amount, 0.5)}';
    expect(renderTemplate(t2, store, fns)).toBe(50);
  });

  it('renderTemplateObject: applies registry functions within object/string fields', () => {
    registerTemplateFunctions(
      {
        greet: (name: string) => `Hello ${name}`,
        inc: (n: number) => n + 1,
      },
      { namespace: 'ns', override: true }
    );
    const fns = ctx();

    const template = {
      a: '{ns.greet(user.name)}',
      b: '{ns.inc(user.age)}',
      nested: {
        c: 'Age next year: {ns.inc(user.age)}',
      },
      arr: ['{ns.inc(list[0])}', '{ns.inc(list[1])}'],
    };

    const result = renderTemplateObject(template, store, fns) as any;
    expect(result.a).toBe('Hello Alice');
    expect(result.b).toBe(31);
    expect(result.nested.c).toBe('Age next year: 31');
    expect(result.arr).toEqual([2, 3]);
  });

  it('renderTemplateWithJS: exposes registry functions in contextExtensions', () => {
    registerTemplateFunctions(
      { calc: (v: number, r: number) => v * r },
      {
        namespace: 'myApp',
        override: true,
      }
    );
    const tpl = 'js:{myApp.calc(order.amount, 0.15)}';
    const result = renderTemplateWithJS(tpl.replace(/^js:/, ''), store, {
      contextExtensions: ctx(),
      sandbox: true,
      returnRawValues: true,
    });
    expect(result).toBe(15);
  });

  it('#{} store-based templates (preprocessed) work with registry functions using renderTemplate', () => {
    registerTemplateFunctions(
      { upper: (s: string) => String(s).toUpperCase() },
      {
        namespace: 'str',
        override: true,
      }
    );
    const t = preprocessHash('#{str.upper(user.name)}');
    expect(renderTemplate(t, store, ctx())).toBe('ALICE');
  });

  it('q: QueryEngine integration via registry customFunctions (smoke)', async () => {
    // This test ensures the registry map can be wired into QueryEngine by caller code
    // Detailed QueryEngine+registry tests live in queryengine-registry.integration.test.ts
    registerTemplateFunctions(
      { add: (v: number, x: number) => v + x },
      {
        namespace: 'm',
        override: true,
      }
    );
    // Simulate how evaluateStringExpression wires functions into QueryEngine
    const { QueryEngine } = await import('../QueryEngine');
    const qe = new QueryEngine();
    const res = qe.query(store, 'order.amount[|m.add(23)]', {
      customFunctions: ctx() as any,
    });
    expect(res[0].value).toBe(123);
  });
});
