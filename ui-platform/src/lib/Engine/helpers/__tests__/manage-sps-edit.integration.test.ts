import { describe, expect, it, vi } from 'vitest';
import { evaluateExpression } from '../../hooks/useClientActionsAsync/utils/expression-evaluation';
import { processPropsForRender } from '../template-processor';
import { MANAGE_SPS_EDIT_STATE as manageConfig } from './mock';

// Mock minimal store to satisfy many expressions
const store = {
  sp_profile: {
    id: 999,
    details: { name: 'ACME', onboarding_state: 5 },
    address: {},
    financial: {},
    companies: [],
    documents: [],
  },
  sp_enums: {
    reasons_panel: [],
    client_sub_active_states: [],
    reasons_onboarding: [],
    companies: [],
  },
  formDataRaw: {},
};

const getStoreState = () => store;

describe('MANAGE_SPS_EDIT integration (static checks)', () => {
  it('renders props without evaluating onClick handlers at render-time', () => {
    const screens = (manageConfig as any).screens || {};
    // Pick a dense screen e.g., status
    const fragments = screens.status?.fragments || [];

    // Simulate render-time processing for a representative fragment props
    for (const frag of fragments) {
      const processed = processPropsForRender(frag.props || {}, store);
      // If a fragment has onClick arrays (e.g., in nested buttons), ensure they are not evaluated
      if (processed?.onClick) {
        const anyPayload = processed.onClick?.[0]?.payload;
        if (anyPayload && typeof anyPayload === 'object') {
          // payload should retain templates like '#{sp_profile.id}' at render-time
          const containsTemplate = Object.values(anyPayload).some(
            (v) => typeof v === 'string' && v.includes('#{')
          );
          expect(containsTemplate).toBe(true);
        }
      }
    }
  });

  it('action-time evaluateExpression resolves templates correctly in representative actions', () => {
    // Pick sample payloads echoing lines ~2529-2531 and ~3056-3058 patterns:
    const payload1 = { sp_id: '#{sp_profile.id}' };
    const payload2 = {
      suspend_period: '#{formDataRaw.suspension_duration}',
      sp_id: '#{sp_profile.id}',
    };

    const evaluated1 = evaluateExpression(payload1, undefined, getStoreState);
    expect(evaluated1.sp_id).toBe(999);

    const evaluated2 = evaluateExpression(payload2, undefined, getStoreState);
    expect(evaluated2.sp_id).toBe(999);
    // formDataRaw.suspension_duration is undefined here -> stays as-is (by design)
    expect(evaluated2.suspend_period).toBe(
      '#{formDataRaw.suspension_duration}'
    );
  });
});

// Optional: performance probe — not asserting exact improvement, just ensuring not pathological
// Real performance measurement would require browser/runtime tooling; here we keep it simple.
describe('MANAGE_SPS_EDIT performance sanity', () => {
  it('processes many fragments props without excessive slowdowns', () => {
    const screens = (manageConfig as any).screens || {};
    const fragments = screens.status?.fragments || [];

    const start = performance.now();
    for (const frag of fragments) {
      processPropsForRender(frag.props || {}, store);
    }
    const dur = performance.now() - start;
    expect(dur).toBeLessThan(400); // sanity threshold
  });
});
