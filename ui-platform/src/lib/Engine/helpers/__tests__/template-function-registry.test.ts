
import { describe, it, expect, beforeEach } from 'vitest';
import { TemplateFunctionRegistry } from '../template-function-registry';

describe('TemplateFunctionRegistry', () => {
  let registry: TemplateFunctionRegistry;
  beforeEach(() => {
    registry = new TemplateFunctionRegistry();
  });

  it('registers functions', () => {
    registry.register({ a: (x: number) => x + 1 });
    const all = registry.getAll();
    expect(typeof all.a).toBe('function');
    expect(all.a(1)).toBe(2);
  });

  it('supports namespacing', () => {
    registry.register({ calc: (x: number, y: number) => x + y }, { namespace: 'ns' });
    const all = registry.getAll();
    expect(typeof all['ns.calc']).toBe('function');
    expect(all['ns.calc'](2, 3)).toBe(5);
  });

  it('prevents conflicts unless override is true', () => {
    registry.register({ f: () => 1 });
    expect(() => registry.register({ f: () => 2 })).toThrow();
    registry.register({ f: () => 3 }, { override: true });
    expect(registry.getAll().f()).toBe(3);
  });

  it('registerFn works and can override', () => {
    registry.registerFn('g', (s: string) => s);
    expect(registry.getAll().g('x')).toBe('x');
    registry.registerFn('g', () => 'y', { override: true });
    expect(registry.getAll().g()).toBe('y');
  });

  it('clear empties registry', () => {
    registry.register({ a: () => 1 });
    registry.clear();
    expect(Object.keys(registry.getAll()).length).toBe(0);
  });
});

