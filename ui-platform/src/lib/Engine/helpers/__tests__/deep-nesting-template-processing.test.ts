import { describe, it, expect, vi } from 'vitest';
import { processPropsForRender } from '../template-processor';
import { evaluateExpression } from '../../hooks/useClientActionsAsync/utils/expression-evaluation';

const store = {
  sp_profile: { id: 42 },
  formDataRaw: {},
};

// Mock getStoreState for evaluateExpression
const getStoreState = () => store;

describe('Deep nesting template processing', () => {
  it('does not evaluate nested onClick payloads during render', () => {
    const props = {
      fragment: {
        modal: {
          footerButtons: [
            {
              label: 'Change Status',
              onClick: [
                {
                  action: 'submit',
                  payload: { sp_id: '#{sp_profile.id}' },
                },
              ],
            },
          ],
        },
      },
    };

    const out = processPropsForRender(props, store, { excludeKeys: ['onClick'] });

    // Ensure payload template remains untouched at render time
    const nested = out.fragment.modal.footerButtons[0];
    expect(nested.onClick[0].payload.sp_id).toBe('#{sp_profile.id}');
  });

  it('evaluates templates at action-time using evaluateExpression', () => {
    // Simulate clicking the button
    const payload = { sp_id: '#{sp_profile.id}' };
    const evaluated = evaluateExpression(payload, undefined, getStoreState);
    expect(evaluated.sp_id).toBe(42);
  });

  it('supports chained actions where modals have their own onClick handlers', () => {
    const chain = [
      {
        action: 'openModal',
        payload: {
          buttons: [
            {
              label: 'Confirm',
              onClick: [
                { action: 'submit', payload: { sp_id: '#{sp_profile.id}' } },
              ],
            },
          ],
        },
      },
    ];

    const out = processPropsForRender({ onClick: chain }, store);
    // Excluded at root level (default excludes include onClick) => untouched
    expect(out.onClick[0].payload.buttons[0].onClick[0].payload.sp_id).toBe(
      '#{sp_profile.id}'
    );

    // Action-time eval resolves it
    const evaluatedModalPayload = evaluateExpression(
      out.onClick[0].payload,
      undefined,
      getStoreState
    );
    // After evaluation, nested sp_id should resolve
    expect(
      evaluatedModalPayload.buttons[0].onClick[0].payload.sp_id
    ).toBe(42);
  });
});

