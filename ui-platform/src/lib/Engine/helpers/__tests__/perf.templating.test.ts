import { renderTemplateWithJS } from 'js-in-strings';
import { describe, expect, it } from 'vitest';
import { formatMemory, sampleMemory } from '../perf/memory';
import { logMeasure, measureAsync, measureReplicates } from '../perf/timer';
import { renderTemplate, renderTemplateObject } from '../render-template';
import { templateFunctions } from '../render-template-functions';
import {
  registerTemplateFunctions,
  templateFunctionRegistry,
} from '../template-function-registry';

// Keep iterations relatively small for CI while still meaningful
const ITERS = Number(process.env.PERF_ITERS || 200);
const REPS = Number(process.env.PERF_REPS || 3);

const store = {
  user: { name: 'Alice', age: 30 },
  order: {
    amount: 100,
    lines: Array.from({ length: 50 }, (_, i) => ({ id: i + 1, price: 10 + i })),
  },
  list: Array.from({ length: 200 }, (_, i) => i),
};

const ctx = () => templateFunctions(store, {});

function buildNestedTemplate(depth = 5, breadth = 5) {
  const obj: any = {};
  for (let d = 0; d < depth; d++) {
    const node: any = {};
    for (let b = 0; b < breadth; b++) {
      node[`k${b}`] = '{user.age}';
    }
    obj[`level_${d}`] = node;
  }
  return obj;
}

describe('Perf: templating engines', () => {
  it('renderTemplate basic variable substitution', async () => {
    const t = 'Hello {user.name}! {order.amount}';
    const res = await measureAsync('renderTemplate/basic', ITERS, () => {
      const out = renderTemplate(t, store, ctx());
      if (!out) throw new Error('no output');
    });
    logMeasure(res);
    expect(res.iterations).toBe(ITERS);
  });

  it('renderTemplate function calls and nested evaluation', async () => {
    templateFunctionRegistry.clear();
    registerTemplateFunctions(
      {
        up: (s: string) => s.toUpperCase(),
        add: (a: number, b: number) => a + b,
      },
      { namespace: 'ns', override: true }
    );
    const t = '{ns.up(user.name)} - {ns.add(order.amount, 23)}';
    const res = await measureAsync('renderTemplate/functions', ITERS, () => {
      const out = renderTemplate(t, store, ctx());
      if (!out) throw new Error('no output');
    });
    logMeasure(res);
    expect(res.iterations).toBe(ITERS);
  });

  it('renderTemplateObject large nested object', async () => {
    const tmpl = buildNestedTemplate(8, 8);
    const res = await measureAsync(
      'renderTemplateObject/nested',
      Math.max(ITERS / 4, 50),
      () => {
        const out = renderTemplateObject(tmpl, store, ctx());
        if (!out) throw new Error('no output');
      }
    );
    logMeasure(res);
    expect(res.iterations).toBeGreaterThan(0);
  });

  it('renderTemplateWithJS evaluation', async () => {
    templateFunctionRegistry.clear();
    registerTemplateFunctions(
      { calc: (v: number, r: number) => v * r },
      { namespace: 'myApp', override: true }
    );
    const js = 'js:{myApp.calc(order.amount, 0.18)}';
    const res = await measureAsync('renderTemplateWithJS/basic', ITERS, () => {
      const out = renderTemplateWithJS(js, store, {
        contextExtensions: ctx(),
        sandbox: true,
        returnRawValues: true,
      });
      if (out === undefined) throw new Error('no output');
    });
    logMeasure(res);
    expect(res.iterations).toBe(ITERS);
  });

  it('renderTemplate basic (replicates)', async () => {
    const res = await measureReplicates(
      'renderTemplate/basic/rep',
      REPS,
      Math.max(ITERS / 2, 50),
      () => {
        renderTemplate('Hello {user.name}!', store, ctx());
      }
    );
    console.info(
      `[perf] rep mean=${res.meanAvgMs.toFixed(
        4
      )}ms stdev=${res.stdevAvgMs.toFixed(4)}ms`
    );
    expect(res.replicates).toBeGreaterThan(0);
  });

  it('Memory snapshot (best-effort)', () => {
    const mem = sampleMemory();
    console.info(`[perf] memory: ${formatMemory(mem)}`);
    expect(mem).toBeTypeOf('object');
  });
});
