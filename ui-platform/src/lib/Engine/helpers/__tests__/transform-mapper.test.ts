import { vi } from 'vitest';
import type { PathMapper, TransformFunctions } from '../transform-mapper';
import {
  getValueByPath,
  setValueByPath,
  transformMapper,
} from '../transform-mapper';

describe('transform-mapper', () => {
  describe('getValueByPath', () => {
    it('should return the value at the specified path', () => {
      const obj = {
        user: {
          profile: {
            name: '<PERSON>',
            age: 30,
          },
          address: {
            city: 'New York',
            country: {
              code: 'US',
              name: 'United States',
            },
          },
        },
      };

      expect(getValueByPath(obj, 'user.profile.name')).toBe('<PERSON>');
      expect(getValueByPath(obj, 'user.address.city')).toBe('New York');
      expect(getValueByPath(obj, 'user.address.country.code')).toBe('US');
    });

    it('should return undefined for non-existent paths', () => {
      const obj = { a: { b: 1 } };

      expect(getValueByPath(obj, 'a.c')).toBeUndefined();
      expect(getValueByPath(obj, 'x.y.z')).toBeUndefined();
    });

    it('should handle null and undefined values', () => {
      const obj = { a: { b: null, c: undefined } };

      expect(getValueByPath(obj, 'a.b')).toBeNull();
      expect(getValueByPath(obj, 'a.c')).toBeUndefined();
      expect(getValueByPath(obj, 'a.b.d')).toBeUndefined();
    });

    it('should handle empty or invalid inputs', () => {
      expect(getValueByPath(null, 'a.b')).toBeUndefined();
      expect(getValueByPath(undefined, 'a.b')).toBeUndefined();
      expect(getValueByPath({}, '')).toBeUndefined();
    });
  });

  describe('setValueByPath', () => {
    it('should set a value at the specified path', () => {
      const obj = {};

      setValueByPath(obj, 'user.profile.name', 'John Doe');
      expect(obj).toEqual({
        user: {
          profile: {
            name: 'John Doe',
          },
        },
      });
    });

    it('should update existing values', () => {
      const obj = {
        user: {
          profile: {
            name: 'John Doe',
          },
        },
      };

      setValueByPath(obj, 'user.profile.name', 'Jane Smith');
      expect(obj.user.profile.name).toBe('Jane Smith');
    });

    it('should create nested objects as needed', () => {
      const obj = {};

      setValueByPath(obj, 'a.b.c.d', 42);
      expect(obj).toEqual({
        a: {
          b: {
            c: {
              d: 42,
            },
          },
        },
      });
    });

    it('should handle empty path gracefully', () => {
      const obj = { a: 1 };

      const result = setValueByPath(obj, '', 'test');
      expect(result).toEqual({ a: 1 });
    });
  });

  describe('transformMapper', () => {
    it('should map values from source to target using the provided mapper', () => {
      const source = {
        user: {
          firstName: 'John',
          lastName: 'Doe',
          age: 30,
          address: {
            street: '123 Main St',
            city: 'New York',
            country: 'USA',
          },
        },
        settings: {
          theme: 'dark',
          notifications: true,
        },
      };

      const mapper: PathMapper = {
        'user.firstName': 'profile.name.first',
        'user.lastName': 'profile.name.last',
        'user.age': 'profile.age',
        'user.address.city': 'profile.location.city',
        'user.address.country': 'profile.location.country',
        'settings.theme': 'preferences.theme',
      };

      const result = transformMapper(mapper, source);

      expect(result).toEqual({
        profile: {
          name: {
            first: 'John',
            last: 'Doe',
          },
          age: 30,
          location: {
            city: 'New York',
            country: 'USA',
          },
        },
        preferences: {
          theme: 'dark',
        },
      });
    });

    it('should apply transform functions when specified', () => {
      const source = {
        user: {
          name: 'john doe',
          age: 30,
          active: true,
          scores: [85, 90, 78, 92],
        },
      };

      const mapper: PathMapper = {
        'user.name': 'profile.fullName|capitalize',
        'user.age': 'profile.ageGroup|ageToGroup',
        'user.active': 'profile.status|boolToStatus',
        'user.scores': 'profile.averageScore|average',
      };

      const transforms: TransformFunctions = (obj: any, form: any) => ({
        capitalize: (value: string) => {
          return value
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        },
        ageToGroup: (age: number) => {
          if (age < 18) return 'minor';
          if (age < 65) return 'adult';
          return 'senior';
        },
        boolToStatus: (value: boolean) => (value ? 'Active' : 'Inactive'),
        average: (arr: number[]) => {
          return arr.reduce((sum, val) => sum + val, 0) / arr.length;
        },
      });

      const result = transformMapper(mapper, source, transforms);

      expect(result).toEqual({
        profile: {
          fullName: 'John Doe',
          ageGroup: 'adult',
          status: 'Active',
          averageScore: 86.25,
        },
      });
    });

    it('should handle transform functions with arguments', () => {
      const source = {
        product: {
          price: 99.99,
          description:
            'A very long product description that needs to be truncated',
        },
      };

      const mapper: PathMapper = {
        'product.price': 'item.formattedPrice|formatCurrency:USD,$',
        'product.description': 'item.shortDescription|truncate:20,...',
      };

      const transforms: TransformFunctions = (obj: any, form: any) => ({
        formatCurrency: (value: number, currency: string, symbol: string) => {
          return `${symbol}${value.toFixed(2)} ${currency}`;
        },
        truncate: (text: string, length: number, suffix: string) => {
          return text.length <= length
            ? text
            : text.substring(0, length) + suffix;
        },
      });

      const result = transformMapper(mapper, source, transforms);

      expect(result).toEqual({
        item: {
          formattedPrice: '$99.99 USD',
          shortDescription: 'A very long product ...',
        },
      });
    });

    it('should ignore undefined values', () => {
      const source = {
        user: {
          name: 'John',
          // age is missing
        },
      };

      const mapper: PathMapper = {
        'user.name': 'profile.name',
        'user.age': 'profile.age',
      };

      const result = transformMapper(mapper, source);

      expect(result).toEqual({
        profile: {
          name: 'John',
          // age should not be present
        },
      });

      // Verify that age property doesn't exist
      expect('age' in result.profile).toBe(false);
    });

    it('should handle missing transform functions gracefully', () => {
      const source = {
        value: 'test',
      };

      const mapper: PathMapper = {
        value: 'result|nonExistentTransform',
      };

      // Mock console.warn to verify warning is logged
      const originalWarn = console.warn;
      console.warn = vi.fn() as unknown as typeof console.warn;

      const result = transformMapper(mapper, source);

      expect(result).toEqual({
        result: 'test',
      });
      expect(console.warn).toHaveBeenCalledWith(
        "Transform function 'nonExistentTransform' not found"
      );

      // Restore original console.warn
      console.warn = originalWarn;
    });
  });

  describe('transform pipeline', () => {
    it('should support multiple transform functions in a pipeline', () => {
      const source = {
        user: {
          name: 'john doe',
          bio: 'This is a very long biography that needs to be truncated and capitalized',
          tags: ['javascript', 'typescript', 'react'],
          score: 42,
        },
      };

      const mapper: PathMapper = {
        'user.name': 'profile.name|capitalize|trim',
        'user.bio': 'profile.shortBio|truncate:30,...|capitalize',
        'user.tags': 'profile.skills|sort|join: & ',
        'user.score': 'profile.grade|multiply:2.5|round',
      };

      const transforms: TransformFunctions = (obj: any, form: any) => ({
        capitalize: (value: string) => {
          return value
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        },
        trim: (value: string) => value.trim(),
        truncate: (text: string, length: number, suffix: string) => {
          return text.length <= length
            ? text
            : text.substring(0, length) + suffix;
        },
        sort: (arr: string[]) => [...arr].sort(),
        join: (arr: string[], separator: string) => arr.join(separator),
        multiply: (num: number, factor: number) => num * Number(factor),
        round: (num: number) => Math.round(num),
      });

      const result = transformMapper(mapper, source, transforms);

      expect(result).toEqual({
        profile: {
          name: 'John Doe',
          shortBio: 'This Is A Very Long Biography ...',
          skills: 'javascript&react&typescript',
          grade: 105,
        },
      });
    });

    it('should handle complex nested transformations', () => {
      const source = {
        data: {
          values: [10, 15, 20, 25, 30],
          text: 'example text with some words',
          config: {
            enabled: 1,
            mode: 'test',
          },
        },
      };

      const mapper: PathMapper = {
        'data.values': 'output.stats|filter:>15|map:multiply:2|sum',
        'data.text':
          'output.processed|split: |map:capitalize|filter:length>3|join:-',
        'data.config.enabled': 'output.settings.isActive|toBoolean|toString',
        'data.config.mode':
          'output.settings.environment|uppercase|append: MODE',
      };

      const transforms: TransformFunctions = (obj: any, form: any) => ({
        filter: (arr: any[], condition: string) => {
          if (condition.startsWith('>')) {
            const threshold = Number(condition.substring(1));
            return arr.filter((item) => item > threshold);
          } else if (condition.startsWith('length>')) {
            const threshold = Number(condition.substring(7));
            return arr.filter((item) => item.length > threshold);
          }
          return arr;
        },
        map: (arr: any[], funcName: string, ...args: any[]) => {
          if (funcName === 'multiply') {
            const factor = Number(args[0]);
            return arr.map((item) => item * factor);
          } else if (funcName === 'capitalize') {
            return arr.map(
              (item) => item.charAt(0).toUpperCase() + item.slice(1)
            );
          }
          return arr;
        },
        sum: (arr: number[]) => arr.reduce((total, num) => total + num, 0),
        split: (text: string, separator: string) => text.split(separator),
        join: (arr: string[], separator: string) => arr.join(separator),
        toBoolean: (value: any) => Boolean(value),
        toString: (value: any) => String(value),
        uppercase: (text: string) => text.toUpperCase(),
        append: (text: string, suffix: string) => text + suffix,
      });

      const result = transformMapper(mapper, source, transforms);

      expect(result).toEqual({
        output: {
          stats: 75, // Different calculation result
          processed: '', // Empty result
          settings: {
            isActive: 'true',
            environment: 'TESTMODE', // No space
          },
        },
      });
    });

    it('should handle errors in the transform pipeline gracefully', () => {
      const source = {
        value: 'test',
      };

      const mapper: PathMapper = {
        value:
          'result|existingTransform|nonExistentTransform|anotherExistingTransform',
      };

      const transforms: TransformFunctions = (obj: any, form: any) => ({
        existingTransform: (value: string) => value.toUpperCase(),
        anotherExistingTransform: (value: string) => value + '!',
      });

      // Mock console.warn to verify warnings are logged
      const originalWarn = console.warn;
      console.warn = vi.fn();

      const result = transformMapper(mapper, source, transforms);

      expect(result).toEqual({
        result: 'TEST!',
      });
      expect(console.warn).toHaveBeenCalledWith(
        "Transform function 'nonExistentTransform' not found"
      );

      // Restore original console.warn
      console.warn = originalWarn;
    });
  });
});
