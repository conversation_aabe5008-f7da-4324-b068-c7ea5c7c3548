/**
 * Analyze a props/config object to discover required store root keys
 * by scanning for template expressions referencing $storeKey or #{storeKey.*} or {storeKey.*}.
 */
export function analyzeRequiredStoreKeys(input: unknown): Set<string> {
  const keys = new Set<string>();
  const visited = new WeakSet<object>();

  const addKey = (k: string) => {
    if (!k) return;
    // Avoid environment-like names or obvious non-store placeholders
    if (k.match(/^(VITE_|http|https)/)) return;
    keys.add(k);
  };

  const fromStorePath = (path: string) => {
    // Extract root before first dot or array bracket
    const m = path.match(/^([a-zA-Z0-9_]+)/);
    if (m) addKey(m[1]);
  };

  const scanString = (s: string) => {
    // $path or $a.b.c
    if (s.startsWith('$')) {
      fromStorePath(s.slice(1));
    }
    // #{expr}
    const hashMatches = s.match(/#\{([^}]+)\}/g) || [];
    for (const m of hashMatches) {
      const expr = m.slice(2, -1);
      const root = expr.split('.')[0].trim();
      if (root) addKey(root);
    }
    // {expr}
    // Scan for {…} but skip any preceded by '#' and ignore namespaced calls
    for (const m of s.matchAll(/\{([^}]+)\}/g)) {
      const idx = m.index ?? -1;
      // Exclude "#{…}" which is handled elsewhere
      if (idx > 0 && s[idx - 1] === '#') continue;
      const expr = (m[1] ?? '').trim();
      // Skip function calls like fn(...), ns.fn(...), ns.sub.fn(...)
      if (/^[A-Za-z_]\w*(?:\.[A-Za-z_]\w*)*\s*\(/.test(expr)) continue;
      const root = expr.split('.')[0].trim();
      if (root) addKey(root);
    }
  };

  const walk = (val: unknown) => {
    if (val == null) return;
    if (typeof val === 'string') return scanString(val);
    if (Array.isArray(val)) {
      if (visited.has(val as any)) return;
      visited.add(val as any);
      val.forEach(walk);
      visited.delete(val as any);
      return;
    }
    if (typeof val === 'object') {
      const obj = val as Record<string, unknown>;
      if (visited.has(obj)) return;
      visited.add(obj);
      Object.values(obj).forEach(walk);
      visited.delete(obj);
    }
  };

  walk(input);
  return keys;
}
