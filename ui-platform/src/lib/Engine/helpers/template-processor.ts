import {
  evalStringExpression,
  hasTemplatesRecursive,
  processTemplates,
} from './evaluateStringExpression';

/**
 * Event-like prop keys that should not be pre-processed during render.
 * Their payloads (templates) must be evaluated lazily at action-time.
 */
export const DEFAULT_EXCLUDED_PROP_KEYS = [
  'onChange',
  'onClick',
  'onDropdownSelectChange',
  'onSubmit',
  'onBlur',
  'onFocus',
  'onKeyDown',
  'onMouseEnter',
  'onMouseLeave',
] as const;

export type ExcludedPropKey = (typeof DEFAULT_EXCLUDED_PROP_KEYS)[number];

export interface ProcessPropOptions {
  debug?: boolean;
  /**
   * Additional prop keys to exclude from render-time template processing.
   * These are merged with DEFAULT_EXCLUDED_PROP_KEYS.
   */
  excludeKeys?: string[];
}

/**
 * Process a single prop value for render-time with centralized, consistent rules.
 * - Strings: evaluated via evalStringExpression
 * - Objects/arrays with templates: processed via processTemplates
 * - Event-like props (onClick/onChange/...): not processed at render-time
 * - Special-case defaultValues: always processed as an object
 */
export function processPropValueForRender(
  key: string,
  value: any,
  store: any,
  options?: ProcessPropOptions
): any {
  const debug = !!options?.debug;
  const excluded = new Set<string>([
    ...DEFAULT_EXCLUDED_PROP_KEYS,
    ...(options?.excludeKeys || []),
  ]);

  // Skip render-time processing for event-like root keys
  if (excluded.has(key)) {
    return value;
  }

  // Special-case: form default values often contain many templates
  if (key === 'defaultValues') {
    return processTemplates(value, store, { debug });
  }

  // Non-strings that are falsy or primitives
  if (value === null || value === undefined) return value;

  if (typeof value === 'string') {
    return evalStringExpression(value, store, { debug });
  }

  if (typeof value === 'object') {
    // Only process objects/arrays that actually contain templates
    if (hasTemplatesRecursive(value)) {
      return processTemplates(value, store, {
        debug,
        // Respect exclusion at any nesting (e.g., arrays of buttons[].onClick)
        excludeKeys: Array.from(excluded),
      });
    }
    return value;
  }

  // functions, numbers, booleans, etc.
  return value;
}

/**
 * Process an entire props object uniformly for render-time.
 * Note: this does NOT inject debug prop; caller can merge `debug` as needed.
 */
export function processPropsForRender(
  props: Record<string, any>,
  store: any,
  options?: ProcessPropOptions
): Record<string, any> {
  const entries = Object.entries(props || {});
  const out: Record<string, any> = {};
  for (const [key, value] of entries) {
    out[key] = processPropValueForRender(key, value, store, options);
  }
  return out;
}
