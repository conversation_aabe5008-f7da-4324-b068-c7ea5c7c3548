// external modules imports
import { renderTemplateWithJS } from 'js-in-strings';
import Keycloak from 'keycloak-js';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { FieldValues, useFormContext, UseFormReturn } from 'react-hook-form';
// interal module imports
import { InteractionBlocker, Loader } from '../../../Components';
import { getNestedProperty } from '../../helpers/client-utils';
import { parseAndExecuteQuery } from '../../helpers/query-utils';
import {
  renderTemplate,
  renderTemplateObject,
} from '../../helpers/render-template';
import { templateFunctions as renderTemplateFunctions } from '../../helpers/render-template-functions';
import {
  ActionConfig,
  ActionPanelConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../models';
import { ViewConfig } from '../../models/view.config';
import { useAppStore } from '../../useAppStore';
// import { QueryEngine } from '../helpers/QueryEngine';
import { logger } from '../../../Utilities';

interface Props {
  config: ScreenConfig | ActionPanelConfig | ViewConfig;
  fetcher?: any;
  submit?: any;
  navigation?: any;
  componentMap: any;
  actionData?: any;
  keycloak?: Keycloak;
  callClientAction: (config: ActionConfig) => void;
}
// const queryEngine = new QueryEngine();

function runFragmentCondition(
  conditionalFragment: ViewConfig['conditionalFragment'],
  store: any
) {
  const condition = conditionalFragment?.condition;
  // const fragments = conditionalFragment.fragments || [];
  let item;

  if (typeof condition?.item === 'string' && condition.item.startsWith('$')) {
    const storePath = condition.item.replace('$', '');
    item = getNestedProperty(store, storePath, []);
    console.log({ item });
  } else if (
    typeof condition?.item === 'string' &&
    condition.item.startsWith('q:')
  ) {
    // const [query, storePath] = condition.item.replace('q:', '');
    // item = queryEngine.query(store, storePath);
    // console.log({ item });
  } else {
    item = condition?.item;
  }

  switch (condition?.comparator) {
    case 'isEmpty': {
      return item ? !item.length : false;
    }

    default:
      return false;
  }
}

function extractFormDefaultValues(
  defaultValues: { [key: string]: any },
  store: any
) {
  const obj = Object.entries(defaultValues).reduce((acc, [key, value]) => {
    let val = '';

    if (typeof value === 'string' && value.startsWith('$')) {
      const storePath = value.replace('$', '');
      val = getNestedProperty(store, storePath, '');
    } else if (typeof value === 'string' && value.startsWith('q:')) {
      // const storePath = value.replace('q:', '');
      // val = queryEngine.query(store, storePath);
    } else if (typeof value === 'string' && value.startsWith('js:')) {
      val = renderTemplateWithJS(value.replace('js:', '').trim(), store, {
        returnRawValues: true,
        sandbox: true,
      });
    } else {
      val = value;
    }
    return {
      ...acc,
      [key]: val,
    };
  }, {});

  // Fallback on using templating to transform default values
  return renderTemplateObject(
    obj,
    store,
    renderTemplateFunctions(store, store?.formDataRaw)
  );
}

export function ClientDynamicScreenLoaderV1({
  config,
  navigation,
  componentMap,
  fetcher,
  submit,
  actionData,
  keycloak,
  callClientAction,
}: Props) {
  const store: any = useAppStore((state) => state);
  const formContext = useFormContext();
  let fragments: FragmentConfig[] = [];
  // const conditionalFragment = {condition:{item: '$claims', comparator: 'hasLength', value: 0}, fragments: []}

  if (config?.conditionalFragment) {
    const useThis = runFragmentCondition(config.conditionalFragment, store);

    if (useThis) {
      fragments = config.conditionalFragment.fragments;
    } else {
      fragments = config.fragments || [];
    }
  } else {
    fragments = config.fragments || [];
  }

  const evalStringExpression = (value: string) => {
    let val;
    if (value.startsWith('$')) {
      // Existing store path logic
      const [storePath, query] = value.replace('$', '').split('?');
      val = getNestedProperty(store, storePath, '');

      if (query && Array.isArray(val)) {
        try {
          val = parseAndExecuteQuery(val, query);
        } catch (error) {
          console.error(`Error executing query for ${storePath}:`, error);
        }
      }
    } else if (value.startsWith('q:')) {
      // const [query, storePath] = value.replace('q:', '').split('?');
      // val = queryEngine.query(store, storePath)
      // if (query && Array.isArray(val)) {
      //   try {
      //     val = parseAndExecuteQuery(val, query);
      //   } catch (error) {
      //     console.error(`Error executing query for ${storePath}:`, error);
      //     val = []; // Fallback to empty array on error
      //   }
      // }
    } else if (value.startsWith('#')) {
      // New template string processing with # prefix
      val = renderTemplate(
        value.substring(1),
        store,
        renderTemplateFunctions(store, store?.formDataRaw)
      );
    } else if (value.startsWith('js:')) {
      // New template string processing with js: prefix
      const template = value.substring(3);
      console.log('js template:', template, 'store: ', store);
      const result = renderTemplateWithJS(template, store, {
        returnRawValues: true,
        sandbox: true,
      });
      // Handling error output from evaluation
      if (typeof result === 'string' && result?.startsWith('[Error:')) {
        console.error('js template error:', template, result);
        val = value;
      } else {
        val = result;
      }
      console.log('js template result:', template, val);
    } else {
      val = value;
    }
    return val;
  };

  const evalConditionExpression = (value?: string | boolean) => {
    let result = false;
    const val = typeof value === 'string' ? evalStringExpression(value) : value;

    if (typeof val === 'boolean') {
      result = val;
    }
    return result;
  };

  const RenderedComp = useMemo(() => {
    return fragments?.map((cfg, i) => {
      logger.configure({
        prefix: `🔍[Client Dynamic Screen Loader] - Mounting ${cfg.component} Component:`,
        enabled: cfg?.debug,
        options: { style: { color: '#980a43' } },
      });
      const log = logger.log;
      log`${cfg}`;
      const Comp = componentMap[cfg.component] as any;

      const newProps = Object.entries(cfg.props).reduce(
        (acc, [key, value]) => {
          let val: any;

          if (key === 'defaultValues') {
            val = extractFormDefaultValues(value, store);
          } else if (typeof value === 'string') {
            val = evalStringExpression(value);
          } else if (value && typeof value === 'object') {
            // Helper function to check for templates recursively
            const hasTemplatesRecursive = (obj: any): boolean => {
              if (typeof obj === 'string') {
                return obj.startsWith('#') || obj.startsWith('js:');
              }
              if (Array.isArray(obj)) {
                return obj.some((item) => hasTemplatesRecursive(item));
              }
              if (obj && typeof obj === 'object') {
                return Object.values(obj).some((v) => hasTemplatesRecursive(v));
              }
              return false;
            };

            // Helper function to process templates recursively
            const processTemplates = (obj: any): any => {
              if (typeof obj === 'string') {
                return obj.startsWith('#')
                  ? renderTemplate(
                      obj.substring(1),
                      store,
                      renderTemplateFunctions(store, store?.formDataRaw)
                    )
                  : obj.startsWith('js:')
                  ? renderTemplateWithJS(obj.substring(3), store, {
                      returnRawValues: true,
                    })
                  : obj;
              }
              if (Array.isArray(obj)) {
                return obj.map((item) => processTemplates(item));
              }
              if (obj && typeof obj === 'object') {
                const result: any = {};
                for (const [k, v] of Object.entries(obj)) {
                  result[k] = processTemplates(v);
                }
                return result;
              }
              return obj;
            };

            if (hasTemplatesRecursive(value)) {
              val = processTemplates(value);
            } else {
              val = value;
            }
          } else {
            val = value;
          }
          return {
            ...acc,
            debug: cfg.props['debug'] || cfg.debug,
            [key]: val,
          };
        },
        {
          _actionData: actionData,
          _onNotify: store.setState,
          _config: config,
          _formContext: formContext,
          _submit: submit,
          _fetcher: fetcher,
          _navigation: navigation,
          _callClientAction: callClientAction,
          _store: store, // Expose store to fragments
          _keycloak: keycloak || store?.auth,
        }
      );
      log`Generated new props ${newProps}`;

      // console.log({newProps});
      if (cfg?.debounceMs) {
        return (
          <DebounceCompV1
            key={i}
            config={cfg}
            component={
              <ComponentHOC
                {...{
                  config: cfg,
                  idx: i,
                  props: newProps,
                  store: newProps._store,
                  Component: Comp,
                }}
              />
            }
          />
        );
      }
      if (Comp) {
        return (
          <div
            key={i}
            style={cfg.disableComponentConfig ? { position: 'relative' } : {}}
          >
            {cfg?.layout ? (
              <div style={cfg.layout || {}}>
                <Comp {...(newProps || {})} />
              </div>
            ) : (
              <Comp {...(newProps || {})} />
            )}
            {cfg.disableComponentConfig && (
              <InteractionBlocker
                disableCondition={evalConditionExpression(
                  cfg.disableComponentConfig.disableCondition
                )}
                label={
                  cfg.disableComponentConfig.noIconLabel
                    ? ''
                    : cfg.disableComponentConfig.label || 'read only'
                }
                labelSize={cfg.disableComponentConfig.labelSize}
                icon={
                  cfg.disableComponentConfig.noIconLabel
                    ? undefined
                    : cfg.disableComponentConfig.icon || {
                        type: 'lock-04',
                        size: 20,
                        strokeWidth: '1',
                        color: '#b28511',
                      }
                }
                style={cfg.disableComponentConfig.style || {}}
              />
            )}
          </div>
        );
      } else {
        console.warn(`Component "${cfg.component}" not found in componentMap`);
        return (
          <div key={i} style={{ backgroundColor: 'red', color: 'white' }}>
            Component "{cfg.component}" cannot be found!
          </div>
        );
      }
    });
  }, [fragments, store]);

  return <>{RenderedComp}</>;
}

interface DebounceCompProps {
  config: FragmentConfig;
  component: any;
}

interface NewProps {
  [key: string]: string | number | boolean | object;
  _actionData: any;
  _onNotify: any;
  _config: ScreenConfig | ActionPanelConfig | ViewConfig;
  _formContext: UseFormReturn<FieldValues, any, undefined>;
  _submit: (data: any) => void;
  _fetcher: (query: string) => Promise<any>;
  _navigation: any;
  _callClientAction: (config: ActionConfig) => void;
  _store: any;
  _keycloak: Keycloak;
}

interface RenderedCompProps {
  config: FragmentConfig;
  idx: number;
  props: NewProps;
  store: any;
  Component: any;
}

const evalStringExpression = (value: string, store: any) => {
  let val;
  if (value.startsWith('$')) {
    // Existing store path logic
    const [storePath, query] = value.replace('$', '').split('?');
    val = getNestedProperty(store, storePath, '');

    if (query && Array.isArray(val)) {
      try {
        val = parseAndExecuteQuery(val, query);
      } catch (error) {
        console.error(`Error executing query for ${storePath}:`, error);
      }
    }
  } else if (value.startsWith('q:')) {
    // const [query, storePath] = value.replace('q:', '').split('?');
    // val = queryEngine.query(store, storePath)
    // if (query && Array.isArray(val)) {
    //   try {
    //     val = parseAndExecuteQuery(val, query);
    //   } catch (error) {
    //     console.error(`Error executing query for ${storePath}:`, error);
    //     val = []; // Fallback to empty array on error
    //   }
    // }
  } else if (value.startsWith('#')) {
    // New template string processing with # prefix
    val = renderTemplate(
      value.substring(1),
      store,
      renderTemplateFunctions(store, store?.formDataRaw)
    );
  } else if (value.startsWith('js:')) {
    // New template string processing with js: prefix
    const template = value.substring(3);
    console.log('js template:', template, 'store: ', store);
    const result = renderTemplateWithJS(template, store, {
      returnRawValues: true,
      sandbox: true,
    });
    // Handling error output from evaluation
    if (typeof result === 'string' && result?.startsWith('[Error:')) {
      console.error('js template error:', template, result);
      val = value;
    } else {
      val = result;
    }
    console.log('js template result:', template, val);
  } else {
    val = value;
  }
  return val;
};

const evalConditionExpression = (value: string | boolean = '', store: any) => {
  let result = false;
  const val =
    typeof value === 'string' ? evalStringExpression(value, store) : value;

  if (typeof val === 'boolean') {
    result = val;
  }
  return result;
};

const ComponentHOC = ({
  config: cfg,
  idx: i,
  props: newProps,
  store,
  Component: Comp,
}: RenderedCompProps) => {
  if (Comp) {
    return (
      <div
        key={i}
        style={cfg.disableComponentConfig ? { position: 'relative' } : {}}
      >
        {cfg?.layout ? (
          <div style={cfg.layout || {}}>
            <Comp {...(newProps || {})} />
          </div>
        ) : (
          <Comp {...(newProps || {})} />
        )}
        {cfg.disableComponentConfig && (
          <InteractionBlocker
            disableCondition={evalConditionExpression(
              cfg.disableComponentConfig.disableCondition,
              store
            )}
            label={
              cfg.disableComponentConfig.noIconLabel
                ? ''
                : cfg.disableComponentConfig.label || 'read only'
            }
            labelSize={cfg.disableComponentConfig.labelSize}
            icon={
              cfg.disableComponentConfig.noIconLabel
                ? undefined
                : cfg.disableComponentConfig.icon || {
                    type: 'lock-04',
                    size: 20,
                    strokeWidth: '1',
                    color: '#b28511',
                  }
            }
            style={cfg.disableComponentConfig.style || {}}
          />
        )}
      </div>
    );
  } else {
    console.warn(`Component "${cfg.component}" not found in componentMap`);
    return (
      <div key={i} style={{ backgroundColor: 'red', color: 'white' }}>
        Component "{cfg.component}" cannot be found!
      </div>
    );
  }
};
export const DebounceCompV1 = ({ config, component }: DebounceCompProps) => {
  const id = useRef<number | null>(null);
  const [loadingComponent, setLoadingComponent] = useState<boolean>(
    !!config.debounceMs
  );

  useEffect(() => {
    if (config.debounceMs) {
      id.current = window.setTimeout(
        () => setLoadingComponent(false),
        config.debounceMs
      );
    }
    return () => {
      if (id.current) clearTimeout(id.current);
    };
  }, [config.debounceMs]);

  return loadingComponent ? (
    <Loader type="alert" text="Loading please wait..." />
  ) : (
    component
  );
};
