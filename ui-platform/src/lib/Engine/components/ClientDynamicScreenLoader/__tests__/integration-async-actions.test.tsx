import {
  act,
  render,
  renderHook,
  screen,
  waitFor,
} from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionAsync } from '../../../hooks/useClientActionsAsync';
import type {
  ActionConfig,
  ExtendedActionConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../../models';
import { ClientDynamicScreenLoaderV2 } from '../ClientDynamicScreenLoaderV2';

// Create shared mock functions for async loading
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();
const mockSetModalState = vi.fn();
const mockAddError = vi.fn();

// Mock all dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'initial-data',
      user: { name: 'Test User' },
      auth: { token: 'mock-token' },
      setState: mockSetState,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: mockSetModalState,
  })),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: mockAddError,
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-router-dom', () => ({
  useNavigation: vi.fn(() => ({
    state: 'idle',
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/template-processor', () => ({
  processPropsForRender: vi.fn((props, store) => ({
    ...props,
    processedBy: 'mock',
  })),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

// Mock components for testing
const MockTestComponent = ({
  _callClientAction,
  testProp,
  debug,
  ...props
}: any) => {
  const handleClick = () => {
    if (_callClientAction) {
      _callClientAction({
        type: 'clientAction',
        action: 'log',
        payload: ['Component action triggered'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  };

  return (
    <div data-testid="mock-test-component" onClick={handleClick}>
      Mock Component - {testProp}
      {debug && (
        <span data-testid="debug-info">Debug: {JSON.stringify(props)}</span>
      )}
    </div>
  );
};

const MockAsyncComponent = ({ _callClientAction, ...props }: any) => {
  React.useEffect(() => {
    // Simulate component that triggers async actions on mount
    if (_callClientAction) {
      _callClientAction({
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [
          {
            url: 'https://api.example.com/data',
            key: 'componentData',
            method: 'GET',
          },
        ],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  }, [_callClientAction]);

  return <div data-testid="mock-async-component">Async Component</div>;
};

const MockSlowComponent = ({ _callClientAction, ...props }: any) => {
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // Simulate slow component initialization
    const timer = setTimeout(() => {
      setLoading(false);
      if (_callClientAction) {
        _callClientAction({
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ slowComponentReady: true }],
          asyncLoadStart: true,
          asyncLoadEnd: true,
        });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [_callClientAction]);

  if (loading) {
    return <div data-testid="mock-slow-component-loading">Loading...</div>;
  }

  return <div data-testid="mock-slow-component">Slow Component Ready</div>;
};

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const formMethods = useForm();
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};

describe('ClientDynamicScreenLoaderV2 - Async Actions Integration', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;
  let mockCallClientAction: any;

  const componentMap = {
    TestComponent: MockTestComponent,
    AsyncComponent: MockAsyncComponent,
    SlowComponent: MockSlowComponent,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;

    // Create a mock callClientAction that uses the actual hook
    mockCallClientAction = vi.fn();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('Async Loading State Integration', () => {
    it('should manage loading states correctly when client actions are triggered', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'TestComponent',
            props: {
              testProp: 'async-test',
            },
            debounceMs: 100,
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast-forward past debounce time
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
      });

      // Simulate clicking the component to trigger async action
      const component = screen.getByTestId('mock-test-component');

      await act(async () => {
        component.click();
      });

      // Verify that the component rendered and can trigger actions
      expect(component).toBeInTheDocument();
      expect(component.textContent).toContain('async-test');
    });

    it('should handle async actions during component mounting phase', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'AsyncComponent',
            props: {
              autoTrigger: true,
            },
            requireStoreKeys: ['testData'],
            progressiveReadiness: true,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to mount and trigger async action
      await waitFor(() => {
        expect(screen.getByTestId('mock-async-component')).toBeInTheDocument();
      });

      // Verify component rendered successfully
      expect(screen.getByTestId('mock-async-component')).toBeInTheDocument();
    });
  });

  describe('useDataReadiness Compatibility', () => {
    it('should work with components that have data readiness requirements', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SlowComponent',
            props: {
              requiresData: true,
            },
            requireStoreKeys: ['testData', 'user'],
            minWaitTimeMs: 50,
            maxWaitTimeMs: 1000,
            progressiveReadiness: true,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Initially should show loading
      expect(
        screen.getByTestId('mock-slow-component-loading')
      ).toBeInTheDocument();

      // Fast-forward time to allow component to finish loading
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Wait for component to be ready
      await waitFor(() => {
        expect(screen.getByTestId('mock-slow-component')).toBeInTheDocument();
      });

      expect(screen.getByTestId('mock-slow-component')).toBeInTheDocument();
    });

    it('should handle multiple components with different readiness requirements', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'TestComponent',
            props: { testProp: 'fast' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
          {
            component: 'SlowComponent',
            props: { testProp: 'slow' },
            requireStoreKeys: ['testData', 'user'],
            minWaitTimeMs: 100,
            progressiveReadiness: true,
          } as FragmentConfig,
          {
            component: 'AsyncComponent',
            props: { testProp: 'async' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast component should render quickly
      await waitFor(() => {
        expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
      });

      // Async component should render
      await waitFor(() => {
        expect(screen.getByTestId('mock-async-component')).toBeInTheDocument();
      });

      // Slow component should initially show loading
      expect(
        screen.getByTestId('mock-slow-component-loading')
      ).toBeInTheDocument();

      // Fast-forward time for slow component
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Slow component should now be ready
      await waitFor(() => {
        expect(screen.getByTestId('mock-slow-component')).toBeInTheDocument();
      });

      // All components should be present
      expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
      expect(screen.getByTestId('mock-async-component')).toBeInTheDocument();
      expect(screen.getByTestId('mock-slow-component')).toBeInTheDocument();
    });
  });

  describe('Real-world Scenarios', () => {
    it('should handle screen loading with concurrent client actions', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'TestComponent',
            props: {
              testProp: 'concurrent-1',
              triggerOnMount: true,
            },
            debounceMs: 50,
          } as FragmentConfig,
          {
            component: 'AsyncComponent',
            props: {
              testProp: 'concurrent-2',
            },
            debounceMs: 75,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionsConcurrently}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast-forward past all debounce times
      await act(async () => {
        vi.advanceTimersByTime(100);
      });

      // Both components should render
      await waitFor(() => {
        expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
        expect(screen.getByTestId('mock-async-component')).toBeInTheDocument();
      });

      // Verify components have correct props
      expect(screen.getByTestId('mock-test-component').textContent).toContain(
        'concurrent-1'
      );
    });

    it('should handle sequential client actions during screen rendering', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SlowComponent',
            props: {
              testProp: 'sequential',
              chainActions: true,
            },
            requireStoreKeys: ['testData'],
            minWaitTimeMs: 50,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const sequentialActions: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ step: 1 }],
          asyncLoadStart: true,
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Step 1 complete'],
        },
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ step: 2 }],
          asyncLoadEnd: true,
        },
      ];

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={(config) =>
              hookResult.current.callClientActionsSequentially(
                Array.isArray(config) ? config : [config]
              )
            }
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Execute sequential actions
      await act(async () => {
        await hookResult.current.callClientActionsSequentially(
          sequentialActions
        );
      });

      // Fast-forward time for component readiness
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Component should eventually render
      await waitFor(() => {
        expect(screen.getByTestId('mock-slow-component')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling and Regression Prevention', () => {
    it('should handle client action errors without breaking screen loader', async () => {
      const MockErrorComponent = ({ _callClientAction, ...props }: any) => {
        const handleError = () => {
          if (_callClientAction) {
            _callClientAction({
              type: 'clientAction',
              action: 'nonExistentAction',
              payload: ['this will fail'],
              asyncLoadStart: true,
              asyncLoadEnd: true,
            });
          }
        };

        return (
          <div data-testid="mock-error-component" onClick={handleError}>
            Error Component
          </div>
        );
      };

      const errorComponentMap = {
        ...componentMap,
        ErrorComponent: MockErrorComponent,
      };

      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'ErrorComponent',
            props: { testProp: 'error-test' },
          } as FragmentConfig,
          {
            component: 'TestComponent',
            props: { testProp: 'normal-test' },
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={errorComponentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Both components should render despite potential errors
      await waitFor(() => {
        expect(screen.getByTestId('mock-error-component')).toBeInTheDocument();
        expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
      });

      // Trigger error action
      const errorComponent = screen.getByTestId('mock-error-component');
      await act(async () => {
        errorComponent.click();
      });

      // Screen loader should still function normally
      expect(screen.getByTestId('mock-error-component')).toBeInTheDocument();
      expect(screen.getByTestId('mock-test-component')).toBeInTheDocument();
    });

    it('should prevent memory leaks during component unmounting', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SlowComponent',
            props: { testProp: 'memory-test' },
            debounceMs: 200,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const { unmount } = render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Start the debounce timer
      await act(async () => {
        vi.advanceTimersByTime(50);
      });

      // Unmount before debounce completes
      unmount();

      // Fast-forward past debounce time
      await act(async () => {
        vi.advanceTimersByTime(200);
      });

      // Should not cause any errors or memory leaks
      expect(true).toBe(true); // Test passes if no errors thrown
    });
  });
});
