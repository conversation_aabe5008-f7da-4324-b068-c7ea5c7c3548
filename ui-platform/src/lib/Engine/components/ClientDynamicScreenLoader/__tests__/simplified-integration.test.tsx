import {
  act,
  render,
  renderHook,
  screen,
  waitFor,
} from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionAsync } from '../../../hooks/useClientActionsAsync';
import type {
  ActionConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../../models';
import { ClientDynamicScreenLoaderV2 } from '../ClientDynamicScreenLoaderV2';

// Mock stores with minimal implementation
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();

vi.mock('../../../useAppStore', () => {
  // Create a proper Zustand store mock that matches the actual interface
  const state = {
    testData: 'initial-data',
    user: { name: 'Test User' },
    auth: { token: 'mock-token' },
    filtersData: [],
    reset: vi.fn(),
    setDynamicState: vi.fn(),
    removeDynamicState: vi.fn(),
  };

  // Create the store instance with Zustand-like methods
  const storeInstance = {
    ...state,
    setState: vi.fn((updater, replace = false) => {
      if (typeof updater === 'function') {
        const updates = updater(state);
        if (replace) {
          Object.keys(state).forEach((key) => {
            if (
              !(key in updates) &&
              typeof state[key as keyof typeof state] !== 'function'
            ) {
              (state as any)[key] = undefined;
            }
          });
        }
        Object.assign(state, updates);
      } else {
        if (replace) {
          Object.keys(state).forEach((key) => {
            if (
              !(key in updater) &&
              typeof state[key as keyof typeof state] !== 'function'
            ) {
              (state as any)[key] = undefined;
            }
          });
        }
        Object.assign(state, updater);
      }
    }),
    getState: vi.fn(() => state),
    subscribe: vi.fn(() => vi.fn()), // Returns unsubscribe function
    destroy: vi.fn(),
  };

  // Mock the useAppStore as both a hook function AND the store instance
  const useAppStore = Object.assign(
    vi.fn((selector) => {
      if (selector) {
        return selector(state);
      }
      return state;
    }),
    storeInstance
  );

  return {
    useAppStore,
  };
});

vi.mock('../../../useAsyncLoaderStore', () => {
  // Create async loader store mock
  const state = {
    asyncLoading: false,
    setAsyncLoading: vi.fn((loading) => {
      state.asyncLoading = loading;
    }),
    getState: vi.fn(() => state),
    setState: vi.fn((updater) => {
      if (typeof updater === 'function') {
        const updates = updater(state);
        Object.assign(state, updates);
      } else {
        Object.assign(state, updater);
      }
    }),
    subscribe: vi.fn(() => vi.fn()),
    destroy: vi.fn(),
  };

  // Mock the useAsyncLoaderStore hook function that accepts a selector
  const useAsyncLoaderStore = vi.fn((selector) => {
    if (selector) {
      return selector(state);
    }
    return state;
  });

  return {
    useAsyncLoaderStore,
  };
});

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

// Simple test components
const SimpleTestComponent = ({
  _callClientAction,
  testProp,
  ...props
}: any) => {
  const handleClick = () => {
    if (_callClientAction) {
      _callClientAction({
        type: 'clientAction',
        action: 'log',
        payload: ['Component action triggered'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  };

  return (
    <div data-testid="simple-test-component" onClick={handleClick}>
      Simple Component - {testProp}
    </div>
  );
};

const AsyncTestComponent = ({ _callClientAction, ...props }: any) => {
  React.useEffect(() => {
    if (_callClientAction) {
      _callClientAction({
        type: 'clientAction',
        action: 'updateStore',
        payload: [{ componentMounted: true }],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  }, [_callClientAction]);

  return <div data-testid="async-test-component">Async Component</div>;
};

// Test wrapper
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const formMethods = useForm();
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};

describe('ClientDynamicScreenLoaderV2 - Simplified Integration', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  const componentMap = {
    SimpleTest: SimpleTestComponent,
    AsyncTest: AsyncTestComponent,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('Basic Integration', () => {
    it('should render components with client action integration', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SimpleTest',
            props: {
              testProp: 'integration-test',
            },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('simple-test-component')).toBeInTheDocument();
      });

      // Verify component rendered with correct props
      const component = screen.getByTestId('simple-test-component');
      expect(component.textContent).toContain('integration-test');
    });

    it('should handle async client actions during component lifecycle', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'AsyncTest',
            props: {},
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render and trigger async action
      await waitFor(() => {
        expect(screen.getByTestId('async-test-component')).toBeInTheDocument();
      });

      // Verify component rendered successfully
      expect(screen.getByTestId('async-test-component')).toBeInTheDocument();
    });

    it('should handle multiple components with different configurations', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SimpleTest',
            props: { testProp: 'first' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
          {
            component: 'AsyncTest',
            props: {},
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for both components to render
      await waitFor(() => {
        expect(screen.getByTestId('simple-test-component')).toBeInTheDocument();
        expect(screen.getByTestId('async-test-component')).toBeInTheDocument();
      });

      // Verify both components rendered correctly
      expect(screen.getByTestId('simple-test-component').textContent).toContain(
        'first'
      );
      expect(screen.getByTestId('async-test-component')).toBeInTheDocument();
    });

    it('should handle client action triggers from user interactions', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SimpleTest',
            props: { testProp: 'clickable' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('simple-test-component')).toBeInTheDocument();
      });

      // Simulate user interaction
      const component = screen.getByTestId('simple-test-component');
      await act(async () => {
        component.click();
      });

      // Verify component still functions after interaction
      expect(component).toBeInTheDocument();
      expect(component.textContent).toContain('clickable');
    });

    it('should maintain component state during async operations', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'SimpleTest',
            props: { testProp: 'stateful' },
            requireStoreKeys: ['testData'],
            debounceMs: 100,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast-forward past debounce time
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('simple-test-component')).toBeInTheDocument();
      });

      // Verify component maintains its state
      const component = screen.getByTestId('simple-test-component');
      expect(component.textContent).toContain('stateful');
    });
  });
});
