import {
  act,
  render,
  renderHook,
  screen,
  waitFor,
} from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionAsync } from '../../../hooks/useClientActionsAsync';
import type {
  ActionConfig,
  ExtendedActionConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../../models';
import { ClientDynamicScreenLoaderV2 } from '../ClientDynamicScreenLoaderV2';

// Create shared mock functions
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();

// Mock store with performance tracking
let storeUpdateCount = 0;
const mockAppStoreState = {
  testData: 'initial-data',
  user: { name: 'Test User' },
  auth: { token: 'mock-token' },
  performanceData: {},
  setState: vi.fn((updater) => {
    storeUpdateCount++;
    if (typeof updater === 'function') {
      const newState = updater(mockAppStoreState);
      Object.assign(mockAppStoreState, newState);
    } else {
      Object.assign(mockAppStoreState, updater);
    }
  }),
};

// Mock dependencies with performance tracking
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    return selector ? selector(mockAppStoreState) : mockAppStoreState;
  }),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-router-dom', () => ({
  useNavigation: vi.fn(() => ({
    state: 'idle',
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/template-processor', () => ({
  processPropsForRender: vi.fn((props, store) => ({
    ...props,
    processedBy: 'mock',
  })),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

// Performance tracking component
const PerformanceTrackingComponent = ({
  _callClientAction,
  componentId,
  ...props
}: any) => {
  const [renderCount, setRenderCount] = React.useState(0);
  const [actionCount, setActionCount] = React.useState(0);

  React.useEffect(() => {
    setRenderCount((prev) => prev + 1);
  });

  const handleAction = async () => {
    setActionCount((prev) => prev + 1);
    if (_callClientAction) {
      await _callClientAction({
        type: 'clientAction',
        action: 'updateStore',
        payload: [{ [`${componentId}_actionCount`]: actionCount + 1 }],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  };

  return (
    <div
      data-testid={`performance-component-${componentId}`}
      onClick={handleAction}
    >
      Component {componentId} - Renders: {renderCount}, Actions: {actionCount}
    </div>
  );
};

// Heavy computation component
const HeavyComputationComponent = ({
  _callClientAction,
  computationSize = 1000,
  ...props
}: any) => {
  const [result, setResult] = React.useState(0);

  React.useEffect(() => {
    // Simulate heavy computation
    let sum = 0;
    for (let i = 0; i < computationSize; i++) {
      sum += Math.random();
    }
    setResult(sum);

    // Trigger action after computation
    if (_callClientAction) {
      _callClientAction({
        type: 'clientAction',
        action: 'updateStore',
        payload: [{ computationResult: sum }],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      });
    }
  }, [computationSize, _callClientAction]);

  return (
    <div data-testid="heavy-computation-component">
      Heavy Computation Result: {result.toFixed(2)}
    </div>
  );
};

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const formMethods = useForm();
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};

describe('ClientDynamicScreenLoaderV2 - Performance Integration', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  const componentMap = {
    PerformanceComponent: PerformanceTrackingComponent,
    HeavyComponent: HeavyComputationComponent,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    storeUpdateCount = 0;

    // Reset mock store state
    Object.assign(mockAppStoreState, {
      testData: 'initial-data',
      user: { name: 'Test User' },
      auth: { token: 'mock-token' },
      performanceData: {},
    });

    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('Performance Optimization', () => {
    it('should minimize re-renders during async action execution', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'PerformanceComponent',
            props: { componentId: 'perf1' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
          {
            component: 'PerformanceComponent',
            props: { componentId: 'perf2' },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for components to render
      await waitFor(() => {
        expect(
          screen.getByTestId('performance-component-perf1')
        ).toBeInTheDocument();
        expect(
          screen.getByTestId('performance-component-perf2')
        ).toBeInTheDocument();
      });

      // Trigger actions on both components
      const comp1 = screen.getByTestId('performance-component-perf1');
      const comp2 = screen.getByTestId('performance-component-perf2');

      await act(async () => {
        comp1.click();
        comp2.click();
      });

      // Verify components rendered efficiently
      expect(comp1.textContent).toContain('Actions: 1');
      expect(comp2.textContent).toContain('Actions: 1');
    });

    it('should handle multiple concurrent async actions without performance degradation', async () => {
      const screenConfig: ScreenConfig = {
        fragments: Array.from(
          { length: 5 },
          (_, i) =>
            ({
              component: 'PerformanceComponent',
              props: { componentId: `concurrent${i}` },
              debounceMs: 50,
            } as FragmentConfig)
        ),
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const startTime = performance.now();

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionsConcurrently}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast-forward past debounce time
      await act(async () => {
        vi.advanceTimersByTime(100);
      });

      // Wait for all components to render
      await waitFor(() => {
        for (let i = 0; i < 5; i++) {
          expect(
            screen.getByTestId(`performance-component-concurrent${i}`)
          ).toBeInTheDocument();
        }
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (this is a rough check)
      expect(renderTime).toBeLessThan(1000); // Less than 1 second
    });

    it('should efficiently handle heavy computation components', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'HeavyComponent',
            props: { computationSize: 500 },
            requireStoreKeys: ['testData'],
            minWaitTimeMs: 100,
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const startTime = performance.now();

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Fast-forward past minimum wait time
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      // Wait for heavy component to render
      await waitFor(() => {
        expect(
          screen.getByTestId('heavy-computation-component')
        ).toBeInTheDocument();
      });

      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Should complete within reasonable time
      expect(totalTime).toBeLessThan(2000); // Less than 2 seconds
      expect(
        screen.getByTestId('heavy-computation-component').textContent
      ).toContain('Result:');
    });
  });

  describe('State Synchronization', () => {
    it('should maintain state consistency during rapid updates', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'PerformanceComponent',
            props: { componentId: 'sync1' },
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(
          screen.getByTestId('performance-component-sync1')
        ).toBeInTheDocument();
      });

      const initialStoreUpdateCount = storeUpdateCount;

      // Trigger rapid state updates
      const rapidActions: ActionConfig[] = Array.from(
        { length: 10 },
        (_, i) => ({
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ rapidUpdate: i }],
        })
      );

      await act(async () => {
        await hookResult.current.callClientActionsSequentially(rapidActions);
      });

      // Verify state was updated correctly
      expect(storeUpdateCount).toBeGreaterThan(initialStoreUpdateCount);
      expect(mockAppStoreState.rapidUpdate).toBe(9); // Last update should be 9
    });

    it('should prevent race conditions in concurrent state updates', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'PerformanceComponent',
            props: { componentId: 'race1' },
          } as FragmentConfig,
          {
            component: 'PerformanceComponent',
            props: { componentId: 'race2' },
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionsConcurrently}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for components to render
      await waitFor(() => {
        expect(
          screen.getByTestId('performance-component-race1')
        ).toBeInTheDocument();
        expect(
          screen.getByTestId('performance-component-race2')
        ).toBeInTheDocument();
      });

      // Create concurrent actions that might cause race conditions
      const concurrentActions: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ counter: 1, source: 'action1' }],
          asyncLoadStart: true,
        },
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ counter: 2, source: 'action2' }],
        },
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ counter: 3, source: 'action3' }],
          asyncLoadEnd: true,
        },
      ];

      await act(async () => {
        await hookResult.current.callClientActionsConcurrently(
          concurrentActions
        );
      });

      // Verify final state is consistent
      expect(mockAppStoreState.counter).toBeDefined();
      expect(mockAppStoreState.source).toBeDefined();
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(false);
    });
  });
});
