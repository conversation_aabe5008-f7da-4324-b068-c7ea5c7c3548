import {
  act,
  render,
  renderHook,
  screen,
  waitFor,
} from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionAsync } from '../../../hooks/useClientActionsAsync';
import type {
  ActionConfig,
  ExtendedActionConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../../models';
import { ClientDynamicScreenLoaderV2 } from '../ClientDynamicScreenLoaderV2';

// Create shared mock functions
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();

// Mock dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'initial-data',
      user: { name: 'Test User' },
      formData: {},
      searchResults: [],
      setState: mockSetState,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-router-dom', () => ({
  useNavigation: vi.fn(() => ({
    state: 'idle',
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi
    .fn()
    .mockResolvedValue({ searchResults: ['result1', 'result2'] }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/template-processor', () => ({
  processPropsForRender: vi.fn((props, store) => ({
    ...props,
    processedBy: 'mock',
  })),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

// Debounced search component
const DebouncedSearchComponent = ({
  _callClientAction,
  debounceMs = 300,
  ...props
}: any) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [searchCount, setSearchCount] = React.useState(0);

  const handleSearch = React.useCallback(
    (term: string) => {
      setSearchCount((prev) => prev + 1);
      if (_callClientAction) {
        _callClientAction({
          type: 'clientAction',
          action: 'triggerFetchCall',
          payload: [
            {
              url: `https://api.example.com/search?q=${term}`,
              key: 'searchResults',
              method: 'GET',
            },
          ],
          debounce: debounceMs,
          asyncLoadStart: true,
          asyncLoadEnd: true,
        });
      }
    },
    [_callClientAction, debounceMs]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    handleSearch(value);
  };

  return (
    <div data-testid="debounced-search-component">
      <input
        data-testid="search-input"
        type="text"
        value={searchTerm}
        onChange={handleInputChange}
        placeholder="Search..."
      />
      <div data-testid="search-count">Search Count: {searchCount}</div>
    </div>
  );
};

// Form with debounced validation
const DebouncedFormComponent = ({ _callClientAction, ...props }: any) => {
  const [formData, setFormData] = React.useState({ email: '', password: '' });
  const [validationCount, setValidationCount] = React.useState(0);

  const handleValidation = React.useCallback(
    (data: any) => {
      setValidationCount((prev) => prev + 1);
      if (_callClientAction) {
        _callClientAction({
          type: 'clientAction',
          action: 'validateForm',
          payload: [data],
          debounce: 500,
          asyncLoadStart: true,
          asyncLoadEnd: true,
        });
      }
    },
    [_callClientAction]
  );

  const handleInputChange = (field: string, value: string) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    handleValidation(newData);
  };

  return (
    <div data-testid="debounced-form-component">
      <input
        data-testid="email-input"
        type="email"
        value={formData.email}
        onChange={(e) => handleInputChange('email', e.target.value)}
        placeholder="Email"
      />
      <input
        data-testid="password-input"
        type="password"
        value={formData.password}
        onChange={(e) => handleInputChange('password', e.target.value)}
        placeholder="Password"
      />
      <div data-testid="validation-count">
        Validation Count: {validationCount}
      </div>
    </div>
  );
};

// Auto-save component
const AutoSaveComponent = ({ _callClientAction, ...props }: any) => {
  const [content, setContent] = React.useState('');
  const [saveCount, setSaveCount] = React.useState(0);

  const handleAutoSave = React.useCallback(
    (data: string) => {
      setSaveCount((prev) => prev + 1);
      if (_callClientAction) {
        _callClientAction({
          type: 'clientAction',
          action: 'autoSave',
          payload: [{ content: data, timestamp: Date.now() }],
          debounce: 1000, // 1 second debounce for auto-save
          asyncLoadStart: true,
          asyncLoadEnd: true,
        });
      }
    },
    [_callClientAction]
  );

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setContent(value);
    handleAutoSave(value);
  };

  return (
    <div data-testid="auto-save-component">
      <textarea
        data-testid="content-textarea"
        value={content}
        onChange={handleContentChange}
        placeholder="Start typing to auto-save..."
        rows={4}
        cols={50}
      />
      <div data-testid="save-count">Save Count: {saveCount}</div>
    </div>
  );
};

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const formMethods = useForm();
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};

describe('ClientDynamicScreenLoaderV2 - Debounce Integration', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  const componentMap = {
    DebouncedSearch: DebouncedSearchComponent,
    DebouncedForm: DebouncedFormComponent,
    AutoSave: AutoSaveComponent,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('Debounced Search Functionality', () => {
    it('should debounce search requests correctly', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'DebouncedSearch',
            props: { debounceMs: 300 },
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(
          screen.getByTestId('debounced-search-component')
        ).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId(
        'search-input'
      ) as HTMLInputElement;

      // Simulate rapid typing
      await act(async () => {
        searchInput.value = 't';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      });

      await act(async () => {
        searchInput.value = 'te';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      });

      await act(async () => {
        searchInput.value = 'test';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      });

      // Should show multiple search attempts
      expect(screen.getByTestId('search-count').textContent).toContain('3');

      // Fast-forward past debounce time
      await act(async () => {
        vi.advanceTimersByTime(350);
      });

      // Verify final search term
      expect(searchInput.value).toBe('test');
    });

    it('should handle multiple debounced components independently', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'DebouncedSearch',
            props: { debounceMs: 200 },
          } as FragmentConfig,
          {
            component: 'DebouncedForm',
            props: {},
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for components to render
      await waitFor(() => {
        expect(
          screen.getByTestId('debounced-search-component')
        ).toBeInTheDocument();
        expect(
          screen.getByTestId('debounced-form-component')
        ).toBeInTheDocument();
      });

      const searchInput = screen.getByTestId(
        'search-input'
      ) as HTMLInputElement;
      const emailInput = screen.getByTestId('email-input') as HTMLInputElement;

      // Trigger actions on both components
      await act(async () => {
        searchInput.value = 'search term';
        searchInput.dispatchEvent(new Event('input', { bubbles: true }));

        emailInput.value = '<EMAIL>';
        emailInput.dispatchEvent(new Event('input', { bubbles: true }));
      });

      // Both should show activity
      expect(screen.getByTestId('search-count').textContent).toContain('1');
      expect(screen.getByTestId('validation-count').textContent).toContain('1');

      // Fast-forward past both debounce times
      await act(async () => {
        vi.advanceTimersByTime(600);
      });

      // Verify both components handled their debounced actions
      expect(searchInput.value).toBe('search term');
      expect(emailInput.value).toBe('<EMAIL>');
    });
  });

  describe('Auto-save Functionality', () => {
    it('should handle auto-save with longer debounce periods', async () => {
      const screenConfig: ScreenConfig = {
        fragments: [
          {
            component: 'AutoSave',
            props: {},
            requireStoreKeys: ['testData'],
          } as FragmentConfig,
        ],
      };

      const { result: hookResult } = renderHook(() =>
        useClientActionAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      render(
        <TestWrapper>
          <ClientDynamicScreenLoaderV2
            config={screenConfig}
            componentMap={componentMap}
            callClientAction={hookResult.current.callClientActionAsync}
            keycloak={mockKeycloak}
          />
        </TestWrapper>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('auto-save-component')).toBeInTheDocument();
      });

      const textarea = screen.getByTestId(
        'content-textarea'
      ) as HTMLTextAreaElement;

      // Simulate typing
      await act(async () => {
        textarea.value = 'Hello';
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
      });

      await act(async () => {
        textarea.value = 'Hello World';
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
      });

      // Should show multiple save attempts
      expect(screen.getByTestId('save-count').textContent).toContain('2');

      // Fast-forward past auto-save debounce time (1 second)
      await act(async () => {
        vi.advanceTimersByTime(1100);
      });

      // Verify final content
      expect(textarea.value).toBe('Hello World');
    });
  });
});
