// external modules imports
import Keycloak from 'keycloak-js';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { FieldValues, useFormContext, UseFormReturn } from 'react-hook-form';
// interal module imports
import { InteractionBlocker, Loader } from '../../../Components';
import { logger, TemplateLiteralLogger } from '../../../Utilities';
import { evalConditionExpression } from '../../helpers';
import { getNestedProperty } from '../../helpers/client-utils';
import { processPropsForRender } from '../../helpers/template-processor';
import { useDataReadiness } from '../../hooks/useDataReadiness';
import {
  ActionConfig,
  ActionPanelConfig,
  FragmentConfig,
  ScreenConfig,
} from '../../models';
import { ViewConfig } from '../../models/view.config';
import { useAppStore } from '../../useAppStore';

const warn = TemplateLiteralLogger.createLog(
  {
    prefix: '⚠[Client Dynamic Screen Loader V2]:',
    enabled: true,
    options: { style: { backgroundColor: '#FFF4DC', color: '#F0A607' } },
  },
  'warn'
);

interface Props {
  config: ScreenConfig | ActionPanelConfig | ViewConfig;
  fetcher?: any;
  submit?: any;
  navigation?: any;
  componentMap: any;
  actionData?: any;
  keycloak?: Keycloak;
  callClientAction: (config: ActionConfig) => void;
}
// const queryEngine = new QueryEngine();
interface RenderedCompsProps extends Props {
  store: any;
  formContext: UseFormReturn<FieldValues, any, undefined>;
  fragments: FragmentConfig[];
}

function runFragmentCondition(
  conditionalFragment: ViewConfig['conditionalFragment'],
  store: any
) {
  logger.configure({
    prefix:
      '🔍[Client Dynamic Screen Loader V2] - Evaluating Conditional Fragment:',
    enabled: conditionalFragment?.fragments?.some((f) => f.debug),
    options: { style: { color: '#980a43' } },
  });
  const condition = conditionalFragment?.condition;
  const log = logger.log;
  let item;

  if (typeof condition?.item === 'string' && condition.item.startsWith('$')) {
    const storePath = condition.item.replace('$', '');
    item = getNestedProperty(store, storePath, []);
    log`${{ item }}`;
  } else if (
    typeof condition?.item === 'string' &&
    condition.item.startsWith('q:')
  ) {
    // const [query, storePath] = condition.item.replace('q:', '');
    // item = queryEngine.query(store, storePath);
  } else {
    item = condition?.item;
  }

  switch (condition?.comparator) {
    case 'isEmpty': {
      return item ? !item.length : false;
    }

    default:
      return false;
  }
}

const RenderedComp = memo(
  ({
    fragments,
    componentMap,
    store,
    actionData,
    callClientAction,
    config,
    fetcher,
    formContext,
    keycloak,
    navigation,
    submit,
  }: RenderedCompsProps) => {
    return fragments?.map((cfg, i) => {
      return (
        <Fragment
          key={i}
          fragmentConfig={cfg}
          idx={i}
          {...{
            store,
            actionData,
            formContext,
            submit,
            fetcher,
            navigation,
            callClientAction,
            keycloak,
            config,
            componentMap,
          }}
        />
      );
    });
  }
);

export function ClientDynamicScreenLoaderV2({
  config,
  navigation,
  componentMap,
  fetcher,
  submit,
  actionData,
  keycloak,
  callClientAction,
}: Props) {
  const store: any = useAppStore((state) => state);
  const formContext = useFormContext();
  let fragments: FragmentConfig[] = [];
  // const conditionalFragment = {condition:{item: '$claims', comparator: 'hasLength', value: 0}, fragments: []}

  if (config?.conditionalFragment) {
    const useThis = runFragmentCondition(config.conditionalFragment, store);

    if (useThis) {
      fragments = config.conditionalFragment.fragments;
    } else {
      fragments = config.fragments || [];
    }
  } else {
    fragments = config.fragments || [];
  }
  return (
    <RenderedComp
      {...{
        fragments: fragments,
        store: store,
        formContext: formContext,
        componentMap: componentMap,
        actionData: actionData,
        callClientAction: callClientAction,
        config: config,
        fetcher: fetcher,
        keycloak: keycloak,
        navigation: navigation,
        submit: submit,
      }}
    />
  );
}

interface DebounceCompProps {
  config: FragmentConfig;
  component: any;
  componentsRequiringDataLoad?: string[];
}

interface NewProps {
  [key: string]: string | number | boolean | object;
  _actionData: any;
  _onNotify: any;
  _config: ScreenConfig | ActionPanelConfig | ViewConfig;
  _formContext: UseFormReturn<FieldValues, any, undefined>;
  _submit: (data: any) => void;
  _fetcher: (query: string) => Promise<any>;
  _navigation: any;
  _callClientAction: (config: ActionConfig) => void;
  _store: any;
  _keycloak: Keycloak;
}

interface RenderedCompProps {
  config: FragmentConfig;
  idx: number;
  props: NewProps;
  store: any;
  Component: any;
}

const ComponentHOC = ({
  config: cfg,
  idx: i,
  props: newProps,
  store,
  Component: Comp,
}: RenderedCompProps) => {
  if (Comp) {
    return (
      <div
        key={i}
        style={cfg.disableComponentConfig ? { position: 'relative' } : {}}
      >
        {cfg?.layout ? (
          <div style={cfg.layout || {}}>
            <Comp {...(newProps || {})} />
          </div>
        ) : (
          <Comp {...(newProps || {})} />
        )}
        {cfg.disableComponentConfig && (
          <InteractionBlocker
            disableCondition={evalConditionExpression(
              store,
              cfg.disableComponentConfig.disableCondition,
              { debug: cfg?.debug }
            )}
            label={
              cfg.disableComponentConfig.noIconLabel
                ? ''
                : cfg.disableComponentConfig.label || 'read only'
            }
            labelSize={cfg.disableComponentConfig.labelSize}
            icon={
              cfg.disableComponentConfig.noIconLabel
                ? undefined
                : cfg.disableComponentConfig.icon || {
                    type: 'lock-04',
                    size: 20,
                    strokeWidth: '1',
                    color: '#b28511',
                  }
            }
            style={cfg.disableComponentConfig.style || {}}
          />
        )}
      </div>
    );
  } else {
    warn`Component "${cfg.component}" not found in componentMap`;
    return (
      <div key={i} style={{ backgroundColor: 'red', color: 'white' }}>
        Component "{cfg.component}" cannot be found!
      </div>
    );
  }
};
interface FragmentProps extends Omit<RenderedCompsProps, 'fragments'> {
  fragmentConfig: FragmentConfig;
  idx: number;
}
const Fragment = ({ fragmentConfig: cfg, idx, ...rest }: FragmentProps) => {
  logger.configure({
    prefix: `🔍[Client Dynamic Screen Loader V2] - Mounting ${cfg.component} Component:`,
    enabled: cfg?.debug,
    options: { style: { color: '#980a43' } },
  });
  const {
    componentMap,
    store,
    actionData,
    formContext,
    submit,
    fetcher,
    navigation,
    callClientAction,
    keycloak,
    config,
  } = rest;
  const log = logger.log;
  log`${cfg}`;
  const Comp = componentMap[cfg.component] as any;

  const newProps = useMemo(() => {
    const processedProps = processPropsForRender(cfg.props, store, {
      debug: cfg?.debug,
    });
    return {
      ...processedProps,
      debug: cfg.props['debug'] || cfg.debug,
      _actionData: actionData,
      _onNotify: store.setState,
      _config: config,
      _formContext: formContext,
      _submit: submit,
      _fetcher: fetcher,
      _navigation: navigation,
      _callClientAction: callClientAction,
      _store: store, // Expose store to fragments
      _keycloak: keycloak || store?.auth,
    };
  }, [
    store,
    cfg,
    actionData,
    formContext,
    submit,
    fetcher,
    navigation,
    callClientAction,
    keycloak,
    config,
  ]);
  log`Generated new props ${newProps}`;
  if (Comp) {
    return (
      <DebounceCompV2
        key={idx}
        config={cfg}
        component={
          <ComponentHOC
            {...{
              config: cfg,
              idx,
              props: newProps,
              store: newProps._store,
              Component: Comp,
            }}
          />
        }
      />
    );
  } else {
    warn`Component "${cfg.component}" not found in componentMap`;
    return (
      <div key={idx} style={{ backgroundColor: 'red', color: 'white' }}>
        Component "{cfg.component}" cannot be found!
      </div>
    );
  }
};
export const DebounceCompV2 = ({
  config,
  component,
  componentsRequiringDataLoad = [],
}: DebounceCompProps) => {
  logger.configure({
    prefix: `🔍[Client Dynamic Screen Loader V2] - Mounting ${config.component} Component:`,
    enabled: config?.debug,
    options: { style: { color: '#490c6b' } },
  });
  const log = logger.log;
  const id = useRef<number | null>(null);
  const componentRequiresDataLoad = useMemo(
    () => ['DynamicTable', 'FormBuilder', ...componentsRequiringDataLoad],
    [componentsRequiringDataLoad]
  );
  log`${
    config.component
  } requires a data load delay: ${componentsRequiringDataLoad.includes(
    config.component
  )}`;

  // Use unified readiness hook to delay mounting until ready
  const readinessState = useDataReadiness({
    componentName: config.component,
    componentsRequiringDataLoad,
    // Callers can now extend requiredStoreKeys via config or use auto-detection from props
    requiredStoreKeys: config.requireStoreKeys || [],
    autoDetectFromProps: config.props,
    autoDetectEnabled: true,
    // progressive data loading
    progressiveReadiness: config.progressiveReadiness,
    minWaitTimeMs: config.minWaitTimeMs,
    maxWaitTimeMs: config.maxWaitTimeMs,
    retryInterval: config.retryInterval,
    allowPartialReadiness: config.allowPartialReadiness,
    debug: config?.debug,
  });

  const [loadingComponent, setLoadingComponent] = useState<boolean>(
    !!config.debounceMs || componentRequiresDataLoad.includes(config.component)
  );

  useEffect(() => {
    if (
      config.debounceMs ||
      componentRequiresDataLoad.includes(config.component)
    ) {
      id.current = window.setTimeout(
        () => setLoadingComponent(false),
        config.debounceMs || 500
      );
    }
    return () => {
      if (id.current) clearTimeout(id.current);
    };
  }, [
    config.debounceMs,
    config.component,
    componentsRequiringDataLoad,
    component,
    componentRequiresDataLoad,
  ]);

  // Gate on readiness as well as debounce
  const componentReady = config.progressiveReadiness
    ? readinessState.level === 'complete'
    : readinessState.ready;
  const componentStillLoading = config.progressiveReadiness
    ? readinessState.level === 'partial'
    : !componentReady;
  const show = componentReady && !loadingComponent;
  // const hasCriticalData = config?.requireStoreKeys && config.requireStoreKeys.length > 0 && !readinessState.missingKeys.some(key => (config.requireStoreKeys as string[]).includes(key));
  if (
    readinessState.waitTimeElapsed &&
    config.maxWaitTimeMs &&
    readinessState.waitTimeElapsed > 0.8 * config.maxWaitTimeMs
  ) {
    <Loader
      type="alert"
      text={`Taking longer than expected... ${Math.round(
        readinessState.progress * 100
      )}% done... `}
    />;
  }
  return !show ? (
    <Loader type="alert" text="Loading please wait..." />
  ) : componentStillLoading ? (
    <Loader
      type="alert"
      text={`Still loading, please wait, ${Math.round(
        readinessState.progress * 100
      )}% done... `}
    />
  ) : (
    component
  );
};
