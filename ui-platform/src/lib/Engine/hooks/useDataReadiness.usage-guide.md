# Enhanced useDataReadiness Hook - Usage Guide

## Overview

The enhanced `useDataReadiness` hook provides comprehensive support for progressive data loading scenarios while maintaining strict backward compatibility. It handles race conditions by preventing component rendering until required data is available, with advanced features for gradual data updates over time.

## Key Features

### 1. **Backward Compatibility**
- Existing `ClientDynamicScreenLoaderV2` usage continues to work without changes
- Traditional binary ready/not-ready behavior is preserved as default
- Same return interface with additional optional properties

### 2. **Progressive Readiness**
- Support for gradual data loading scenarios
- Detailed progress tracking (0-1 completion percentage)
- Readiness levels: 'none', 'partial', 'complete'
- Missing/available key tracking

### 3. **Time-based Controls**
- Minimum wait times to prevent premature readiness
- Maximum wait timeouts to prevent infinite blocking
- Configurable retry intervals for re-checking
- Data staleness detection and handling

### 4. **Graceful Degradation**
- Allow components to render with partial data
- Configurable thresholds for acceptable data completeness
- Non-critical key handling

## Basic Usage (Backward Compatible)

```typescript
// Existing usage continues to work unchanged
const { ready } = useDataReadiness({
  componentName: 'MyComponent',
  componentsRequiringDataLoad: ['DynamicTable', 'FormBuilder'],
  requiredStoreKeys: ['users', 'permissions'],
  debug: true
});

if (!ready) {
  return <Loader />;
}
return <MyComponent />;
```

## Enhanced Usage Examples

### 1. **Progressive Data Loading**

```typescript
const readinessState = useDataReadiness({
  componentName: 'DashboardWidget',
  requiredStoreKeys: ['analytics', 'userPreferences', 'notifications'],
  progressiveReadiness: true,
  gracefulDegradation: true,
  minWaitTimeMs: 500,    // Wait at least 500ms
  maxWaitTimeMs: 10000,  // Force ready after 10s
  retryInterval: 200,    // Check every 200ms
  debug: true
});

// Use detailed readiness information
if (readinessState.level === 'complete') {
  return <FullDashboard data={readinessState} />;
} else if (readinessState.level === 'partial' && readinessState.progress > 0.6) {
  return <PartialDashboard missingKeys={readinessState.missingKeys} />;
} else {
  return <LoadingDashboard progress={readinessState.progress} />;
}
```

### 2. **API Data with Staleness Detection**

```typescript
const { ready, staleness, missingKeys, waitTimeElapsed } = useDataReadiness({
  componentName: 'UserProfile',
  requiredStoreKeys: ['currentUser', 'userSettings'],
  staleDataThresholdMs: 300000, // 5 minutes
  maxWaitTimeMs: 15000,         // 15 seconds max
  progressiveReadiness: true,
  debug: true
});

// Handle stale data scenarios
const hasStaleData = Object.values(staleness).some(age => age > 300000);
if (hasStaleData) {
  // Trigger data refresh or show stale data warning
  console.log('Some data is stale, consider refreshing');
}
```

### 3. **Fast Local Data vs Slow API Calls**

```typescript
// For fast local data (localStorage, cached data)
const localDataReadiness = useDataReadiness({
  requiredStoreKeys: ['userPreferences', 'appSettings'],
  minWaitTimeMs: 0,      // No minimum wait
  maxWaitTimeMs: 1000,   // Quick timeout
  retryInterval: 50,     // Fast checking
});

// For slow API data
const apiDataReadiness = useDataReadiness({
  requiredStoreKeys: ['serverData', 'analytics'],
  minWaitTimeMs: 1000,   // Wait at least 1s for server
  maxWaitTimeMs: 30000,  // 30s timeout for APIs
  retryInterval: 500,    // Slower checking
  gracefulDegradation: true, // Allow partial data
});
```

## Configuration Guidelines

### Timing Parameters

| Scenario | minWaitTimeMs | maxWaitTimeMs | retryInterval | Notes |
|----------|---------------|---------------|---------------|-------|
| **Fast Local Data** | 0 | 1000 | 50 | Cached/localStorage data |
| **Standard API** | 500 | 15000 | 200 | Typical REST API calls |
| **Slow API/Analytics** | 1000 | 30000 | 500 | Heavy queries, reports |
| **Real-time Data** | 100 | 5000 | 100 | WebSocket, live updates |
| **Background Sync** | 0 | 60000 | 1000 | Non-critical background data |

### Progressive Readiness Thresholds

```typescript
// Conservative: Wait for most data
gracefulDegradation: true, // Allow rendering at >50% completion

// Aggressive: Render with minimal data
gracefulDegradation: true, // Combined with custom logic checking progress > 0.3

// Strict: All or nothing (default behavior)
gracefulDegradation: false, // Traditional behavior
```

## ReadinessState Properties

```typescript
interface ReadinessState {
  // Core properties
  ready: boolean;                    // Main readiness flag (backward compatible)
  level: 'none' | 'partial' | 'complete'; // Progressive readiness level
  progress: number;                  // 0-1 completion percentage
  
  // Detailed information
  missingKeys: string[];            // Keys still loading/undefined
  availableKeys: string[];          // Keys that are ready
  staleness: Record<string, number>; // Data age in milliseconds
  waitTimeElapsed: number;          // Time since readiness checking started
  
  // Individual signals (for debugging)
  routeReady: boolean;              // React Router navigation idle
  loadersReady: boolean;            // Async operations complete
  storeReady: boolean;              // Store keys available
  requiredKeys: string[];           // All monitored keys
}
```

## Best Practices

### 1. **Start with Default Behavior**
```typescript
// Begin with existing patterns
const { ready } = useDataReadiness({ /* existing options */ });
```

### 2. **Add Progressive Features Gradually**
```typescript
// Then enhance with progressive features
const readinessState = useDataReadiness({
  // ... existing options
  progressiveReadiness: true,
  debug: true, // Enable logging during development
});
```

### 3. **Monitor Performance**
```typescript
// Use debug mode to understand timing
useEffect(() => {
  if (readinessState.waitTimeElapsed > 5000) {
    console.warn('Slow data loading detected:', readinessState.missingKeys);
  }
}, [readinessState.waitTimeElapsed, readinessState.missingKeys]);
```

### 4. **Handle Edge Cases**
```typescript
// Graceful handling of missing data
if (readinessState.ready) {
  return <Component />;
} else if (readinessState.waitTimeElapsed > maxWaitTimeMs * 0.8) {
  return <TimeoutWarning missingKeys={readinessState.missingKeys} />;
} else {
  return <LoadingSpinner progress={readinessState.progress} />;
}
```

## Migration Guide

### From Basic to Enhanced Usage

1. **No Changes Required**: Existing code continues to work
2. **Optional Enhancement**: Add progressive features as needed
3. **Gradual Adoption**: Enable features component by component
4. **Performance Monitoring**: Use debug mode to optimize timing

### Common Migration Patterns

```typescript
// Before: Basic usage
const { ready } = useDataReadiness({ componentName: 'MyComp' });

// After: Enhanced with progressive features
const readinessState = useDataReadiness({
  componentName: 'MyComp',
  progressiveReadiness: true,
  minWaitTimeMs: 300,
  maxWaitTimeMs: 10000,
});

// Backward compatible: still use ready flag
if (!readinessState.ready) return <Loader />;

// Or use enhanced features
if (readinessState.level === 'partial') {
  return <PartialComponent progress={readinessState.progress} />;
}
```
