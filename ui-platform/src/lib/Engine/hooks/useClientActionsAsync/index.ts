// Main export file for the refactored useClientActionsAsync module
// This maintains backward compatibility while providing access to all new modular components

// Export the main hook
export { useClientActionsAsync as useClientActionV3 } from './hooks/useClientActionsV3';

// Export types for consumers who need them
export type {
  ActionConfig,
  ActionContext,
  ActionExecutionOptions,
  BatchExecutor,
  StateSynchronizer,
  UseClientActionsAsyncProps,
  UseClientActionsAsyncReturn,
} from './types';

// Export utilities for advanced usage
export {
  createErrorHandler,
  evaluateExpression,
  validateConfig,
} from './utils';

// Export action handlers for custom implementations
export { createActionRegistry } from './handlers';

export {
  createBatchExecutor,
  executeAction,
  executeActionSync,
  executeActionWithLoaderManagement,
  useClientActionsAsync as useClientActionAsync,
  useClientAction as useClientActionV1,
  useClientActionAsync as useClientActionV2,
} from './hooks';

// Export context management
export { createStateSynchronizer } from './context';
