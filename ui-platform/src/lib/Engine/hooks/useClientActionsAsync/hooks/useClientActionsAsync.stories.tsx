import type { Meta, StoryObj } from '@storybook/react';
import { MultipleAsyncActionsExamples, FormSubmissionExample } from './useClientActionsAsync.examples';

const meta: Meta<typeof MultipleAsyncActionsExamples> = {
  component: MultipleAsyncActionsExamples,
  title: 'Engine/Hooks/useClientActionsAsync',
};

export default meta;
type Story = StoryObj<typeof MultipleAsyncActionsExamples>;

export const Examples: Story = {
  args: {},
};

export const FormSubmission: Story = {
  render: () => <FormSubmissionExample />,
};