import React, { useState } from 'react';
import { TemplateLiteralLogger } from '../../../../Utilities';
import { ActionConfig } from '../../../models/action.config';
import { useClientActionsAsync } from './useClientActionsV3';

// instantiate logger for debugging generate control component
const log = TemplateLiteralLogger.createLog(
  {
    prefix: '🪵[Use Client Actions Async log]:',
    enabled: true,
    options: { style: { backgroundColor: '#efefef', color: '#017812' } },
  },
  'log'
);

/**
 * Comprehensive examples demonstrating the refactored callClientAction factory function
 * and different ways to handle multiple async actions
 */
export function MultipleAsyncActionsExamples() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const {
    callClientAction,
    callClientActionAsync,
    callClientActionsSequentially,
    callClientActionsConcurrently,
    callClientActionsWithLimit,
    withAsync,
    createBatch,
  } = useClientActionsAsync({
    navigate: () => {
      log`navigating...`;
    },
    location: { search: '' } as any,
  });

  // Example action configurations
  const actionConfigs: ActionConfig[] = [
    {
      type: 'clientAction',
      action: 'timeout',
      payload: [
        4000,
        [
          {
            type: 'clientAction',
            action: 'log',
            payload: ['Action 1: Starting process'],
          },
        ],
      ],
    },
    {
      type: 'clientAction',
      action: 'timeout',
      payload: [
        2000,
        [
          {
            type: 'clientAction',
            action: 'log',
            payload: ['Action 2: Processing data'],
          },
        ],
      ],
    },
    {
      type: 'clientAction',
      action: 'timeout',
      payload: [
        6000,
        [
          {
            type: 'clientAction',
            action: 'log',
            payload: ['Action 3: Saving results'],
          },
        ],
      ],
    },
    {
      type: 'clientAction',
      action: 'timeout',
      payload: [
        5000,
        [
          {
            type: 'clientAction',
            action: 'log',
            payload: ['Action 4: Cleanup'],
          },
        ],
      ],
    },
  ];

  // ❌ PROBLEMATIC: Concurrent execution without await
  const handleProblematicConcurrent = () => {
    setResult('');
    setLoading(true);

    // These will all start executing immediately and concurrently
    // This can cause race conditions and unpredictable behavior
    callClientActionAsync(actionConfigs[0]);
    callClientActionAsync(actionConfigs[1]);
    callClientActionAsync(actionConfigs[2]);
    callClientActionAsync(actionConfigs[3]);

    // Loading will be set to false immediately, before actions complete!
    setLoading(false);
    setResult('All actions started (but may not be finished!)');
  };

  // ✅ SOLUTION 1: Sequential execution with manual await
  const handleSequentialManual = async () => {
    setResult('');
    setLoading(true);

    try {
      // Execute one after another
      await callClientActionAsync(actionConfigs[0]);
      await callClientActionAsync(actionConfigs[1]);
      await callClientActionAsync(actionConfigs[2]);
      await callClientActionAsync(actionConfigs[3]);

      setResult('Sequential actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 2: Sequential execution with helper function
  const handleSequentialHelper = async () => {
    setResult('');
    setLoading(true);

    try {
      await callClientActionsSequentially(actionConfigs);
      setResult('Sequential actions (helper) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 3: Concurrent execution (when safe)
  const handleConcurrentSafe = async () => {
    setResult('');
    setLoading(true);

    try {
      // Safe for independent actions that don't affect the same state
      await callClientActionsConcurrently(actionConfigs);
      setResult('Concurrent actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 4: Controlled concurrency
  const handleControlledConcurrency = async () => {
    setResult('');
    setLoading(true);

    try {
      // Execute max 2 actions at a time
      await callClientActionsWithLimit(actionConfigs, 2);
      setResult('Controlled concurrency actions completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 5: Mixed approach with error handling
  const handleMixedApproach = async () => {
    setResult('');
    setLoading(true);

    try {
      // Critical actions that must be sequential
      await callClientActionsSequentially([
        actionConfigs[0], // Initialize
        actionConfigs[1], // Setup
      ]);

      // Independent actions that can run concurrently
      await callClientActionsConcurrently([
        actionConfigs[2], // Save data
        actionConfigs[3], // Send notification
      ]);

      setResult('Mixed approach completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ SOLUTION 6: Promise.allSettled for partial failure handling
  const handlePartialFailures = async () => {
    setResult('');
    setLoading(true);

    try {
      const promises = actionConfigs.map((config) =>
        callClientActionAsync(config)
      );
      const results = await Promise.allSettled(promises);

      const successful = results.filter((r) => r.status === 'fulfilled').length;
      const failed = results.filter((r) => r.status === 'rejected').length;

      setResult(`Completed: ${successful} successful, ${failed} failed`);
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // ✅ NEW: Using the refactored callClientAction factory function
  const handleFactorySequential = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 2: Array with async=true (sequential execution)
      await callClientAction(actionConfigs, true);
      setResult('Factory function (sequential) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactoryConcurrent = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 3: Array with async=false/undefined (concurrent execution)
      await callClientAction(actionConfigs, false);
      setResult('Factory function (concurrent) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactoryWithLimit = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 1: Array with concurrency limit
      await callClientAction(actionConfigs, false, 2);
      setResult(
        'Factory function (concurrency limit 2) completed successfully'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactorySingleAsync = async () => {
    setResult('');
    setLoading(true);

    try {
      // Case 5: Single action with async parameter override
      await callClientAction(actionConfigs[0], true);
      setResult('Factory function (single async) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleFactorySingleSync = () => {
    setResult('');
    setLoading(true);

    try {
      // Case 6: Single action - synchronous execution (default)
      callClientAction(actionConfigs[0]);
      setResult('Factory function (single sync) completed successfully');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add a new example that clearly demonstrates the issue and solution
  const handleSequentialOrderTest = async () => {
    setResult('');
    setLoading(true);

    // Create actions with different durations but in a specific order
    const orderedActions: ActionConfig[] = [
      {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          3000, // 3 seconds - should complete third
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['1️⃣ First action (3s)'],
            },
          ],
        ],
      },
      {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          1000, // 1 second - should complete first if concurrent
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['2️⃣ Second action (1s)'],
            },
          ],
        ],
      },
      {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          2000, // 2 seconds - should complete second if concurrent
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['3️⃣ Third action (2s)'],
            },
          ],
        ],
      },
    ];

    try {
      // This should execute in order: 1️⃣, 2️⃣, 3️⃣ regardless of duration
      log`🔄 Starting sequential execution test...`;
      await callClientActionsSequentially(orderedActions);
      setResult(
        'Sequential order test completed - check console for execution order'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add a new example for API call sequence testing
  const handleApiSequenceTest = async () => {
    setResult('');
    setLoading(true);

    // Create actions with API calls in a specific order
    const apiActions: ActionConfig[] = [
      // First API call - takes ~1-2 seconds
      {
        type: 'clientAction',
        action: 'submitAndFetch',
        payload: [
          {}, // No form data needed for GET
          {
            url: 'https://jsonplaceholder.typicode.com/posts/1',
            headers: { 'Content-Type': 'application/json' },
          },
          { method: 'get' },
        ],
      },
      // Second API call - logs when first is complete
      {
        type: 'clientAction',
        action: 'log',
        payload: ['✅ First API call completed'],
      },
      // Third API call - takes ~1 second
      {
        type: 'clientAction',
        action: 'submitAndFetch',
        payload: [
          {}, // No form data needed for GET
          {
            url: 'https://jsonplaceholder.typicode.com/users/1',
            headers: { 'Content-Type': 'application/json' },
          },
          { method: 'get' },
        ],
      },
      // Fourth - logs when second API call is complete
      {
        type: 'clientAction',
        action: 'log',
        payload: ['✅ Second API call completed'],
      },
      // Fifth - navigation action (mocked)
      {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/success-page', { replace: true }],
      },
      // Sixth - logs when navigation is complete
      {
        type: 'clientAction',
        action: 'log',
        payload: ['✅ Navigation completed'],
      },
    ];

    try {
      log`🔄 Starting API sequence test...`;
      await callClientActionsSequentially(apiActions);
      setResult(
        'API sequence test completed - check console for execution order'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add a test for submitAsync with multiple calls
  const handleSubmitAsyncTest = async () => {
    setResult('');
    setLoading(true);

    // Create a submitAsync action with multiple API calls
    const submitAsyncAction: ActionConfig = {
      type: 'clientAction',
      action: 'submitAsync',
      payload: {
        calls: [
          {
            key: 'posts',
            url: 'https://jsonplaceholder.typicode.com/posts?_limit=3',
            data: {},
          },
          {
            key: 'users',
            url: 'https://jsonplaceholder.typicode.com/users?_limit=2',
            data: {},
          },
          {
            key: 'comments',
            url: 'https://jsonplaceholder.typicode.com/comments?_limit=3',
            data: {},
          },
        ],
        onFinish: {
          type: 'clientAction',
          action: 'log',
          payload: ['✅ All submitAsync calls completed'],
        },
      },
    };

    try {
      log`🔄 Starting submitAsync test...`;
      await callClientActionAsync(submitAsyncAction);
      setResult(
        'submitAsync test completed - check console for execution order'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add a mixed test with timeouts and API calls
  const handleMixedApiTimeoutTest = async () => {
    setResult('');
    setLoading(true);

    // Create a mix of timeout and API actions
    const mixedActions: ActionConfig[] = [
      // First - timeout (3s)
      {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          3000,
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['⏱️ 3s timeout completed'],
            },
          ],
        ],
      },
      // Second - API call
      {
        type: 'clientAction',
        action: 'submitAndFetch',
        payload: [
          {},
          {
            url: 'https://jsonplaceholder.typicode.com/todos/1',
            headers: { 'Content-Type': 'application/json' },
          },
          { method: 'get' },
        ],
      },
      // Third - short timeout (1s)
      {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          1000,
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['⏱️ 1s timeout completed'],
            },
          ],
        ],
      },
      // Fourth - another API call
      {
        type: 'clientAction',
        action: 'submitAndFetch',
        payload: [
          {},
          {
            url: 'https://jsonplaceholder.typicode.com/photos/1',
            headers: { 'Content-Type': 'application/json' },
          },
          { method: 'get' },
        ],
      },
    ];

    try {
      log`🔄 Starting mixed API/timeout test...`;
      await callClientActionsSequentially(mixedActions);
      setResult(
        'Mixed API/timeout test completed - check console for execution order'
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add examples for withAsync helper
  const handleWithAsyncSequential = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using withAsync with sequential mode (default)
      log`🔄 Starting withAsync sequential test...`;
      await withAsync(actionConfigs, { mode: 'sequential' });
      setResult('withAsync sequential test completed');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleWithAsyncConcurrent = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using withAsync with concurrent mode
      log`🔄 Starting withAsync concurrent test...`;
      await withAsync(actionConfigs, { mode: 'concurrent' });
      setResult('withAsync concurrent test completed');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleWithAsyncLimit = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using withAsync with concurrent mode and concurrency limit
      log`🔄 Starting withAsync concurrency limit test...`;
      await withAsync(actionConfigs, {
        mode: 'concurrent',
        concurrencyLimit: 2,
      });
      setResult('withAsync concurrency limit test completed');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleWithAsyncSingle = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using withAsync with a single action
      log`🔄 Starting withAsync single action test...`;
      await withAsync(actionConfigs[0]);
      setResult('withAsync single action test completed');
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add examples for createBatch helper
  const handleCreateBatchSequential = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using createBatch with sequential execution
      log`🔄 Starting createBatch sequential test...`;
      const batch = createBatch();

      // Add actions to the batch
      actionConfigs.forEach((config) => batch.add(config));

      // Log batch size
      log`📦 Batch size: ${batch.size()}`;

      // Execute batch sequentially
      await batch.execute({ mode: 'sequential' });

      setResult(
        `createBatch sequential test completed (${batch.size()} actions)`
      );

      // Clear batch
      batch.clear();
      log`🧹 Batch cleared, new size: ${batch.size()}`;
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBatchConcurrent = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using createBatch with concurrent execution
      log`🔄 Starting createBatch concurrent test...`;
      const batch = createBatch();

      // Add actions to the batch
      actionConfigs.forEach((config) => batch.add(config));

      // Execute batch concurrently
      await batch.execute({ mode: 'concurrent' });

      setResult(
        `createBatch concurrent test completed (${batch.size()} actions)`
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBatchLimit = async () => {
    setResult('');
    setLoading(true);

    try {
      // Using createBatch with concurrency limit
      log`🔄 Starting createBatch concurrency limit test...`;
      const batch = createBatch();

      // Add actions to the batch
      actionConfigs.forEach((config) => batch.add(config));

      // Execute batch with concurrency limit
      await batch.execute({ mode: 'concurrent', concurrencyLimit: 2 });

      setResult(
        `createBatch concurrency limit test completed (${batch.size()} actions)`
      );
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add example for dynamic batch creation
  const handleDynamicBatch = async () => {
    setResult('');
    setLoading(true);

    try {
      // Create a batch dynamically based on conditions
      log`🔄 Starting dynamic batch creation test...`;
      const batch = createBatch();

      // Add actions conditionally
      actionConfigs.forEach((config, index) => {
        // Only add even-indexed actions
        if (index % 2 === 0) {
          log`➕ Adding action ${index + 1} to batch`;
          batch.add(config);
        } else {
          log`⏭️ Skipping action ${index + 1}`;
        }
      });

      // Execute the dynamic batch
      await batch.execute({ mode: 'sequential' });

      setResult(`Dynamic batch test completed (${batch.size()} actions)`);
    } catch (error) {
      setResult(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // Add example for error handling with batch
  const handleBatchWithError = async () => {
    setResult('');
    setLoading(true);

    try {
      // Create a batch with an action that will fail
      log`🔄 Starting batch with error test...`;
      const batch = createBatch();

      // Add normal actions
      batch.add(actionConfigs[0]);
      batch.add(actionConfigs[1]);

      // Add an action that will fail
      batch.add({
        type: 'clientAction',
        action: 'nonExistentAction', // This action doesn't exist
        payload: ['This will fail'],
      });

      // Add more actions after the failing one
      batch.add(actionConfigs[2]);

      // Execute batch and handle error
      await batch.execute({ mode: 'sequential' });

      // This line should not be reached if error handling works
      setResult('Batch completed without errors (unexpected)');
    } catch (error) {
      // This is the expected path
      log`❌ Caught error in batch execution: ${error}`;
      setResult(`Batch error handling test completed successfully`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2>Multiple Async Actions Examples</h2>

      <div
        style={{
          marginBottom: '30px',
          padding: '15px',
          backgroundColor: '#2d3033',
          borderRadius: '5px',
        }}
      >
        <h3>📚 Understanding Client Action Functions</h3>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <th style={{ textAlign: 'left', padding: '8px' }}>Function</th>
              <th style={{ textAlign: 'left', padding: '8px' }}>Description</th>
              <th style={{ textAlign: 'left', padding: '8px' }}>Best For</th>
            </tr>
          </thead>
          <tbody>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>callClientAction</code>
              </td>
              <td style={{ padding: '8px' }}>
                Unified factory function that handles all execution patterns
              </td>
              <td style={{ padding: '8px' }}>
                General purpose, simplifies API by providing a single entry
                point
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>callClientActionAsync</code>
              </td>
              <td style={{ padding: '8px' }}>
                Always returns a Promise for a single action
              </td>
              <td style={{ padding: '8px' }}>
                When you need to await a single action's completion
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>callClientActionsSequentially</code>
              </td>
              <td style={{ padding: '8px' }}>
                Executes actions one after another in order
              </td>
              <td style={{ padding: '8px' }}>
                When order matters or actions modify the same state
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>callClientActionsConcurrently</code>
              </td>
              <td style={{ padding: '8px' }}>
                Executes all actions at the same time
              </td>
              <td style={{ padding: '8px' }}>
                Independent actions for maximum performance
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>callClientActionsWithLimit</code>
              </td>
              <td style={{ padding: '8px' }}>
                Executes actions with a concurrency limit
              </td>
              <td style={{ padding: '8px' }}>
                Balancing performance with resource constraints
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>withAsync</code>
              </td>
              <td style={{ padding: '8px' }}>
                Flexible helper with execution mode options
              </td>
              <td style={{ padding: '8px' }}>
                When you need to switch execution modes dynamically
              </td>
            </tr>
            <tr style={{ borderBottom: '1px solid #ddd' }}>
              <td style={{ padding: '8px' }}>
                <code>createBatch</code>
              </td>
              <td style={{ padding: '8px' }}>
                Creates a batch executor for collecting actions
              </td>
              <td style={{ padding: '8px' }}>
                When actions need to be collected over time before execution
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div
        style={{
          marginBottom: '30px',
          padding: '15px',
          backgroundColor: '#2d3033',
          borderRadius: '5px',
        }}
      >
        <h3>🔍 Common Scenarios & Recommended Approaches</h3>
        <ul style={{ paddingLeft: '20px' }}>
          <li style={{ marginBottom: '10px' }}>
            <strong>Form Submission Workflow:</strong> Use{' '}
            <code>callClientActionsSequentially</code> when you need to
            validate, submit, and navigate in a specific order.
          </li>
          <li style={{ marginBottom: '10px' }}>
            <strong>Loading Dashboard Data:</strong> Use{' '}
            <code>callClientActionsConcurrently</code> to load multiple
            independent data sources simultaneously.
          </li>
          <li style={{ marginBottom: '10px' }}>
            <strong>Processing Large Datasets:</strong> Use{' '}
            <code>callClientActionsWithLimit</code> to process many items
            without overwhelming resources.
          </li>
          <li style={{ marginBottom: '10px' }}>
            <strong>Dynamic Workflows:</strong> Use <code>withAsync</code> when
            execution mode needs to be determined at runtime.
          </li>
          <li style={{ marginBottom: '10px' }}>
            <strong>User-Initiated Batch Operations:</strong> Use{' '}
            <code>createBatch</code> when users select multiple items for
            processing.
          </li>
          <li style={{ marginBottom: '10px' }}>
            <strong>Simple Single Actions:</strong> Use{' '}
            <code>callClientAction</code> for straightforward cases with a
            single action.
          </li>
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>❌ Problematic Approach</h3>
        <button onClick={handleProblematicConcurrent} disabled={loading}>
          Concurrent Without Await (Problematic)
        </button>
        <p>
          <strong>Issues:</strong> Race conditions, immediate completion,
          unpredictable order
        </p>
        <p>
          <strong>Why it's problematic:</strong> Actions start executing but the
          function returns immediately, before actions complete. This can lead
          to state inconsistencies and timing issues.
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Sequential Solutions</h3>
        <button onClick={handleSequentialManual} disabled={loading}>
          Sequential (Manual Await)
        </button>
        <button onClick={handleSequentialHelper} disabled={loading}>
          Sequential (Helper Function)
        </button>
        <p>
          <strong>Use when:</strong> Actions depend on each other or modify the
          same state
        </p>
        <p>
          <strong>Benefits:</strong> Predictable order, prevents race
          conditions, ensures each action completes before the next starts
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Concurrent Solutions</h3>
        <button onClick={handleConcurrentSafe} disabled={loading}>
          Concurrent (All at Once)
        </button>
        <button onClick={handleControlledConcurrency} disabled={loading}>
          Controlled Concurrency (Limit 2)
        </button>
        <p>
          <strong>Use when:</strong> Actions are independent and don't conflict
          with each other
        </p>
        <p>
          <strong>Benefits:</strong> Faster overall execution, better
          performance for independent operations
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>✅ Advanced Solutions</h3>
        <button onClick={handleMixedApproach} disabled={loading}>
          Mixed Approach
        </button>
        <button onClick={handlePartialFailures} disabled={loading}>
          Handle Partial Failures
        </button>
        <p>
          <strong>Use when:</strong> Complex workflows with different
          requirements or potential failures
        </p>
        <p>
          <strong>Benefits:</strong> Combines the advantages of sequential and
          concurrent execution, provides graceful degradation when some actions
          fail
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🆕 Refactored Factory Function Examples</h3>
        <button onClick={handleFactorySequential} disabled={loading}>
          Factory: Sequential (async=true)
        </button>
        <button onClick={handleFactoryConcurrent} disabled={loading}>
          Factory: Concurrent (async=false)
        </button>
        <button onClick={handleFactoryWithLimit} disabled={loading}>
          Factory: Concurrency Limit (limit=2)
        </button>
        <button onClick={handleFactorySingleAsync} disabled={loading}>
          Factory: Single Async
        </button>
        <button onClick={handleFactorySingleSync} disabled={loading}>
          Factory: Single Sync
        </button>
        <p>
          <strong>Use when:</strong> You want a unified interface for all
          execution patterns
        </p>
        <p>
          <strong>Benefits:</strong> Simplified API, consistent behavior,
          flexible configuration
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 Order Test</h3>
        <button onClick={handleSequentialOrderTest} disabled={loading}>
          Test Sequential Order
        </button>
        <p>Tests if actions execute in order regardless of duration</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 API Sequence Test</h3>
        <button onClick={handleApiSequenceTest} disabled={loading}>
          Test API Sequence
        </button>
        <p>Tests if API calls execute in order regardless of duration</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 SubmitAsync Test</h3>
        <button onClick={handleSubmitAsyncTest} disabled={loading}>
          Test SubmitAsync
        </button>
        <p>Tests if submitAsync executes multiple API calls in order</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 Mixed API/Timeout Test</h3>
        <button onClick={handleMixedApiTimeoutTest} disabled={loading}>
          Test Mixed API/Timeout
        </button>
        <p>Tests if timeouts and API calls execute in order</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 withAsync Examples</h3>
        <button onClick={handleWithAsyncSequential} disabled={loading}>
          withAsync Sequential
        </button>
        <button onClick={handleWithAsyncConcurrent} disabled={loading}>
          withAsync Concurrent
        </button>
        <button onClick={handleWithAsyncLimit} disabled={loading}>
          withAsync Concurrency Limit
        </button>
        <button onClick={handleWithAsyncSingle} disabled={loading}>
          withAsync Single Action
        </button>
        <p>Examples demonstrating the withAsync helper function</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🧪 createBatch Examples</h3>
        <button onClick={handleCreateBatchSequential} disabled={loading}>
          createBatch Sequential
        </button>
        <button onClick={handleCreateBatchConcurrent} disabled={loading}>
          createBatch Concurrent
        </button>
        <button onClick={handleCreateBatchLimit} disabled={loading}>
          createBatch Concurrency Limit
        </button>
        <button onClick={handleDynamicBatch} disabled={loading}>
          Dynamic Batch Creation
        </button>
        <button onClick={handleBatchWithError} disabled={loading}>
          Batch Error Handling
        </button>
        <p>Examples demonstrating the createBatch helper function</p>
      </div>

      <div
        style={{
          marginBottom: '30px',
          padding: '15px',
          backgroundColor: '#2d3033',
          borderRadius: '5px',
        }}
      >
        <h3>📝 Code Samples</h3>

        <div style={{ marginBottom: '20px' }}>
          <h4>Sequential Execution</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Sequential execution with callClientActionsSequentially
const handleSequentialActions = async () => {
  const actions = [
    { type: 'clientAction', action: 'log', payload: ['Step 1'] },
    { type: 'clientAction', action: 'log', payload: ['Step 2'] },
    { type: 'clientAction', action: 'log', payload: ['Step 3'] }
  ];
  
  // Actions will execute in order, waiting for each to complete
  await callClientActionsSequentially(actions);
  console.log('All actions completed in sequence');
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Concurrent Execution</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Concurrent execution with callClientActionsConcurrently
const handleConcurrentActions = async () => {
  const actions = [
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* ... */] },
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* ... */] },
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* ... */] }
  ];
  
  // All actions start at the same time
  await callClientActionsConcurrently(actions);
  console.log('All concurrent actions completed');
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Controlled Concurrency</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Controlled concurrency with callClientActionsWithLimit
const handleLimitedConcurrency = async () => {
  const actions = [
    /* 10 different API calls or operations */
  ];
  
  // Only 3 actions will run at a time
  await callClientActionsWithLimit(actions, 3);
  console.log('All actions completed with controlled concurrency');
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Single Action (Async)</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Single action with callClientActionAsync
const handleSingleAction = async () => {
  const action = { 
    type: 'clientAction', 
    action: 'submitAndFetch', 
    payload: [/* ... */] 
  };
  
  // Returns a promise that resolves when the action completes
  await callClientActionAsync(action);
  console.log('Single action completed');
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Using withAsync Helper</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Using withAsync for flexible execution patterns
const handleWithAsyncExample = async () => {
  const actions = [/* array of actions */];
  
  // Sequential execution (default)
  await withAsync(actions);
  
  // Concurrent execution
  await withAsync(actions, { mode: 'concurrent' });
  
  // Concurrent with limit
  await withAsync(actions, { mode: 'concurrent', concurrencyLimit: 2 });
  
  // Single action shorthand
  await withAsync(actions[0]);
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Using createBatch Helper</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Using createBatch for collecting and executing actions
const handleBatchExample = async () => {
  // Create a new batch
  const batch = createBatch();
  
  // Add actions to the batch over time
  batch.add({ type: 'clientAction', action: 'log', payload: ['Action 1'] });
  
  // Later in the code...
  if (someCondition) {
    batch.add({ type: 'clientAction', action: 'log', payload: ['Action 2'] });
  }
  
  // Even later...
  userSelectedItems.forEach(item => {
    batch.add({ 
      type: 'clientAction', 
      action: 'submitAndFetch', 
      payload: [{ id: item.id }, /* ... */] 
    });
  });
  
  // Execute all collected actions
  await batch.execute({ mode: 'sequential' });
  console.log(\`Completed \${batch.size()} actions\`);
  
  // Clear the batch for reuse
  batch.clear();
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Mixed Approach Example</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Mixed approach combining sequential and concurrent execution
const handleMixedApproach = async () => {
  // First, run these actions in sequence
  await callClientActionsSequentially([
    { type: 'clientAction', action: 'log', payload: ['Validation step'] },
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* validation */] }
  ]);
  
  // Then, run these actions concurrently
  await callClientActionsConcurrently([
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* load data 1 */] },
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* load data 2 */] },
    { type: 'clientAction', action: 'submitAndFetch', payload: [/* load data 3 */] }
  ]);
  
  // Finally, run these actions in sequence again
  await callClientActionsSequentially([
    { type: 'clientAction', action: 'log', payload: ['Processing complete'] },
    { type: 'clientAction', action: 'navigate', payload: ['/success'] }
  ]);
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Error Handling Example</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Error handling with async actions
const handleWithErrorHandling = async () => {
  try {
    await callClientActionsSequentially([
      { type: 'clientAction', action: 'submitAndFetch', payload: [/* ... */] },
      { type: 'clientAction', action: 'nonExistentAction', payload: [/* ... */] }
    ]);
  } catch (error) {
    console.error('An action failed:', error);
    
    // Perform recovery actions
    await callClientAction(
      { type: 'clientAction', action: 'log', payload: ['Recovering from error'] }
    );
  }
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>Factory Function Example</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Using the unified factory function
const handleWithFactory = async () => {
  const actions = [/* array of actions */];
  
  // Sequential execution
  await callClientAction(actions, { async: true, mode: 'sequential' });
  
  // Concurrent execution
  await callClientAction(actions, { async: true, mode: 'concurrent' });
  
  // Concurrent with limit
  await callClientAction(actions, { 
    async: true, 
    mode: 'concurrent', 
    concurrencyLimit: 3 
  });
  
  // Single action (async)
  await callClientAction(actions[0], { async: true });
  
  // Single action (sync)
  callClientAction(actions[0], { async: false });
};`}
          </pre>
        </div>

        <div style={{ marginBottom: '20px' }}>
          <h4>submitAsync Example</h4>
          <pre
            style={{
              backgroundColor: '#2c2c2c',
              border: '1px solid #f1f1f1',
              padding: '10px',
              borderRadius: '4px',
              overflowX: 'auto',
            }}
          >
            {`// Using submitAsync for multiple API calls
const handleSubmitAsync = async () => {
  const submitAsyncAction = {
    type: 'clientAction',
    action: 'submitAsync',
    payload: {
      calls: [
        {
          key: 'userData',
          url: '/api/users/profile',
          data: { userId: 123 }
        },
        {
          key: 'preferences',
          url: '/api/users/preferences',
          data: { userId: 123 }
        }
      ],
      onFinish: {
        type: 'clientAction',
        action: 'navigate',
        payload: ['/dashboard']
      }
    }
  };
  
  await callClientActionAsync(submitAsyncAction);
};`}
          </pre>
        </div>
      </div>

      {loading && <div>Loading...</div>}

      {result && (
        <div>
          <h3>Result:</h3>
          <pre>{result}</pre>
        </div>
      )}
    </div>
  );
}

/**
 * Real-world example: Form submission with multiple steps
 */
export function FormSubmissionExample() {
  const { callClientActionsSequentially, callClientActionAsync } =
    useClientActionsAsync({
      navigate: () => {
        log`navigating...`;
      },
      location: { search: '' } as any,
    });

  const handleFormSubmission = async () => {
    try {
      // Step 1: Validate form (must be first)
      await callClientActionAsync({
        type: 'clientAction',
        action: 'log',
        payload: ['Validating form...'],
      });

      // Step 2: Submit data sequentially (order matters)
      await callClientActionsSequentially([
        {
          type: 'clientAction',
          action: 'submitAndFetch',
          payload: [
            { formData: 'user details' },
            { url: '/api/users' },
            { method: 'post' },
          ],
        },
        {
          type: 'clientAction',
          action: 'submitAndFetch',
          payload: [
            { formData: 'preferences' },
            { url: '/api/preferences' },
            { method: 'post' },
          ],
        },
      ]);

      // Step 3: Navigate to success page
      await callClientActionAsync({
        type: 'clientAction',
        action: 'navigate',
        payload: ['/success'],
      });
    } catch (error) {
      // Handle error
      await callClientActionAsync({
        type: 'clientAction',
        action: 'navigate',
        payload: ['/error'],
      });
    }
  };

  return (
    <button onClick={handleFormSubmission}>
      Submit Form (Sequential Steps)
    </button>
  );
}
