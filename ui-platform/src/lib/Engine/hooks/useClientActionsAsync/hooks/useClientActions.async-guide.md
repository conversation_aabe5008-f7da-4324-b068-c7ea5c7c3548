# Multiple Async Actions Guide

## The Problem

When you call multiple `callClientActionAsync` functions sequentially without `await`, they execute **concurrently** (in parallel), not sequentially:

```typescript
// ❌ PROBLEMATIC: These all start immediately
callClientActionAsync(actionConfig1);
callClientActionAsync(actionConfig2);
callClientActionAsync(actionConfig3);
callClientActionAsync(actionConfig4);
```

### Issues This Causes:

1. **Race Conditions**: Actions may complete in unpredictable order
2. **State Conflicts**: Multiple actions modifying the same state simultaneously
3. **Resource Contention**: Multiple API calls hitting the server at once
4. **Loading State Issues**: Loading indicators may complete before actions finish
5. **Error Handling**: Difficult to handle errors from multiple concurrent operations

## Solutions

### 🆕 Refactored Factory Function (Recommended)

The `callClientAction` function has been refactored to handle all execution patterns through a single, unified interface:

```typescript
const { callClientAction } = useClientAction({...});

// Case 1: Array with concurrency limit (async execution with controlled concurrency)
await callClientAction(actionArray, false, 2); // limit to 2 concurrent actions

// Case 2: Array with async=true (sequential async execution)
await callClientAction(actionArray, true);

// Case 3: Array with async=false or undefined (concurrent execution - default for arrays)
await callClientAction(actionArray); // or callClientAction(actionArray, false)

// Case 4: Single action with explicit async property in config
await callClientAction({ ...actionConfig, async: true });

// Case 5: Single action with async parameter override
await callClientAction(actionConfig, true);

// Case 6: Single action - synchronous execution (default for single actions)
callClientAction(actionConfig); // returns undefined, executes synchronously
```

**Benefits of the Factory Function:**
- **Unified Interface**: One function handles all execution patterns
- **Clear Precedence**: concurrencyLimit > async parameter > config.async property
- **Type Safety**: Proper TypeScript support for all cases
- **Backward Compatibility**: Existing code continues to work

### 1. Sequential Execution (Manual)

```typescript
// ✅ Execute one after another
const handleSequential = async () => {
  try {
    await callClientActionAsync(actionConfig1);
    await callClientActionAsync(actionConfig2);
    await callClientActionAsync(actionConfig3);
    await callClientActionAsync(actionConfig4);
  } catch (error) {
    console.error('Action failed:', error);
  }
};
```

**Use when**: Actions depend on each other or modify the same state.

### 2. Sequential Execution (Helper Function)

```typescript
// ✅ Use the built-in helper
const { callClientActionsSequentially } = useClientAction({...});

await callClientActionsSequentially([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
]);
```

**Use when**: You have an array of actions that must execute in order.

### 3. Concurrent Execution (Safe)

```typescript
// ✅ Execute all at once (when safe)
const { callClientActionsConcurrently } = useClientAction({...});

await callClientActionsConcurrently([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
]);
```

**Use when**: Actions are independent and don't conflict with each other.

### 4. Controlled Concurrency

```typescript
// ✅ Execute with limited concurrency
const { callClientActionsWithLimit } = useClientAction({...});

// Execute max 2 actions at a time
await callClientActionsWithLimit([
  actionConfig1,
  actionConfig2,
  actionConfig3,
  actionConfig4
], 2);
```

**Use when**: You want some parallelism but need to limit resource usage.

### 5. Mixed Approach

```typescript
// ✅ Combine sequential and concurrent as needed
const handleMixed = async () => {
  // Critical setup actions (sequential)
  await callClientActionsSequentially([
    initAction,
    setupAction
  ]);

  // Independent actions (concurrent)
  await callClientActionsConcurrently([
    saveDataAction,
    sendNotificationAction,
    updateUIAction
  ]);

  // Final cleanup (sequential)
  await callClientActionAsync(cleanupAction);
};
```

### 6. Partial Failure Handling

```typescript
// ✅ Handle cases where some actions may fail
const handlePartialFailures = async () => {
  const promises = actions.map(action => callClientActionAsync(action));
  const results = await Promise.allSettled(promises);

  const successful = results.filter(r => r.status === 'fulfilled');
  const failed = results.filter(r => r.status === 'rejected');

  console.log(`${successful.length} succeeded, ${failed.length} failed`);
};
```

## Decision Matrix

| Scenario | Solution | Reason |
|----------|----------|---------|
| Actions modify the same state | Sequential | Prevent race conditions |
| Actions depend on previous results | Sequential | Ensure proper order |
| Independent API calls | Concurrent | Faster execution |
| Large number of actions | Controlled Concurrency | Prevent server overload |
| Mix of dependent/independent | Mixed Approach | Optimize for each type |
| Some actions may fail | Partial Failure Handling | Graceful degradation |
| Dynamic action building | createBatch | Flexible collection and execution |
| Need consistent async behavior | withAsync | Always returns Promise |
| Complex execution patterns | Extended ActionConfig | Fine-grained control |
| Error-prone environments | Enhanced Error Handling | Robust execution |

## Enhanced Hook API

The enhanced `useClientActionAsync` hook now returns:

```typescript
const {
  callClientAction,                    // Original function (sync/async based on config)
  callClientActionAsync,               // Always async version
  callClientActionsSequentially,      // Execute array sequentially
  callClientActionsConcurrently,       // Execute array concurrently
  callClientActionsWithLimit,          // Execute with concurrency limit
  executeClientActionAsynchronously,   // Core async execution function
  executeClientActionSynchronously,    // Core sync execution function
  withAsync,                          // Flexible async wrapper with execution modes
  createBatch,                        // Batch executor for collecting actions
} = useClientActionAsync({...});
```

## Best Practices

1. **Always use `await`** when you need to wait for completion
2. **Choose the right execution pattern** based on your use case
3. **Handle errors appropriately** with try-catch blocks
4. **Set loading states** before starting and clear them after completion
5. **Consider user experience** - don't block the UI unnecessarily
6. **Test thoroughly** - async code can have subtle bugs
7. **Use withAsync for consistent behavior** when you always need a Promise
8. **Leverage createBatch for dynamic scenarios** when building action lists conditionally
9. **Take advantage of enhanced error handling** - the hook now gracefully handles malformed inputs
10. **Use Extended ActionConfig properties** for fine-grained control over execution
11. **Prefer specific functions over generic ones** when the use case is clear (e.g., `callClientActionsSequentially` vs `withAsync`)
12. **Monitor performance** with debug logging using the `debug` property

## Common Patterns

### Form Submission Workflow
```typescript
// Sequential: validation → submission → navigation
await callClientActionsSequentially([
  validateAction,
  submitAction,
  navigateAction
]);
```

### Data Loading
```typescript
// Concurrent: load multiple independent data sources
await callClientActionsConcurrently([
  loadUserDataAction,
  loadPreferencesAction,
  loadNotificationsAction
]);
```

### Batch Operations
```typescript
// Controlled: process large number of items
await callClientActionsWithLimit(
  items.map(item => createProcessAction(item)),
  5 // Process 5 at a time
);
```

## New Advanced Features

### 7. withAsync Function

The `withAsync` function provides a flexible way to execute actions asynchronously with different execution modes:

```typescript
// Single action - always returns a Promise
await withAsync(singleAction);

// Array with sequential execution (default)
await withAsync(actionArray, { mode: 'sequential' });

// Array with concurrent execution
await withAsync(actionArray, { mode: 'concurrent' });

// Array with controlled concurrency
await withAsync(actionArray, {
  mode: 'concurrent',
  concurrencyLimit: 3
});
```

**Benefits:**
- **Unified Interface**: Works with both single actions and arrays
- **Flexible Modes**: Choose between sequential and concurrent execution
- **Always Async**: Always returns a Promise for consistent handling
- **Type Safe**: Full TypeScript support

### 8. createBatch Function

The `createBatch` function allows you to collect actions and execute them together:

```typescript
const batch = createBatch();

// Add actions to the batch
batch.add(action1);
batch.add(action2);
batch.add(action3);

console.log(`Batch size: ${batch.size()}`); // 3

// Execute all actions sequentially
await batch.execute({ mode: 'sequential' });

// Execute all actions concurrently
await batch.execute({ mode: 'concurrent' });

// Execute with concurrency limit
await batch.execute({
  mode: 'concurrent',
  concurrencyLimit: 2
});

// Clear the batch
batch.clear();
```

**Use Cases:**
- **Dynamic Action Building**: Build action lists conditionally
- **Reusable Batches**: Create templates for common action sequences
- **Flexible Execution**: Change execution mode based on runtime conditions
- **Memory Management**: Clear batches when done

### 9. Enhanced Error Handling

The hook now provides robust error handling for malformed inputs:

```typescript
// These will now log warnings instead of throwing errors
callClientAction(null);           // ⚠️ Warning logged, action ignored
callClientAction(undefined);      // ⚠️ Warning logged, action ignored
callClientAction("invalid");      // ⚠️ Warning logged, action ignored
callClientAction({});             // ⚠️ Warning logged, action ignored

// Invalid action configurations are handled gracefully
callClientAction({
  type: 'clientAction',
  // Missing 'action' property
  payload: []
}); // ⚠️ Warning logged, action ignored
```

### 10. Extended ActionConfig Properties

Actions now support additional properties for enhanced control:

```typescript
// Basic action
const basicAction: ActionConfig = {
  type: 'clientAction',
  action: 'log',
  payload: ['Basic message']
};

// Extended action with async property
const asyncAction: ExtendedActionConfig = {
  type: 'clientAction',
  action: 'log',
  payload: ['Async message'],
  async: true,        // Forces async execution
  debug: true,        // Enables debug logging
  concurrencyLimit: 2 // Sets concurrency limit (for arrays)
};

// Use with callClientAction
callClientAction(asyncAction); // Returns Promise due to async: true
```

**Extended Properties:**
- **`async: boolean`** - Forces async execution when true
- **`debug: boolean`** - Enables debug logging for the action
- **`concurrencyLimit: number`** - Sets concurrency limit for batch operations

## Advanced Usage Patterns

### Dynamic Batch Building
```typescript
const batch = createBatch();

// Conditionally add actions based on user permissions
if (user.canEdit) {
  batch.add(editAction);
}

if (user.canDelete) {
  batch.add(deleteAction);
}

// Always add audit action
batch.add(auditAction);

// Execute based on batch size
if (batch.size() > 2) {
  await batch.execute({ mode: 'concurrent', concurrencyLimit: 2 });
} else {
  await batch.execute({ mode: 'sequential' });
}
```

### Consistent Async Patterns
```typescript
// Use withAsync when you need consistent Promise behavior
const handleUserAction = async (action: ActionConfig) => {
  setLoading(true);
  try {
    // Always returns a Promise, regardless of action type
    await withAsync(action);
    showSuccess('Action completed');
  } catch (error) {
    showError('Action failed');
  } finally {
    setLoading(false);
  }
};
```

### Error-Resilient Execution
```typescript
// The hook now handles malformed inputs gracefully
const executeUserActions = (actions: unknown[]) => {
  actions.forEach(action => {
    // These won't throw errors, just log warnings
    callClientAction(action);
  });
};
```

### Performance Monitoring
```typescript
// Use debug property for performance monitoring
const debugAction: ExtendedActionConfig = {
  type: 'clientAction',
  action: 'submitAsync',
  payload: { /* ... */ },
  debug: true, // Enables detailed logging
  async: true
};

await callClientAction(debugAction);
```
