import { act, renderHook } from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import type { Location } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { ExtendedActionConfig } from '../../../models/action.config';
import { useClientActionsAsync } from '../hooks';

// Create shared mock functions
const mockSetAsyncLoadingFn = vi.fn();

// Mock dependencies with proper paths
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'mock-data',
      setState: vi.fn(),
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
    reset: vi.fn(),
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

describe('Debug Async Loading', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  it('should debug async loading properties', async () => {
    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
        keycloak: mockKeycloak,
      })
    );

    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    const actionWithAsyncLoadStart: ExtendedActionConfig = {
      type: 'clientAction',
      action: 'log',
      payload: ['test message'],
      asyncLoadStart: true,
    };

    console.log('Action config:', JSON.stringify(actionWithAsyncLoadStart, null, 2));
    console.log('asyncLoadStart value:', actionWithAsyncLoadStart.asyncLoadStart);
    console.log('asyncLoadStart !== undefined:', actionWithAsyncLoadStart.asyncLoadStart !== undefined);

    await act(async () => {
      await result.current.callClientActionAsync(actionWithAsyncLoadStart);
    });

    console.log('mockSetAsyncLoadingFn calls:', mockSetAsyncLoadingFn.mock.calls);
    
    expect(consoleSpy).toHaveBeenCalledWith('test message');
    consoleSpy.mockRestore();
  });

  it('should test direct executeClientActionAsynchronously call', async () => {
    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
        keycloak: mockKeycloak,
      })
    );

    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    const actionWithAsyncLoadStart: ExtendedActionConfig = {
      type: 'clientAction',
      action: 'log',
      payload: ['test message'],
      asyncLoadStart: true,
    };

    await act(async () => {
      await result.current.executeClientActionAsynchronously(actionWithAsyncLoadStart);
    });

    console.log('Direct call - mockSetAsyncLoadingFn calls:', mockSetAsyncLoadingFn.mock.calls);
    
    expect(consoleSpy).toHaveBeenCalledWith('test message');
    consoleSpy.mockRestore();
  });
});
