import { act, renderHook } from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  ActionConfig,
  ExtendedActionConfig,
} from '../../../models/action.config';
import { useClientActionsAsync } from '../hooks';

// Create shared mock functions that we can track
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();
const mockSetModalState = vi.fn();
const mockAddError = vi.fn();

// Mock all dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'mock-data',
      user: { name: 'Test User' },
      setState: mockSetState,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: mockSetModalState,
  })),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: mockAddError,
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
    reset: vi.fn(),
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

describe('useClientActionsAsync - Comprehensive Async Features Test', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Core Functionality Verification', () => {
    it('should execute basic actions correctly', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const basicAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['basic test'],
      };

      await act(async () => {
        await result.current.callClientActionAsync(basicAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('basic test');
      consoleSpy.mockRestore();
    });

    it('should handle sequential execution correctly', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation((message: string) => {
        if (message.includes('1')) executionOrder.push(1);
        if (message.includes('2')) executionOrder.push(2);
        if (message.includes('3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['Action 1'] },
        { type: 'clientAction', action: 'log', payload: ['Action 2'] },
        { type: 'clientAction', action: 'log', payload: ['Action 3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      expect(executionOrder).toEqual([1, 2, 3]);
      consoleSpy.mockRestore();
    });

    it('should handle concurrent execution correctly', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation((message: string) => {
        if (message.includes('1')) executionOrder.push(1);
        if (message.includes('2')) executionOrder.push(2);
        if (message.includes('3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['Action 1'] },
        { type: 'clientAction', action: 'log', payload: ['Action 2'] },
        { type: 'clientAction', action: 'log', payload: ['Action 3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      // All actions should execute
      expect(executionOrder).toContain(1);
      expect(executionOrder).toContain(2);
      expect(executionOrder).toContain(3);
      expect(executionOrder).toHaveLength(3);
      consoleSpy.mockRestore();
    });
  });

  describe('Async Loading State Management', () => {
    it('should verify async loading functionality exists', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      // Verify the hook has the expected methods
      expect(result.current.callClientActionAsync).toBeDefined();
      expect(result.current.executeClientActionAsynchronously).toBeDefined();
      expect(result.current.callClientActionsSequentially).toBeDefined();
      expect(result.current.callClientActionsConcurrently).toBeDefined();
      expect(result.current.withAsync).toBeDefined();
    });

    it('should handle extended action configs', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const extendedAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['extended action test'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
        debug: false,
      };

      await act(async () => {
        await result.current.callClientActionAsync(extendedAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('extended action test');
      consoleSpy.mockRestore();
    });
  });

  describe('Debounce Functionality', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should handle actions without debounce immediately', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionCount = { count: 0 };
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        executionCount.count++;
      });

      const immediateAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['immediate action'],
      };

      await act(async () => {
        await result.current.callClientActionAsync(immediateAction);
      });

      expect(executionCount.count).toBe(1);
      consoleSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should handle action execution errors gracefully', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        throw new Error('Test error');
      });

      const failingAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['failing action'],
      };

      // Should not throw an error at the hook level
      await act(async () => {
        try {
          await result.current.callClientActionAsync(failingAction);
        } catch (error) {
          // Expected to catch the error
          expect(error).toBeInstanceOf(Error);
        }
      });

      console.log = originalConsoleLog;
    });
  });

  describe('Integration with Updated Components', () => {
    it('should maintain backward compatibility', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Test legacy action format
      const legacyAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['legacy test'],
      };

      await act(async () => {
        await result.current.callClientActionAsync(legacyAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('legacy test');
      consoleSpy.mockRestore();
    });
  });
});
