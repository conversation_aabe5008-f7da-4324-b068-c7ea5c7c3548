import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionsAsync } from '../hooks/';
import { ActionConfig } from '../types';

// Mock all dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn(() => ({
    setState: vi.fn(),
    getState: vi.fn(() => ({})),
  })),
}));

vi.mock('../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn(() => vi.fn()),
}));

vi.mock('../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
    trigger: vi.fn(),
  })),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(() => vi.fn()),
  useLocation: vi.fn(() => ({ search: '' })),
}));

describe('useClientActionsAsync - Backward Compatibility', () => {
  const mockNavigate = vi.fn();
  const mockLocation = { search: '' } as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize the hook successfully', () => {
    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    expect(result.current).toBeDefined();
    expect(typeof result.current.callClientAction).toBe('function');
    expect(typeof result.current.callClientActionAsync).toBe('function');
    expect(typeof result.current.callClientActionsSequentially).toBe(
      'function'
    );
    expect(typeof result.current.callClientActionsConcurrently).toBe(
      'function'
    );
    expect(typeof result.current.callClientActionsWithLimit).toBe('function');
    expect(typeof result.current.withAsync).toBe('function');
    expect(typeof result.current.createBatch).toBe('function');
  });

  it('should handle basic log action', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const logAction: ActionConfig = {
      type: 'clientAction',
      action: 'log',
      payload: ['Test message'],
    };

    await act(async () => {
      await result.current.callClientActionAsync(logAction);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Test message');
    consoleSpy.mockRestore();
  });

  it('should handle array of actions sequentially', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const actions: ActionConfig[] = [
      {
        type: 'clientAction',
        action: 'log',
        payload: ['First message'],
      },
      {
        type: 'clientAction',
        action: 'log',
        payload: ['Second message'],
      },
    ];

    await act(async () => {
      await result.current.callClientActionsSequentially(actions);
    });

    expect(consoleSpy).toHaveBeenCalledWith('First message');
    expect(consoleSpy).toHaveBeenCalledWith('Second message');
    consoleSpy.mockRestore();
  });

  it('should handle array of actions concurrently', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const actions: ActionConfig[] = [
      {
        type: 'clientAction',
        action: 'log',
        payload: ['Concurrent message 1'],
      },
      {
        type: 'clientAction',
        action: 'log',
        payload: ['Concurrent message 2'],
      },
    ];

    await act(async () => {
      await result.current.callClientActionsConcurrently(actions);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Concurrent message 1');
    expect(consoleSpy).toHaveBeenCalledWith('Concurrent message 2');
    consoleSpy.mockRestore();
  });

  it('should handle createBatch functionality', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const batch = result.current.createBatch();

    batch.add({
      type: 'clientAction',
      action: 'log',
      payload: ['Batch action 1'],
    });
    batch.add({
      type: 'clientAction',
      action: 'log',
      payload: ['Batch action 2'],
    });

    expect(batch.size()).toBe(2);

    await act(async () => {
      await batch.execute({ mode: 'sequential' });
    });

    expect(consoleSpy).toHaveBeenCalledWith('Batch action 1');
    expect(consoleSpy).toHaveBeenCalledWith('Batch action 2');

    batch.clear();
    expect(batch.size()).toBe(0);

    consoleSpy.mockRestore();
  });

  it('should handle withAsync functionality', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const actions: ActionConfig[] = [
      {
        type: 'clientAction',
        action: 'log',
        payload: ['WithAsync action 1'],
      },
      {
        type: 'clientAction',
        action: 'log',
        payload: ['WithAsync action 2'],
      },
    ];

    await act(async () => {
      await result.current.withAsync(actions, { mode: 'sequential' });
    });

    expect(consoleSpy).toHaveBeenCalledWith('WithAsync action 1');
    expect(consoleSpy).toHaveBeenCalledWith('WithAsync action 2');
    consoleSpy.mockRestore();
  });

  it('should handle conditional actions', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
      return;
    });

    const { result } = renderHook(() =>
      useClientActionsAsync({
        navigate: mockNavigate,
        location: mockLocation,
      })
    );

    const conditionalAction = {
      type: 'clientAction',
      action: 'conditional',
      payload: {
        condition: true,
        actions: {
          whenTrue: [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['Condition was true'],
            },
          ],
          whenFalse: [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['Condition was false'],
            },
          ],
        },
      },
    } satisfies ActionConfig;

    await act(async () => {
      await result.current.callClientActionAsync(conditionalAction);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Condition was true');
    expect(consoleSpy).not.toHaveBeenCalledWith('Condition was false');
    consoleSpy.mockRestore();
  });
});
