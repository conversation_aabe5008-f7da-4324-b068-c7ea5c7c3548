import { act, renderHook } from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  ActionConfig,
  ExtendedActionConfig,
} from '../../../models/action.config';
import { useClientActionsAsync } from '../hooks';

// Mock dependencies
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'mock-data',
      user: { name: 'Test User' },
      setState: vi.fn(),
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

// Create a shared mock function that we can access in tests
const mockSetAsyncLoadingFn = vi.fn();

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
    reset: vi.fn(),
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

describe('useClientActionsAsync - Async Features', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;
  let mockSetAsyncLoading: any;

  beforeEach(() => {
    vi.clearAllMocks();

    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;

    // Get the mock function for setAsyncLoading from the mocked module
    mockSetAsyncLoading = mockSetAsyncLoadingFn;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('AsyncLoadStart and AsyncLoadEnd Events', () => {
    it('should trigger asyncLoadStart when asyncLoadStart is true', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionWithAsyncLoadStart: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithAsyncLoadStart);
      });

      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
    });

    it('should trigger asyncLoadEnd when asyncLoadEnd is true', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionWithAsyncLoadEnd: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithAsyncLoadEnd);
      });

      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);
    });

    it('should handle both asyncLoadStart and asyncLoadEnd in sequence', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionWithBothLoaders: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithBothLoaders);
      });

      // Should call setAsyncLoading(true) first, then setAsyncLoading(false)
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);
      expect(mockSetAsyncLoading).toHaveBeenCalledTimes(2);
    });

    it('should not trigger loading events when asyncLoad properties are false', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionWithoutLoaders: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: false,
        asyncLoadEnd: false,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithoutLoaders);
      });

      expect(mockSetAsyncLoading).not.toHaveBeenCalled();
    });

    it('should ensure asyncLoadEnd is called even if action throws error', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      // Mock console.log to throw an error
      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        throw new Error('Test error');
      });

      const actionWithLoaders: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      try {
        await act(async () => {
          await result.current.callClientActionAsync(actionWithLoaders);
        });
      } catch (error) {
        // Expected to throw
      }

      // Should still call asyncLoadEnd even after error
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);

      // Restore console.log
      console.log = originalConsoleLog;
    });
  });

  describe('Sequential vs Concurrent Execution', () => {
    it('should execute actions sequentially by default', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        if (message.includes('Action 1')) executionOrder.push(1);
        if (message.includes('Action 2')) executionOrder.push(2);
        if (message.includes('Action 3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 1'],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 2'],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 3'],
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      expect(executionOrder).toEqual([1, 2, 3]);
      console.log = originalConsoleLog;
    });

    it('should execute actions concurrently when specified', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        if (message.includes('Action 1')) executionOrder.push(1);
        if (message.includes('Action 2')) executionOrder.push(2);
        if (message.includes('Action 3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 1'],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 2'],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['Action 3'],
        },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      // All actions should have executed (order may vary due to concurrency)
      expect(executionOrder).toContain(1);
      expect(executionOrder).toContain(2);
      expect(executionOrder).toContain(3);
      expect(executionOrder).toHaveLength(3);
      console.log = originalConsoleLog;
    });
  });

  describe('Debounce Functionality', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should debounce actions with debounce property', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionCount = { count: 0 };
      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        executionCount.count++;
      });

      const debouncedAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['debounced message'],
        debounce: 500, // 500ms debounce
      };

      // Execute the action multiple times quickly
      await act(async () => {
        result.current.callClientActionAsync(debouncedAction);
        result.current.callClientActionAsync(debouncedAction);
        result.current.callClientActionAsync(debouncedAction);
      });

      // Should not have executed yet due to debounce
      expect(executionCount.count).toBe(0);

      // Fast-forward time by 500ms
      await act(async () => {
        vi.advanceTimersByTime(500);
      });

      // Now it should have executed
      expect(executionCount.count).toBeGreaterThan(0);
      console.log = originalConsoleLog;
    });

    it('should handle different debounce values for different actions', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: string[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        executionOrder.push(message);
      });

      const shortDebounceAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['short debounce'],
        debounce: 100,
      };

      const longDebounceAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['long debounce'],
        debounce: 300,
      };

      await act(async () => {
        result.current.callClientActionAsync(shortDebounceAction);
        result.current.callClientActionAsync(longDebounceAction);
      });

      // Fast-forward by 150ms - short debounce should execute
      await act(async () => {
        vi.advanceTimersByTime(150);
      });

      expect(executionOrder).toContain('short debounce');
      expect(executionOrder).not.toContain('long debounce');

      // Fast-forward by another 200ms - long debounce should execute
      await act(async () => {
        vi.advanceTimersByTime(200);
      });

      expect(executionOrder).toContain('long debounce');
      console.log = originalConsoleLog;
    });

    it('should handle actions without debounce immediately', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionCount = { count: 0 };
      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        executionCount.count++;
      });

      const immediateAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['immediate message'],
        // No debounce property
      };

      await act(async () => {
        await result.current.callClientActionAsync(immediateAction);
      });

      // Should execute immediately
      expect(executionCount.count).toBe(1);
      console.log = originalConsoleLog;
    });
  });

  describe('Integration with Updated Components', () => {
    it('should work correctly with useDataReadiness hook integration', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      // Test that async loading state is properly managed
      const actionWithLoading: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [
          {
            url: 'https://api.example.com/data',
            key: 'testData',
            method: 'GET',
          },
        ],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithLoading);
      });

      // Verify that loading states were managed
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);
    });

    it('should maintain backward compatibility with existing action patterns', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      // Test legacy action format still works
      const legacyAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['legacy action'],
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      await act(async () => {
        await result.current.callClientActionAsync(legacyAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('legacy action');
      consoleSpy.mockRestore();
    });

    it('should handle complex action chains with mixed async/sync patterns', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: string[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        executionOrder.push(message);
      });

      const mixedActions: (ActionConfig | ExtendedActionConfig)[] = [
        {
          type: 'clientAction',
          action: 'log',
          payload: ['sync action 1'],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['async action with loader'],
          asyncLoadStart: true,
          asyncLoadEnd: true,
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['sync action 2'],
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(mixedActions);
      });

      expect(executionOrder).toEqual([
        'sync action 1',
        'async action with loader',
        'sync action 2',
      ]);

      // Verify loading states were managed for the async action
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);

      console.log = originalConsoleLog;
    });
  });

  describe('Error Handling with Async Features', () => {
    it('should properly clean up loading state when action fails', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      // Mock an action that will fail
      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        throw new Error('Action failed');
      });

      const failingAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['failing action'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      try {
        await act(async () => {
          await result.current.callClientActionAsync(failingAction);
        });
      } catch (error) {
        // Expected to fail
      }

      // Should still call asyncLoadEnd to clean up loading state
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);

      console.log = originalConsoleLog;
    });
  });

  describe('Performance and Timing Tests', () => {
    it('should handle rapid sequential action calls without blocking', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const startTime = Date.now();
      const actions: ActionConfig[] = Array.from({ length: 10 }, (_, i) => ({
        type: 'clientAction',
        action: 'log',
        payload: [`Action ${i + 1}`],
      }));

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Should complete quickly (within reasonable time)
      expect(executionTime).toBeLessThan(1000); // Less than 1 second
    });

    it('should handle concurrent actions efficiently', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const startTime = Date.now();
      const actions: ActionConfig[] = Array.from({ length: 5 }, (_, i) => ({
        type: 'clientAction',
        action: 'log',
        payload: [`Concurrent Action ${i + 1}`],
      }));

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Concurrent execution should be faster than sequential
      expect(executionTime).toBeLessThan(500); // Less than 0.5 seconds
    });

    it('should respect concurrency limits', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionTimes: number[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        executionTimes.push(Date.now());
      });

      const actions: ActionConfig[] = Array.from({ length: 6 }, (_, i) => ({
        type: 'clientAction',
        action: 'log',
        payload: [`Limited Action ${i + 1}`],
      }));

      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2); // Limit to 2 concurrent
      });

      // All actions should have executed
      expect(executionTimes).toHaveLength(6);
      console.log = originalConsoleLog;
    });
  });

  describe('Advanced Async Patterns', () => {
    it('should handle nested async actions correctly', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: string[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        executionOrder.push(message);
      });

      const conditionalAction: ActionConfig = {
        type: 'clientAction',
        action: 'conditional',
        payload: {
          condition: true,
          actions: {
            whenTrue: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['nested true action'],
              },
            ],
            whenFalse: [
              {
                type: 'clientAction',
                action: 'log',
                payload: ['nested false action'],
              },
            ],
          },
        },
      };

      await act(async () => {
        await result.current.callClientActionAsync(conditionalAction);
      });

      expect(executionOrder).toContain('nested true action');
      expect(executionOrder).not.toContain('nested false action');
      console.log = originalConsoleLog;
    });

    it('should handle timeout actions with proper async behavior', async () => {
      vi.useFakeTimers();

      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: string[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        executionOrder.push(message);
      });

      const timeoutAction: ActionConfig = {
        type: 'clientAction',
        action: 'timeout',
        payload: [
          1000, // 1 second delay
          [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['delayed action'],
            },
          ],
        ],
      };

      const promise = act(async () => {
        await result.current.callClientActionAsync(timeoutAction);
      });

      // Should not have executed yet
      expect(executionOrder).not.toContain('delayed action');

      // Fast-forward time
      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      await promise;

      expect(executionOrder).toContain('delayed action');
      console.log = originalConsoleLog;
      vi.useRealTimers();
    });

    it('should handle forEach actions with async execution', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: string[] = [];
      const originalConsoleLog = console.log;
      console.log = vi.fn((message: string) => {
        executionOrder.push(message);
      });

      const forEachAction: ActionConfig = {
        type: 'clientAction',
        action: 'forEach',
        payload: {
          value: ['item1', 'item2', 'item3'],
          actions: [
            {
              type: 'clientAction',
              action: 'log',
              payload: ['@param:{item}'], // This should be resolved for each item
            },
          ],
        },
      };

      await act(async () => {
        await result.current.callClientActionAsync(forEachAction);
      });

      // Should have executed for each item
      expect(executionOrder).toHaveLength(3);
      console.log = originalConsoleLog;
    });
  });

  describe('State Management Integration', () => {
    it('should properly integrate with useAsyncLoaderStore', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionWithAsyncLoad: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test with async load'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithAsyncLoad);
      });

      // Verify the store was called correctly
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(false);
    });

    it('should handle multiple concurrent actions with loading states', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const actionsWithLoading: ExtendedActionConfig[] = [
        {
          type: 'clientAction',
          action: 'log',
          payload: ['action 1'],
          asyncLoadStart: true,
          asyncLoadEnd: true,
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['action 2'],
          asyncLoadStart: true,
          asyncLoadEnd: true,
        },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actionsWithLoading);
      });

      // Should have managed loading states for both actions
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoading).toHaveBeenCalledWith(false);
      // Called multiple times due to concurrent execution
      expect(mockSetAsyncLoading).toHaveBeenCalledTimes(4); // 2 start + 2 end
    });
  });
});
