import { act, renderHook } from '@testing-library/react';
import type Keycloak from 'keycloak-js';
import type { Location } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type {
  ActionConfig,
  ExtendedActionConfig,
} from '../../../models/action.config';
import { useClientActionsAsync } from '../hooks';

// Create shared mock functions
const mockSetAsyncLoadingFn = vi.fn();
const mockSetState = vi.fn();
const mockSetModalState = vi.fn();
const mockAddError = vi.fn();
const mockClearError = vi.fn();
const mockClearAllErrors = vi.fn();

// Mock dependencies with proper paths
vi.mock('../../../useAppStore', () => ({
  useAppStore: vi.fn((selector) => {
    const mockState = {
      testData: 'mock-data',
      user: { name: 'Test User' },
      setState: mockSetState,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: mockSetModalState,
  })),
}));

vi.mock('../../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector) => {
    const mockState = {
      asyncLoading: false,
      setAsyncLoading: mockSetAsyncLoadingFn,
    };
    return selector ? selector(mockState) : mockState;
  }),
}));

vi.mock('../../../useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: mockAddError,
    clearError: mockClearError,
    clearAllErrors: mockClearAllErrors,
  })),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
    reset: vi.fn(),
  })),
}));

// Mock helper functions
vi.mock('../../../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({ mockData: 'test' }),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
  evalStringExpression: vi.fn((value) => value),
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../../../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

describe('useClientActionsAsync - Simple Async Features Test', () => {
  let mockNavigate: any;
  let mockLocation: Location<any>;
  let mockKeycloak: Keycloak;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockNavigate = vi.fn();
    mockLocation = {
      pathname: '/test',
      search: '',
      hash: '',
      state: null,
      key: 'test',
    };
    mockKeycloak = {
      token: 'mock-token',
      authenticated: true,
    } as any;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Functionality', () => {
    it('should execute simple log action', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const simpleAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
      };

      await act(async () => {
        await result.current.callClientActionAsync(simpleAction);
      });

      expect(consoleSpy).toHaveBeenCalledWith('test message');
      consoleSpy.mockRestore();
    });

    it('should handle asyncLoadStart property', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const actionWithAsyncLoadStart: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithAsyncLoadStart);
      });

      expect(consoleSpy).toHaveBeenCalledWith('test message');
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(true);
      consoleSpy.mockRestore();
    });

    it('should handle asyncLoadEnd property', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const actionWithAsyncLoadEnd: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithAsyncLoadEnd);
      });

      expect(consoleSpy).toHaveBeenCalledWith('test message');
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(false);
      consoleSpy.mockRestore();
    });

    it('should handle both asyncLoadStart and asyncLoadEnd', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const actionWithBothLoaders: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['test message'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      await act(async () => {
        await result.current.callClientActionAsync(actionWithBothLoaders);
      });

      expect(consoleSpy).toHaveBeenCalledWith('test message');
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(false);
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledTimes(2);
      consoleSpy.mockRestore();
    });
  });

  describe('Sequential vs Concurrent Execution', () => {
    it('should execute actions sequentially', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation((message: string) => {
        if (message.includes('Action 1')) executionOrder.push(1);
        if (message.includes('Action 2')) executionOrder.push(2);
        if (message.includes('Action 3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['Action 1'] },
        { type: 'clientAction', action: 'log', payload: ['Action 2'] },
        { type: 'clientAction', action: 'log', payload: ['Action 3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });

      expect(executionOrder).toEqual([1, 2, 3]);
      consoleSpy.mockRestore();
    });

    it('should execute actions concurrently', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const executionOrder: number[] = [];
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation((message: string) => {
        if (message.includes('Action 1')) executionOrder.push(1);
        if (message.includes('Action 2')) executionOrder.push(2);
        if (message.includes('Action 3')) executionOrder.push(3);
      });

      const actions: ActionConfig[] = [
        { type: 'clientAction', action: 'log', payload: ['Action 1'] },
        { type: 'clientAction', action: 'log', payload: ['Action 2'] },
        { type: 'clientAction', action: 'log', payload: ['Action 3'] },
      ];

      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });

      // All actions should have executed
      expect(executionOrder).toContain(1);
      expect(executionOrder).toContain(2);
      expect(executionOrder).toContain(3);
      expect(executionOrder).toHaveLength(3);
      consoleSpy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should clean up loading state on error', async () => {
      const { result } = renderHook(() =>
        useClientActionsAsync({
          navigate: mockNavigate,
          location: mockLocation,
          keycloak: mockKeycloak,
        })
      );

      const originalConsoleLog = console.log;
      console.log = vi.fn(() => {
        throw new Error('Test error');
      });

      const failingAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['failing action'],
        asyncLoadStart: true,
        asyncLoadEnd: true,
      };

      try {
        await act(async () => {
          await result.current.callClientActionAsync(failingAction);
        });
      } catch (error) {
        // Expected to fail
      }

      // Should still call asyncLoadEnd to clean up loading state
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(true);
      expect(mockSetAsyncLoadingFn).toHaveBeenCalledWith(false);

      console.log = originalConsoleLog;
    });
  });
});
