import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useClientActionsAsync } from '../hooks';
import { ActionConfig } from '../types';

// Mock the store with state tracking
let mockState = { user: { name: 'Initial' }, counter: 0 };

vi.mock('../../../useAppStore', () => ({
  useAppStore: {
    setState: vi.fn((updater) => {
      if (typeof updater === 'function') {
        mockState = { ...mockState, ...updater(mockState) };
      } else {
        mockState = { ...mockState, ...updater };
      }
    }),
    getState: vi.fn(() => mockState),
  },
}));

vi.mock('../../useModalStore', () => ({
  useModalStore: () => ({
    setModalState: vi.fn(),
  }),
}));

vi.mock('../../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: () => ({
    setAsyncLoading: vi.fn(),
  }),
}));

vi.mock('../../useErrorStore', () => ({
  useErrorStore: () => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  }),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: () => null,
}));

// Mock expression evaluation to test state synchronization
vi.mock('../../../helpers', () => ({
  evalStringExpression: vi.fn((expression, state) => {
    if (expression.startsWith('js:')) {
      const jsCode = expression.substring(3);
      // Simple evaluation for testing - in real implementation this would be more complex
      if (jsCode.includes('user.name')) {
        return state?.user?.name || 'undefined';
      }
      if (jsCode.includes('counter')) {
        return state?.counter || 0;
      }
    }
    return expression;
  }),
  evaluateFormConditionExpression: vi.fn((condition, state) => {
    // Simple boolean evaluation for testing
    if (condition.includes('user.name === "Updated"')) {
      return state?.user?.name === 'Updated';
    }
    if (condition.includes('counter > 0')) {
      return (state?.counter || 0) > 0;
    }
    return false;
  }),
  extractValues: vi.fn((obj) => obj),
  renderTemplateObject: vi.fn((obj) => obj),
  processTemplates: vi.fn((obj) => obj),
}));

describe('State Synchronization Fix', () => {
  const mockNavigate = vi.fn();
  const mockLocation = { pathname: '/test', search: '' };
  const mockKeycloak = { token: 'test-token' };

  const defaultProps = {
    navigate: mockNavigate,
    location: mockLocation as any,
    keycloak: mockKeycloak as any,
    debug: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockState = { user: { name: 'Initial' }, counter: 0 };
  });

  describe('Immediate State Updates', () => {
    it('should have fresh state immediately after updateStore', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        return;
      });

      // Action chain: updateStore -> conditional (should see fresh state)
      const actionChain: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ user: { name: 'Updated' } }],
        },
        {
          type: 'clientAction',
          action: 'conditional',
          payload: {
            condition: 'js:{user.name === "Updated"}',
            actions: {
              whenTrue: [
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: ['State is fresh!'],
                },
              ],
              whenFalse: [
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: ['State is stale!'],
                },
              ],
            },
          },
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actionChain);
      });

      // Should log "State is fresh!" because state synchronization is fixed
      expect(consoleSpy).toHaveBeenCalledWith('State is fresh!');
      expect(consoleSpy).not.toHaveBeenCalledWith('State is stale!');
      consoleSpy.mockRestore();
    });

    it('should maintain state consistency across multiple updates', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        return;
      });

      const actionChain: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ counter: 1 }],
        },
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ counter: 2 }],
        },
        {
          type: 'clientAction',
          action: 'conditional',
          payload: {
            condition: 'js:{counter > 0}',
            actions: {
              whenTrue: [
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: ['Counter is positive'],
                },
              ],
              whenFalse: [
                {
                  type: 'clientAction',
                  action: 'log',
                  payload: ['Counter is not positive'],
                },
              ],
            },
          },
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actionChain);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Counter is positive');
      consoleSpy.mockRestore();
    });
  });

  describe('Expression Evaluation with Fresh State', () => {
    it('should evaluate js:{} expressions with fresh state', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        return;
      });

      const actionChain: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ user: { name: 'Fresh State User' } }],
        },
        {
          type: 'clientAction',
          action: 'log',
          payload: ['User name is:', 'js:{user.name}'],
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actionChain);
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'User name is:',
        'Fresh State User'
      );
      consoleSpy.mockRestore();
    });
  });

  describe('Parameter Passing with State Synchronization', () => {
    it('should pass parameters correctly in forEach with fresh state', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        return;
      });

      const forEachConfig: ActionConfig = {
        type: 'clientAction',
        action: 'forEach',
        payload: {
          value: [
            { id: 1, name: 'Item 1' },
            { id: 2, name: 'Item 2' },
          ],
          actions: [
            {
              type: 'clientAction',
              action: 'updateStore',
              payload: [{ currentItem: '@param:{name}' }],
            },
            {
              type: 'clientAction',
              action: 'log',
              payload: ['Processing:', '@param:{name}'],
            },
          ],
        },
      };

      await act(async () => {
        await result.current.callClientActionAsync(forEachConfig);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Processing:', 'Item 1');
      expect(consoleSpy).toHaveBeenCalledWith('Processing:', 'Item 2');
      consoleSpy.mockRestore();
    });
  });

  describe('Switch Actions with Fresh State', () => {
    it('should evaluate switch conditions with fresh state', async () => {
      const { result } = renderHook(() => useClientActionsAsync(defaultProps));
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {
        return;
      });

      const actionChain: ActionConfig[] = [
        {
          type: 'clientAction',
          action: 'updateStore',
          payload: [{ status: 'active' }],
        },
        {
          type: 'clientAction',
          action: 'switch',
          payload: {
            pathToValue: 'status',
            cases: [
              {
                caseName: 'active',
                actions: [
                  {
                    type: 'clientAction',
                    action: 'log',
                    payload: ['Status is active'],
                  },
                ],
              },
              {
                caseName: 'inactive',
                actions: [
                  {
                    type: 'clientAction',
                    action: 'log',
                    payload: ['Status is inactive'],
                  },
                ],
              },
            ],
          },
        },
      ];

      await act(async () => {
        await result.current.callClientActionsSequentially(actionChain);
      });

      expect(consoleSpy).toHaveBeenCalledWith('Status is active');
      expect(consoleSpy).not.toHaveBeenCalledWith('Status is inactive');
      consoleSpy.mockRestore();
    });
  });
});
