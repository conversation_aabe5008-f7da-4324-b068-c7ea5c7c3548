import {
  act,
  cleanup,
  render,
  renderHook,
  screen,
} from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useDataReadiness } from '../useDataReadiness';

// Create mock store states
const mockAppStoreState: any = {
  users: ['user1', 'user2'],
  permissions: { read: true, write: false },
  filtersData: [] as any[],
  auth: {} as any,
  _timestamps: {
    users: Date.now() - 1000,
    permissions: Date.now() - 2000,
  },
  setState: vi.fn(),
  getState: vi.fn(),
  reset: vi.fn(),
};

const mockAsyncLoaderState: any = {
  asyncLoading: false,
  setAsyncLoading: vi.fn(),
};

// Mock react-router-dom useNavigation
vi.mock('react-router-dom', () => ({
  useNavigation: () => ({ state: 'idle' }),
}));

// Mock useAsyncLoaderStore
vi.mock('../useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn((selector: any) => {
    if (typeof selector === 'function') {
      return selector(mockAsyncLoaderState);
    }
    return mockAsyncLoaderState;
  }),
}));

// Mock useAppStore
vi.mock('../../useAppStore', () => ({
  useAppStore: vi.fn((selector: any) => {
    if (typeof selector === 'function') {
      return selector(mockAppStoreState);
    }
    return mockAppStoreState;
  }),
}));

// Mock store-key-analysis
vi.mock('../../helpers/store-key-analysis', () => ({
  analyzeRequiredStoreKeys: vi.fn(() => new Set(['users', 'permissions'])),
}));

// Mock logger
vi.mock('../../../Utilities', () => ({
  logger: {
    configure: vi.fn(),
    log: vi.fn(),
  },
}));

function Probe(props: any) {
  const { ready, routeReady, loadersReady, storeReady, requiredKeys } =
    useDataReadiness(props.options);
  return (
    <div>
      <div data-testid="ready">{String(ready)}</div>
      <div data-testid="routeReady">{String(routeReady)}</div>
      <div data-testid="loadersReady">{String(loadersReady)}</div>
      <div data-testid="storeReady">{String(storeReady)}</div>
      <div data-testid="keys">{(requiredKeys || []).join(',')}</div>
    </div>
  );
}

describe('useDataReadiness', () => {
  beforeEach(() => {
    cleanup();
    // Reset mock store states
    mockAsyncLoaderState.asyncLoading = false;
    mockAppStoreState.filtersData = [];
    mockAppStoreState.auth = {};
    mockAppStoreState.users = ['user1', 'user2'];
    mockAppStoreState.permissions = { read: true, write: false };

    // Clear all mock calls
    vi.clearAllMocks();
  });

  afterEach(() => cleanup());

  it('gates component by route, loader and store keys when listed', () => {
    // Configure: gate component "CompA" and require store keys
    const options = {
      componentName: 'CompA',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: ['filtersData', 'auth'],
      autoDetectEnabled: false,
    };

    // Initial store state (from default): filtersData: [], auth: {}
    // loaders idle and route idle (mocked)
    render(<Probe options={options} />);

    expect(screen.getByTestId('routeReady').textContent).toBe('true');
    expect(screen.getByTestId('loadersReady').textContent).toBe('true');
    // store not ready because defaults are empty structures
    expect(screen.getByTestId('storeReady').textContent).toBe('false');
    expect(screen.getByTestId('ready').textContent).toBe('false');

    // Satisfy store readiness by updating mock state
    act(() => {
      mockAppStoreState.filtersData = [1];
      mockAppStoreState.auth = { user: 1 };
    });

    // Re-render by unmounting and mounting fresh to avoid duplicate testids in DOM
    cleanup();
    render(<Probe options={options} />);
    expect(screen.getByTestId('storeReady').textContent).toBe('true');
    expect(screen.getByTestId('ready').textContent).toBe('true');
  });

  it('ignores gating when component is not listed', () => {
    const options = {
      componentName: 'CompB',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: ['filtersData'],
      autoDetectEnabled: false,
    };

    render(<Probe options={options} />);
    expect(screen.getByTestId('ready').textContent).toBe('true');
  });

  it('respects asyncLoading flag from useAsyncLoaderStore', () => {
    const options = {
      componentName: 'CompA',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: [],
      autoDetectEnabled: false,
    };

    // Simulate loaders in-flight
    act(() => {
      mockAsyncLoaderState.asyncLoading = true;
    });
    render(<Probe options={options} />);
    expect(screen.getByTestId('loadersReady').textContent).toBe('false');
    expect(screen.getByTestId('ready').textContent).toBe('false');

    // Mark loaders as done
    act(() => {
      mockAsyncLoaderState.asyncLoading = false;
    });
    // Clean and re-render to avoid duplicate nodes
    cleanup();
    render(<Probe options={options} />);
    expect(screen.getByTestId('loadersReady').textContent).toBe('true');
  });

  it('should return basic readiness state for backward compatibility', () => {
    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        requiredStoreKeys: ['users', 'permissions'],
      })
    );

    expect(result.current.ready).toBe(true);
    expect(result.current.routeReady).toBe(true);
    expect(result.current.loadersReady).toBe(true);
    expect(result.current.storeReady).toBe(true);
  });

  it('should provide enhanced readiness information', () => {
    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        requiredStoreKeys: ['users', 'permissions'],
        progressiveReadiness: true,
      })
    );

    expect(result.current.level).toBe('complete');
    expect(result.current.progress).toBe(1);
    expect(result.current.availableKeys).toEqual(['users', 'permissions']);
    expect(result.current.missingKeys).toEqual([]);
    expect(result.current.staleness).toBeDefined();
    expect(result.current.waitTimeElapsed).toBeGreaterThanOrEqual(0);
  });

  it('should handle missing store keys correctly', () => {
    // Temporarily modify mock state to simulate missing keys
    const originalPermissions = mockAppStoreState.permissions;
    delete mockAppStoreState.permissions;

    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        componentsRequiringDataLoad: ['TestComponent'], // Gate the component
        requiredStoreKeys: ['users', 'permissions'],
        progressiveReadiness: true,
      })
    );

    expect(result.current.level).toBe('partial');
    expect(result.current.progress).toBe(0.5); // 1 out of 2 keys available
    expect(result.current.availableKeys).toEqual(['users']);
    expect(result.current.missingKeys).toEqual(['permissions']);
    expect(result.current.ready).toBe(false); // Should not be ready with missing keys

    // Restore original state
    mockAppStoreState.permissions = originalPermissions;
  });

  it('should support graceful degradation', () => {
    // Temporarily modify mock state to simulate missing keys
    const originalPermissions = mockAppStoreState.permissions;
    delete mockAppStoreState.permissions;

    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        componentsRequiringDataLoad: ['TestComponent'], // Gate the component
        requiredStoreKeys: ['users', 'permissions'],
        progressiveReadiness: true,
        allowPartialReadiness: true, // Allow partial readiness
      })
    );

    expect(result.current.level).toBe('partial');
    expect(result.current.progress).toBe(0.5);
    expect(result.current.storeReady).toBe(true); // Should be ready with graceful degradation

    // Restore original state
    mockAppStoreState.permissions = originalPermissions;
  });

  it('should handle timing constraints', async () => {
    const { result, rerender } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        requiredStoreKeys: ['users', 'permissions'],
        minWaitTimeMs: 100,
        maxWaitTimeMs: 1000,
        progressiveReadiness: true,
      })
    );

    // Initially should track wait time
    expect(result.current.waitTimeElapsed).toBeGreaterThanOrEqual(0);

    // Wait a bit and force re-render to update timing
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 50));
      rerender(); // Force re-render to update the timing calculation
    });

    expect(result.current.waitTimeElapsed).toBeGreaterThan(0);
  });

  it('should auto-detect store keys from props', () => {
    // eslint-disable-next-line prefer-template
    const userTemplate = '$' + '{users}';
    // eslint-disable-next-line prefer-template
    const permissionTemplate = '#' + '{permissions.read}';

    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'TestComponent',
        autoDetectFromProps: {
          userList: userTemplate,
          userPermissions: permissionTemplate,
        },
        autoDetectEnabled: true,
        progressiveReadiness: true,
      })
    );

    // Should detect 'users' and 'permissions' from the props
    expect(result.current.requiredKeys).toEqual(['users', 'permissions']);
    expect(result.current.level).toBe('complete');
  });

  it('should handle component gating correctly', () => {
    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'DynamicTable',
        componentsRequiringDataLoad: ['DynamicTable', 'FormBuilder'],
        requiredStoreKeys: ['users'],
      })
    );

    // Should gate the component since it's in the required list
    expect(result.current.ready).toBe(true); // All conditions met
  });

  it('should not gate components not in the required list', () => {
    const { result } = renderHook(() =>
      useDataReadiness({
        componentName: 'SimpleComponent',
        componentsRequiringDataLoad: ['DynamicTable', 'FormBuilder'],
        requiredStoreKeys: ['users'],
      })
    );

    // Should not gate since component is not in required list
    expect(result.current.ready).toBe(true);
  });
});
