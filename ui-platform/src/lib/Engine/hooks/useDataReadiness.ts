import { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigation } from 'react-router-dom';
import { logger } from '../../Utilities';
import { analyzeRequiredStoreKeys } from '../helpers/store-key-analysis';
import { useAppStore } from '../useAppStore';
import { useAsyncLoaderStore } from './useAsyncLoaderStore';

export interface DataReadinessOptions {
  componentsRequiringDataLoad?: string[];
  componentName?: string;
  // Store keys that should be truthy/loaded before mounting
  requiredStoreKeys?: string[];
  // Optional: automatically detect store keys from component config props
  autoDetectFromProps?: Record<string, unknown> | null;
  autoDetectEnabled?: boolean;
  debug?: boolean;

  // Enhanced options for gradual data updates
  minWaitTimeMs?: number; // Minimum time to wait before considering ready (prevents premature readiness)
  maxWaitTimeMs?: number; // Maximum time to wait before forcing ready (prevents infinite waiting)
  staleDataThresholdMs?: number; // How long data can be stale before re-checking
  progressiveReadiness?: boolean; // Enable progressive readiness checking instead of binary ready/not-ready
  retryInterval?: number; // Interval to re-check readiness (default: 100ms)
  allowPartialReadiness?: boolean; // Allow partial readiness for non-critical keys
}

/**
 * Enhanced readiness state providing detailed information about data availability
 */
export interface ReadinessState {
  ready: boolean; // Backward compatibility - true when component should render
  level: 'none' | 'partial' | 'complete'; // Progressive readiness level
  progress: number; // 0-1 indicating completion percentage of required keys
  missingKeys: string[]; // Keys that are still undefined/loading
  availableKeys: string[]; // Keys that are ready and available
  staleness: Record<string, number>; // Timestamp of last update per key (if trackable)
  waitTimeElapsed: number; // Milliseconds since readiness checking started
  routeReady: boolean; // Individual readiness signals for debugging
  loadersReady: boolean;
  storeReady: boolean;
  requiredKeys: string[]; // All keys being monitored
}

/**
 * Aggregates multiple readiness signals to avoid mounting heavy components
 * before necessary data is available. Enhanced version supports progressive
 * data loading scenarios with configurable timing and staleness detection.
 */
export function useDataReadiness(
  options: DataReadinessOptions = {}
): ReadinessState {
  const {
    componentName,
    componentsRequiringDataLoad = [],
    requiredStoreKeys = [],
    autoDetectFromProps = null,
    autoDetectEnabled = true,
    debug = false,
    // Enhanced options with sensible defaults
    minWaitTimeMs = 0,
    maxWaitTimeMs = 30000, // 30 seconds max wait
    staleDataThresholdMs = 60000, // 1 minute staleness threshold
    progressiveReadiness = false,
    retryInterval = 100, // 100ms retry interval
    allowPartialReadiness: gracefulDegradation = false,
  } = options;

  logger.configure({
    enabled: debug,
    prefix: '🔍[Data Readiness]:',
    options: { style: { backgroundColor: '#e3f2fd', color: '#1565c0' } },
  });
  const log = logger.log;

  const navigation = useNavigation();
  const asyncLoading = useAsyncLoaderStore((s) => s.asyncLoading);

  // Timing and progressive readiness state
  const startTimeRef = useRef<number>(Date.now());
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [forceReady, setForceReady] = useState(false);
  const [retryTrigger, setRetryTrigger] = useState(0);

  // Derive final required keys
  const derivedKeys = useMemo(() => {
    if (!autoDetectEnabled || !autoDetectFromProps) return requiredStoreKeys;
    try {
      const detected = Array.from(
        analyzeRequiredStoreKeys(autoDetectFromProps)
      );
      const merged = Array.from(
        new Set([...(requiredStoreKeys || []), ...detected])
      );
      return merged;
    } catch (e) {
      return requiredStoreKeys;
    }
  }, [autoDetectEnabled, autoDetectFromProps, requiredStoreKeys]);

  // Enhanced store-level readiness with detailed key tracking
  const store = useAppStore((state) => state);
  const storeAnalysis = useMemo(() => {
    if (!derivedKeys?.length) {
      return {
        ready: true,
        availableKeys: [],
        missingKeys: [],
        progress: 1,
        staleness: {},
      };
    }

    const availableKeys: string[] = [];
    const missingKeys: string[] = [];
    const staleness: Record<string, number> = {};
    const currentTime = Date.now();

    derivedKeys.forEach((key) => {
      const val =
        store && key in store ? store?.[key as keyof typeof store] : undefined;

      // Check if value is missing or empty
      const isValueReady = (() => {
        if (val === undefined || val === null) return false;
        if (Array.isArray(val) && val.length === 0) return false;
        if (val && typeof val === 'object' && Object.keys(val).length === 0)
          return false;
        return true;
      })();

      if (!isValueReady) {
        missingKeys.push(key);
      } else {
        availableKeys.push(key);

        // Track staleness if store has timestamp metadata (optional enhancement)
        // This assumes store might have _timestamps or similar metadata structure
        const storeWithMeta = store as unknown as {
          _timestamps?: Record<string, number>;
          [key: string]: unknown;
        };
        if (storeWithMeta._timestamps && storeWithMeta._timestamps[key]) {
          const dataAge = currentTime - storeWithMeta._timestamps[key];
          staleness[key] = dataAge;

          // Mark as missing if data is too stale
          if (dataAge > staleDataThresholdMs) {
            missingKeys.push(`${key} (stale)`);
            availableKeys.pop(); // Remove from available since it's stale
          }
        } else {
          // Default to fresh data if no timestamp available
          staleness[key] = 0;
        }
      }
    });

    const progress =
      derivedKeys.length > 0 ? availableKeys.length / derivedKeys.length : 1;
    const ready = gracefulDegradation
      ? progress >= 0.5 // Allow readiness with 50% or more data
      : missingKeys.length === 0;

    return {
      ready,
      availableKeys,
      missingKeys,
      progress,
      staleness,
    };
  }, [
    store,
    derivedKeys,
    gracefulDegradation,
    staleDataThresholdMs,
    retryTrigger, // Intentionally included to force re-evaluation during progressive readiness
  ]);

  // Route-level readiness: react-router navigation idle
  const routeReady = useMemo(
    () => navigation.state === 'idle',
    [navigation.state]
  );

  // State/screen-level readiness: leverage async loader store used by actions & fetch calls
  const loadersReady = useMemo(() => asyncLoading === false, [asyncLoading]);

  // Component-specific need: only gate if the component is listed
  const shouldGate = componentsRequiringDataLoad.includes(componentName || '');

  const activeLoaderFinished = useMemo(() => {
    return routeReady && loadersReady;
  }, [routeReady, loadersReady]);

  // Enhanced readiness calculation with timing constraints
  const enhancedReadiness = useMemo(() => {
    const currentTime = Date.now();
    const waitTimeElapsed = currentTime - startTimeRef.current;

    // Basic readiness from existing logic
    const basicReady = shouldGate
      ? activeLoaderFinished && storeAnalysis.ready
      : activeLoaderFinished;

    // Apply timing constraints
    const minWaitSatisfied = waitTimeElapsed >= minWaitTimeMs;
    const maxWaitExceeded = waitTimeElapsed >= maxWaitTimeMs;

    // Force ready if max wait time exceeded, regardless of other conditions
    const shouldForceReady = maxWaitExceeded || forceReady;

    // Final readiness decision
    const ready = shouldForceReady || (basicReady && minWaitSatisfied);

    // Determine readiness level for progressive readiness
    let level: 'none' | 'partial' | 'complete' = 'none';
    if (storeAnalysis.progress === 1 && activeLoaderFinished) {
      level = 'complete';
    } else if (storeAnalysis.progress > 0 || activeLoaderFinished) {
      level = 'partial';
    }

    return {
      ready,
      level,
      progress: storeAnalysis.progress,
      missingKeys: storeAnalysis.missingKeys,
      availableKeys: storeAnalysis.availableKeys,
      staleness: storeAnalysis.staleness,
      waitTimeElapsed,
      routeReady,
      loadersReady,
      storeReady: storeAnalysis.ready,
      requiredKeys: derivedKeys,
    };
  }, [
    shouldGate,
    activeLoaderFinished,
    storeAnalysis,
    minWaitTimeMs,
    maxWaitTimeMs,
    forceReady,
    routeReady,
    loadersReady,
    derivedKeys,
  ]);

  // Enhanced timing effects for progressive readiness
  useEffect(() => {
    // Set up retry interval for progressive readiness checking
    if (progressiveReadiness && !enhancedReadiness.ready) {
      retryTimeoutRef.current = setTimeout(() => {
        setRetryTrigger((prev) => prev + 1);
      }, retryInterval);
    }

    // Set up max wait timeout to force readiness
    if (maxWaitTimeMs > 0 && !enhancedReadiness.ready) {
      const timeoutId = setTimeout(() => {
        log`[useDataReadiness] Max wait time exceeded (${maxWaitTimeMs}ms), forcing ready state`;
        setForceReady(true);
      }, maxWaitTimeMs);

      return () => clearTimeout(timeoutId);
    }

    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, [
    enhancedReadiness.ready,
    progressiveReadiness,
    retryInterval,
    maxWaitTimeMs,
    log,
  ]);

  // Enhanced stabilization: once ready, maintain ready until unmount (backward compatibility)
  const [latchedReady, setLatchedReady] = useState<boolean>(
    enhancedReadiness.ready
  );
  useEffect(() => {
    log`[useDataReadiness] Enhanced readiness state: ${{
      ready: enhancedReadiness.ready,
      level: enhancedReadiness.level,
      progress: enhancedReadiness.progress,
      waitTimeElapsed: enhancedReadiness.waitTimeElapsed,
      missingKeys: enhancedReadiness.missingKeys,
      shouldGate,
    }}`;

    // Traditional latching behavior for backward compatibility
    if (enhancedReadiness.ready) setLatchedReady(true);
  }, [enhancedReadiness, shouldGate, log]);

  // Return enhanced readiness state with backward compatibility
  return {
    ...enhancedReadiness,
    ready: latchedReady, // Use latched ready for backward compatibility
  };
}
