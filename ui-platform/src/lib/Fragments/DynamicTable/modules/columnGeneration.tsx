import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { ActionConfig } from '../../../Engine/models/action.config';
import { TextConfig } from '../../../Engine/models/text.config';
import { Text } from '../../Text';
import { CellRenderer } from '../components/CellRenderer';
import { ColumnConfig, ColumnVisibilityConfig } from '../types';

/**
 * Helper function to render header with Text component if config provided
 */
export const renderHeader = (header: string | TextConfig | undefined) => {
  if (!header) return null;
  if (typeof header === 'string') {
    return (
      <Text
        textItems={[
          {
            text: header,
            options: { format: 'heading' },
          },
        ]}
      />
    );
  }
  return <Text textItems={[header]} />;
};

/**
 * Helper function to render cell content using the modular CellRenderer
 */
export const renderCell = <T extends Record<string, any>>(
  value: any,
  config: ColumnConfig<T> | undefined,
  row: T,
  rowIndex: number,
  _callClientAction?: (config: ActionConfig[]) => void,
  _store?: any,
  storeKeyName?: string,
  updatedCheckValueKey?: string,
  storeRefColumnName?: string,
  rowsLength?: number
): React.ReactNode => {
  return (
    <CellRenderer
      value={value}
      config={config as ColumnConfig<Record<string, any>>}
      row={row}
      rowIndex={rowIndex}
      _callClientAction={_callClientAction}
      _store={_store}
      storeKeyName={storeKeyName}
      updatedCheckValueKey={updatedCheckValueKey}
      storeRefColumnName={storeRefColumnName}
      rowsLength={rowsLength}
    />
  );
};

/**
 * Hook to generate column structure based on visibility configuration
 */
export const useColumnStructure = <T extends Record<string, any>>(
  data: T[],
  displayColumns?: Partial<keyof T>[],
  columnVisibilityConfig?: ColumnVisibilityConfig<T>
) => {
  return useMemo(() => {
    if (!data || data.length === 0) return [];
    const sampleRow = data[0];
    const allKeys = Object.keys(sampleRow) as (keyof T)[];

    // If displayColumns is provided, use only those columns
    if (displayColumns && displayColumns.length > 0) {
      return displayColumns.filter((key) =>
        allKeys.includes(key as keyof T)
      ) as (keyof T)[];
    }

    // If columnVisibilityConfig is provided, respect its configuration
    if (columnVisibilityConfig) {
      // If visibleColumns is specified, use only those
      if (columnVisibilityConfig.visibleColumns) {
        return columnVisibilityConfig.visibleColumns.filter((key) =>
          allKeys.includes(key)
        );
      }

      // If hiddenColumns is specified, exclude those from all keys
      if (columnVisibilityConfig.hiddenColumns) {
        return allKeys.filter(
          (key) => !columnVisibilityConfig.hiddenColumns?.includes(key)
        );
      }

      // If dynamicVisibility is specified, use its result
      if (columnVisibilityConfig.dynamicVisibility) {
        const dynamicVisible = columnVisibilityConfig.dynamicVisibility(data);
        return dynamicVisible.filter((key) => allKeys.includes(key));
      }
    }

    // Default: return all keys
    return allKeys;
  }, [data, displayColumns, columnVisibilityConfig]);
};

/**
 * Hook to generate TanStack Table column definitions
 */
export const useTableColumns = <T extends Record<string, any>>(
  columnStructure: (keyof T)[],
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  enableSorting: boolean,
  enableFiltering: boolean,
  enableColumnResizing: boolean,
  _callClientAction?: (config: ActionConfig[]) => void,
  _store?: any,
  storeKeyName?: string,
  updatedCheckValueKey?: string,
  storeRefColumnName?: string,
  rowsLength?: number
) => {
  return useMemo<ColumnDef<T>[]>(() => {
    if (columnStructure.length === 0) return [];

    return columnStructure.map((key) => {
      const config = columnConfig[key];

      return {
        accessorKey: key,
        header: () =>
          config?.headerTextConfig ? (
            <Text textItems={[config.headerTextConfig]} />
          ) : (
            renderHeader(config?.header)
          ),
        cell: ({ getValue, row }) =>
          renderCell(
            getValue(),
            config,
            row.original,
            row.index,
            _callClientAction,
            _store,
            storeKeyName,
            updatedCheckValueKey,
            storeRefColumnName,
            rowsLength
          ),
        enableSorting: config?.sortable !== false && enableSorting,
        enableColumnFilter: config?.filterable !== false && enableFiltering,
        enableResizing: enableColumnResizing && config?.resizable !== false,
        size: config?.width
          ? typeof config.width === 'string'
            ? parseInt(config.width)
            : config.width
          : undefined,
        minSize: config?.minWidth,
        maxSize: config?.maxWidth,
        footer: config?.footerTextConfig
          ? () => <Text textItems={[config.footerTextConfig!]} />
          : undefined,
        meta: { cellAlignment: config?.cellAlignment },
      };
    });
  }, [
    columnStructure,
    columnConfig,
    enableSorting,
    enableFiltering,
    enableColumnResizing,
    _callClientAction,
    _store,
    storeKeyName,
    updatedCheckValueKey,
    storeRefColumnName,
  ]);
};

/**
 * Combined hook that provides both column structure and TanStack columns
 */
export const useColumnGeneration = <T extends Record<string, any>>(
  data: T[],
  columnConfig: Partial<Record<keyof T, ColumnConfig<T>>>,
  displayColumns?: Partial<keyof T>[],
  columnVisibilityConfig?: ColumnVisibilityConfig<T>,
  enableSorting = false,
  enableFiltering = false,
  enableColumnResizing = false,
  _callClientAction?: (config: ActionConfig[]) => void,
  _store?: any,
  storeKeyName?: string,
  updatedCheckValueKey?: string,
  storeRefColumnName?: string
) => {
  const columnStructure = useColumnStructure(
    data,
    displayColumns,
    columnVisibilityConfig
  );

  const columns = useTableColumns(
    columnStructure,
    columnConfig,
    enableSorting,
    enableFiltering,
    enableColumnResizing,
    _callClientAction,
    _store,
    storeKeyName,
    updatedCheckValueKey,
    storeRefColumnName,
    data.length
  );

  return {
    columnStructure,
    columns,
  };
};
