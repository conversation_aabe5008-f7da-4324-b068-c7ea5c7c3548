import styled from "styled-components";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import React, { useCallback, useEffect, useRef, useState, useImperativeHandle, forwardRef } from "react";
import { Switch } from "../../Components/Inputs/Switch/Switch";
import { useFilteringEngineStore } from "../../Engine";
// import { useSearchTrigger } from "../../Hooks/useSearchTrigger";

const PanelContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  overflow: hidden;
  border-radius: 10px;
`;

// const Title = styled.h2`
//   color: #fff;
//   font-size: 24px;
//   font-weight: 600;
//   // text-align: center;
//   margin: 0 0 12px 0;
// `;

// const TitleUnderlineContainer = styled.div`
//   display: flex;
//   flex-direction: column;
//   // align-items: center;
//   // width: 100%;
//   align-items: center;
//   margin-bottom: 0;
//   max-width: 320px;
// `;

// const Underline = styled.div`
//   width: 270px;
//   height: 2px;
//   background: #2e8bb8;
//   margin: 0 0 24px 0;
//   border-radius: 2px;
// `;

const SearchRow = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  margin-bottom: 16px;
  padding: 0 16px;
`;

const WorkflowSearch = styled(SearchInput)`
  width: 90%;
box-sizing: border-box;
  border: 1px solid #6c757d;
  color: #fff;
  font-size: 16px;
  padding-right: 36px;
  margin-left: 5px;
`;

const SwitchRow = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 0;
  width: 100%;
  padding: 0 16px;
`;

const SwitchLabel = styled.span`
  color: #bfc3c5;
  font-size: 15px;
  margin-left: 12px;
  user-select: none;
  padding-top: 6px;
`;

interface Props {
  searchUrl: string;
  token: string;
  tokenPrefix: string;
  onSearchTrigger?: (searchTerm: string) => void;
}

export interface WorkflowActionPanelSearchRef {
  performSearch: (term: string) => void;
  setSearchTerm: (term: string) => void;
}

export const WorkflowActionPanelSearch = forwardRef<WorkflowActionPanelSearchRef, Props>(
  ({searchUrl, token, tokenPrefix, onSearchTrigger}, ref) => {
  console.log('[WorkflowActionPanelSearch] Component initialized with props:', {
    searchUrl: searchUrl ? 'provided' : 'missing',
    token: token ? 'provided' : 'missing',
    tokenPrefix: tokenPrefix ? 'provided' : 'missing',
    onSearchTrigger: onSearchTrigger ? 'provided' : 'not provided'
  });

  //
  const [setFilterFunctions, clearFilterFunctions] = useFilteringEngineStore(state => [
    state.setFilterFunctions,
    state.clearFilterFunctions,
  ]);

  // Local component state
  const [isClosedClaim, setIsClosedClaim] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Register this component as a search handler
  // const { registerSearchTrigger } = useSearchTrigger();
  
  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    performSearch: (term: string) => {
      console.log('[WorkflowActionPanelSearch] performSearch called via ref with term:', term);
      setSearchTerm(term);
      performSearch(term, isClosedClaim);
    },
    setSearchTerm: (term: string) => {
      console.log('[WorkflowActionPanelSearch] setSearchTerm called via ref with term:', term);
      setSearchTerm(term);
    }
  }));

  const performSearch = useCallback(async (term: string, isClosedClaim: boolean) => {
    console.log('[WorkflowActionPanelSearch] performSearch called with:', { term, isClosedClaim });
    
    if (!term || !searchUrl) {
      console.log('[WorkflowActionPanelSearch] Search aborted - missing term or searchUrl:', { term: !!term, searchUrl: !!searchUrl });
      setIsSearching(false);
      return;
    }
    
    console.log('[WorkflowActionPanelSearch] Starting search with URL:', searchUrl);
    setIsSearching(true);
    
    try {
      // Cancel any ongoing search request
      if (abortControllerRef.current) {
        console.log('[WorkflowActionPanelSearch] Cancelling previous search request');
        abortControllerRef.current.abort();
      }
      
      // Create a new abort controller for this request
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      
      const requestBody = {
        search: term,
        active: !isClosedClaim,
      };
      
      console.log('[WorkflowActionPanelSearch] Making API request with body:', requestBody);
      
      const response = await fetch(`${searchUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          authorization: `${tokenPrefix} ${token}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal,
      });
      
      console.log('[WorkflowActionPanelSearch] API response status:', response.status);
      
      if (!response.ok) {
        console.error('[WorkflowActionPanelSearch] Search request failed with status:', response.status);
        throw new Error('Search request failed');
      }
      
      const data = await response.json();
      console.log('[WorkflowActionPanelSearch] API response data:', data);
      
      const searchResults = data.payload || [];
      console.log('[WorkflowActionPanelSearch] Setting filter functions with', searchResults.length, 'results');
      
      setFilterFunctions([{ 
        name: 'search_results', 
        filterFn: () => true, 
        getSearchResults: () => searchResults
      }]);
      
      // Notify parent component if callback provided
      if (onSearchTrigger) {
        console.log('[WorkflowActionPanelSearch] Calling onSearchTrigger callback');
        onSearchTrigger(term);
      } else {
        console.log('[WorkflowActionPanelSearch] No onSearchTrigger callback provided');
      }
      
      console.log('[WorkflowActionPanelSearch] Search completed successfully');
      
    } catch (error) {
      if ((error as any)?.name === 'AbortError') {
        console.log('[WorkflowActionPanelSearch] Search request was aborted');
        return;
      }
      console.error('[WorkflowActionPanelSearch] Search error:', error);
      setIsSearching(false);
    } finally {
      setIsSearching(false);
    }
  }, [searchUrl, token, tokenPrefix, onSearchTrigger, setFilterFunctions]);

  // CAUSING BUG: Investigate before use // Register this component to handle search triggers
  // useEffect(() => {
  //   console.log('[WorkflowActionPanelSearch] Registering search trigger handler');
  //   const cleanup = registerSearchTrigger((term: string) => {
  //     console.log('[WorkflowActionPanelSearch] Search trigger received with term:', term);
  //     setSearchTerm(term);
  //     performSearch(term, isClosedClaim);
  //   });
    
  //   console.log('[WorkflowActionPanelSearch] Search trigger handler registered successfully');
  //   return cleanup;
  // }, [registerSearchTrigger, isClosedClaim, performSearch]);

  // Handle search term changes
  const handleSearchTermChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newTerm = e.target.value;
    console.log('[WorkflowActionPanelSearch] Search term changed to:', newTerm);
    setSearchTerm(newTerm);
    
    // If search term is cleared, also clear filters
    if (newTerm === '') {
      console.log('[WorkflowActionPanelSearch] Search term cleared, clearing filters');
      clearFilterFunctions();
    }
  }, [clearFilterFunctions]);

  // Clear filters on unmount
  useEffect(() => {
    console.log('[WorkflowActionPanelSearch] Component mounted, will clear filters on unmount');
    return () => {
      console.log('[WorkflowActionPanelSearch] Component unmounting, clearing filters');
      clearFilterFunctions(); 
    }
  }, [clearFilterFunctions]);

  console.log('[WorkflowActionPanelSearch] Rendering with state:', {
    searchTerm,
    isSearching,
    isClosedClaim
  });

  return (
      <PanelContainer 
      // onClick={handlePanelInteraction} 
      // onKeyDown={handlePanelInteraction}
      >
        <SearchRow>
          <WorkflowSearch
            placeholder="Search"
            value={searchTerm}
            onChange={handleSearchTermChange}
            onSubmit={(e) => {
              console.log('[WorkflowActionPanelSearch] Search form submitted with term:', searchTerm);
              e.preventDefault();
              performSearch(searchTerm, isClosedClaim);
            }}
            onClear={() => {
              console.log('[WorkflowActionPanelSearch] Search cleared');
              setSearchTerm('');
              clearFilterFunctions();
            }}
            isLoading={isSearching}
          />
        </SearchRow>
        <SwitchRow>
          <Switch
            enabled="On"
            disabled="Off"
            name="isClosedClaim"
            isOn={isClosedClaim}
            onChange={(value) => {
              console.log('[WorkflowActionPanelSearch] Closed claims switch changed to:', value);
              setIsClosedClaim(value);
            }}
          />
          <SwitchLabel>Include closed claims</SwitchLabel>
        </SwitchRow>
      </PanelContainer>
  );
});