import React, { useReducer, useEffect } from "react";
import styled from "styled-components";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import { Icon } from "../../Components/Icons";
import { useFilteringEngineStore, FilterFunction, AppliedFilter } from "../../Engine";

const StyledWorkflowActionPanelFilters = styled.div`
  margin: 0;
  width: 90%;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  justify-self: center;
  overflow: hidden;
`;

const FilterHeader = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-top: 24px;
  margin-bottom: 8px;
  text-align: center;
  padding-bottom: 0;
`;

const Underline = styled.div`
  width: 350px;
  height: 2px;
  background: #2e8bb8;
  margin: 8px auto 24px auto;
  border-radius: 2px;
`;

const FilterContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 0 0 16px 0;
`;

const FilterItem = styled.div`
  border: 1px solid #bfc6cc;
  border-radius: 6px;
  padding: 10px 12px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
  text-align: left;
  margin-bottom: 0;
  &:hover {
    background-color: #4a5568;
  }
`;

const SelectedFiltersSection = styled.div`
  margin-top: 0;
  border-radius: 4px;
  overflow: hidden;
`;

const SelectedFilterItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #2D3748;
`;

const FilterName = styled.div`
  font-size: 16px;
  color: #ecc94b;
  font-weight: 500;
`;

const FilterValue = styled.div`
  font-size: 16px;
  color: #fff;
  margin-top: 0;
`;

const CloseButton = styled.div`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const AddFilterButton = styled.button`
  background-color: transparent;
  color: #2e8bb8;
  border: 1px solid #2e8bb8;
  border-radius: 999px;
  padding: 8px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  width: auto;
  align-self: center;
  transition: background 0.2s;
  &:hover {
    background-color: rgba(46, 139, 184, 0.1);
  }
`;

const FilterValueForm = styled.div`
  padding: 0;
  border-radius: 4px;
  margin-top: 0;
`;

const FormHeader = styled.div`
  font-size: 20px;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
`;

const FormInputsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
`;

const ContinueButton = styled.button`
  background-color: #4299e1;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #3182ce;
  }
`;

const StateSelectionContainer = styled.div`
  padding: 0;
  border-radius: 4px;
  margin-top: 0;
`;

const StateHeader = styled.div`
  font-size: 20px;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
`;

const SelectList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 16px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #2D3748;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #4A5568;
    border-radius: 6px;
  }
`;

const StateItem = styled.div`
  padding: 12px 16px;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
  min-height: 20px;
  &:hover {
    background-color: #4A5568;
  }
`;

const StateCount = styled.span`
  color: #A0AEC0;
  font-size: 14px;
  flex-shrink: 0;
`;

const StateName = styled.span`
  text-align: right;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

const StyledSearchInputWrapper = styled.div`
  width: 100%;
  margin-bottom: 12px;
`;

const WorkflowFiltersSearchInput = styled(SearchInput)`
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #6c757d;
  color: #fff;
  font-size: 16px;
`;

export interface FilterOption {
  id: string;
  name: string;
  type: 'range' | 'select' | 'text';
  options?: Array<{
    id: string;
    name: string;
    count?: number;
  }>;
}


export interface WorkflowActionPanelFiltersProps {
  filters: FilterOption[];
  onFilterApply?: (filter: AppliedFilter) => void;
  onFilterRemove?: (filterId: string) => void;
}

// Define the state type
type State = {
  selectedFilter: FilterOption | null;
  filterView: 'main' | 'jobValue' | 'stateSelect';
  minValue: string;
  maxValue: string;
  searchTerm: string;
  filteredOptions: any[];
  showAvailableFilters: boolean;
};

// Define action types
type Action =
  | { type: 'SELECT_FILTER'; payload: FilterOption }
  | { type: 'SET_FILTER_VIEW'; payload: 'main' | 'jobValue' | 'stateSelect' }
  | { type: 'SET_MIN_VALUE'; payload: string }
  | { type: 'SET_MAX_VALUE'; payload: string }
  | { type: 'SET_SEARCH_TERM'; payload: string }
  | { type: 'SET_FILTERED_OPTIONS'; payload: any[] }
  | { type: 'TOGGLE_AVAILABLE_FILTERS' }
  | { type: 'BACK_TO_MAIN' };

// Define the reducer function
function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SELECT_FILTER':
      return {
        ...state,
        selectedFilter: action.payload,
        showAvailableFilters: false,
        filterView: action.payload.type === 'select' ? 'stateSelect' : 'jobValue',
        filteredOptions: action.payload.options || []
      };
    case 'SET_FILTER_VIEW':
      return { ...state, filterView: action.payload };
    case 'SET_MIN_VALUE':
      return { ...state, minValue: action.payload };
    case 'SET_MAX_VALUE':
      return { ...state, maxValue: action.payload };
    case 'SET_SEARCH_TERM':
      return { ...state, searchTerm: action.payload };
    case 'SET_FILTERED_OPTIONS':
      return { ...state, filteredOptions: action.payload };
    case 'TOGGLE_AVAILABLE_FILTERS':
      return { ...state, showAvailableFilters: !state.showAvailableFilters };
    case 'BACK_TO_MAIN':
      return {
        ...state,
        filterView: 'main',
        selectedFilter: null,
        minValue: '',
        maxValue: '',
        searchTerm: ''
      };
    default:
      return state;
  }
}

export function WorkflowActionPanelFilters({
  filters = [],
  onFilterApply,
  onFilterRemove,
}: WorkflowActionPanelFiltersProps) {
  // Initialize state with useReducer
  const [addFilterFunction, removeFilterFunction, clearFilterFunctions, appliedFilters, addAppliedFilter, removeAppliedFilter] = useFilteringEngineStore(state => [
    state.addFilterFunction,
    state.removeFilterFunction,
    state.clearFilterFunctions,
    state.appliedFilters,
    state.addAppliedFilter,
    state.removeAppliedFilter,
  ]);
  const [state, dispatch] = useReducer(reducer, {
    selectedFilter: null,
    filterView: 'main',
    minValue: '',
    maxValue: '',
    searchTerm: '',
    filteredOptions: [],
    showAvailableFilters: false
  });
  
  // Destructure state for easier access
  const {
    selectedFilter,
    filterView,
    minValue,
    maxValue,
    searchTerm,
    filteredOptions,
    showAvailableFilters
  } = state;
  
  useEffect(() => {
    if (selectedFilter?.options) {
      dispatch({ type: 'SET_FILTERED_OPTIONS', payload: selectedFilter.options });
    }
  }, [selectedFilter]);
  
  const handleFilterSelect = (filter: FilterOption) => {
    dispatch({ type: 'SELECT_FILTER', payload: filter });
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    dispatch({ type: 'SET_SEARCH_TERM', payload: term });
    
    if (selectedFilter?.options) {
      const filtered = selectedFilter.options.filter(option => 
        option.name.toLowerCase().includes(term.toLowerCase()) || term.toLowerCase().includes(option.id.toString())
      );
      dispatch({ type: 'SET_FILTERED_OPTIONS', payload: filtered });
    }
  };
  
  const handleApplyFilter = () => {
    if (!selectedFilter) return;
    
    if (selectedFilter.type === 'range') {
      const min = parseFloat(minValue);
      const max = parseFloat(maxValue);
      
      if (isNaN(min) || isNaN(max)) return;
      
      const newFilter: AppliedFilter = {
        id: selectedFilter.id,
        name: selectedFilter.name,
        value: [min, max],
        filterFunction: { name: selectedFilter.id, filterFn: (item) => {
          console.log('Range filter for', selectedFilter.id, 'min:', min, 'max:', max);
          
          // Try different paths to find the value
          let itemValue: number | undefined;
          
          if (item?.filters?.value !== undefined) {
            itemValue = Number(item.filters.value);
          } else if (item?.value !== undefined) {
            itemValue = Number(item.value);
          } else if (item?.jobValue !== undefined) {
            itemValue = Number(item.jobValue);
          } else if (item?.[selectedFilter.id] !== undefined) {
            itemValue = Number(item[selectedFilter.id]);
          }
          
          // If itemValue is undefined or NaN, filter it out
          if (itemValue === undefined || isNaN(itemValue)) {
            return false;
          }
          
          // Now we know itemValue is a valid number
          return itemValue >= min && itemValue <= max;
        } }
      };

      // console.log("Select Filter", newFilter);
      
      addAppliedFilter(newFilter);
      
      // Add the filter function to the store
      addFilterFunction(newFilter.filterFunction);
      
      // Reset form state
      dispatch({ type: 'BACK_TO_MAIN' });

    }
  };
  
  const handleSelect = (option: any) => {
    if (!selectedFilter) return;
    
    const newFilter: AppliedFilter = {
      id: option.id,
      name: option.name,
      value: option.value,
      filterFunction: option.filterfunction
    };

    // console.log("Select Filter", newFilter);
    
    addAppliedFilter(newFilter);
    
    // Add the filter function to the store
    addFilterFunction(newFilter.filterFunction);
    
    // Reset form state
    dispatch({ type: 'BACK_TO_MAIN' });
    

  };
  
  const handleRemoveFilter = (filterId: string) => {
    // Find the filter to be removed
    const filterToRemove = appliedFilters.find(filter => filter.id === filterId);
    
    removeAppliedFilter(filterId);
    
    removeFilterFunction(filterToRemove?.filterFunction.name || '');
  };
  
  const handleBackToMain = () => {
    dispatch({ type: 'BACK_TO_MAIN' });
  };
  
  // Clear all filters from both local state and the store
  const handleClearAllFilters = () => {
    dispatch({ type: 'BACK_TO_MAIN' });
    
    // Clear filters from the store (this now also clears appliedFilters)
    clearFilterFunctions();
    
    // Clear local state
    dispatch({ type: 'SET_FILTERED_OPTIONS', payload: [] });
  };
  

    // Clear all filters on mount for fresh start
    useEffect(() => {
      clearFilterFunctions();
    }, [clearFilterFunctions]);

  
  const renderMainView = () => (
    <>
      {appliedFilters.length > 0 && (
        <SelectedFiltersSection>
          {appliedFilters.map(filter => (
            <SelectedFilterItem key={filter.id}>
              <div>
                <FilterName>{filter.name}</FilterName>
                <FilterValue>
                  {Array.isArray(filter.value) 
                    ? filter.value.join(' - ')
                    : filter.value}
                </FilterValue>
              </div>
              <CloseButton onClick={() => handleRemoveFilter(filter.id)}>
                <Icon type="close" color="#fff" />
              </CloseButton>
            </SelectedFilterItem>
          ))}
          
          {/* Show available filters when the Add Filter button has been clicked */}
          {showAvailableFilters && filters.length > 0 && (
            <FilterContainer>
              {filters
                .filter(filter => !appliedFilters.some(af => af.id === filter.id))
                .map(filter => (
                  <FilterItem 
                    key={filter.id} 
                    onClick={() => handleFilterSelect(filter)}
                  >
                    {filter.name}
                  </FilterItem>
                ))}
            </FilterContainer>
          )}
        </SelectedFiltersSection>
      )}
      
      {appliedFilters.length === 0 && (
        <>
          <FilterContainer>
            {filters.map(filter => (
              <FilterItem 
                key={filter.id} 
                onClick={() => handleFilterSelect(filter)}
              >
                {filter.name}
              </FilterItem>
            ))}
          </FilterContainer>
        </>
      )}
    </>
  );

  const renderJobValueForm = () => (
    <FilterValueForm>
      <FormInputsContainer>
        <div className="input-wrapper" style={{ width: '100%' }}>
          <input
            type="number"
            name="minValue"
            placeholder="Minimum value"
            value={minValue}
            onChange={(e) => dispatch({ type: 'SET_MIN_VALUE', payload: e.target.value })}
            style={{
              height: '40px',
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #4a5568',
              backgroundColor: '#2D3748',
              color: '#fff',
              fontSize: '16px',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <div className="input-wrapper" style={{ width: '100%' }}>
          <input
            type="number"
            name="maxValue"
            placeholder="Maximum value"
            value={maxValue}
            onChange={(e) => dispatch({ type: 'SET_MAX_VALUE', payload: e.target.value })}
            style={{
              height: '40px',
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #4a5568',
              backgroundColor: '#2D3748',
              color: '#fff',
              fontSize: '16px',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>
      </FormInputsContainer>
      <ContinueButton onClick={handleApplyFilter}>CONTINUE</ContinueButton>
      <div style={{ marginTop: '12px' }}>
        <AddFilterButton onClick={handleBackToMain}>BACK</AddFilterButton>
      </div>
    </FilterValueForm>
  );

  const renderSelectView = () => (
    <StateSelectionContainer>
      <StyledSearchInputWrapper>
        <WorkflowFiltersSearchInput
          placeholder="Search"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </StyledSearchInputWrapper>
      <SelectList>
        {filteredOptions.map(option => (
          <StateItem 
            key={option.id} 
            onClick={() => handleSelect(option)}
          >
            <StateCount>{option.id}</StateCount>
            <StateName>{option.name}</StateName>
          </StateItem>
        ))}
      </SelectList>
      <div style={{ marginTop: '16px' }}>
        <AddFilterButton onClick={handleBackToMain}>BACK</AddFilterButton>
      </div>
    </StateSelectionContainer>
  );

  return (
    <StyledWorkflowActionPanelFilters data-testid="workflow-action-panel-filters">
      {filterView === 'main' && renderMainView()}
      {filterView === 'jobValue' && renderJobValueForm()}
      {filterView === 'stateSelect' && renderSelectView()}
      {filterView === 'main' && appliedFilters.length > 0 && (
        <AddFilterButton onClick={() => dispatch({ type: 'TOGGLE_AVAILABLE_FILTERS' })}>
          {showAvailableFilters ? 'HIDE FILTERS' : 'ADD FILTER'}
        </AddFilterButton>
      )}
    </StyledWorkflowActionPanelFilters>
  );
}