import React from 'react';

import { Item } from '../../Components/BOQ/LineItemModify/LineItemModify';
import {
  BOQItemsResponse,
  BOQTampleteResponse,
  getBillingItemsForLimitedOptionalItemsBOQ,
  JobFeeOptions,
} from '../../Engine/helpers/BOQ-utils';
import {
  BOQComplete,
  PreselectedOptionalItem,
} from '../BOQComplete/BOQComplete';

interface BOQItems {
  id: number;
  name: string;
  type: string;
  skills: number[];
  optional_items: number[];
  includes_after_hours_items: boolean;
  allow_custom_items: boolean;
  compulsory_items: number[];
  claim_type: number[];
}

interface BOQLimitedOptionalItemsProps {
  boqItems: BOQItemsResponse;
  boqTemplates: BOQTampleteResponse[];
  templateName: string;
  travelDistance?: number;
  createCustomItemButton?: boolean;
  excessAmount?: number;
  jobFeeOptions?: JobFeeOptions;
  vat?: number;
  preselectedOptionalItem?: PreselectedOptionalItem;
  _formContext?: any;
  initialValue?: string;
  invoiceHeading?: string;
  boqDate?: string;
  boqNotes?: string;
  iconUrl?: string;
}

const handleItemsChange = (updatedItems: Item[]) => {
  console.log('Updated items:', updatedItems);
};

export function BOQLimitedOptionalItems({
  boqItems = { items: [] },
  boqTemplates = [],
  templateName,
  travelDistance,
  createCustomItemButton,
  excessAmount,
  jobFeeOptions,
  boqDate,
  boqNotes,
  vat,
  initialValue,
  invoiceHeading,
  preselectedOptionalItem,
  iconUrl,
}: BOQLimitedOptionalItemsProps) {
  // Display all items without separation
  // const allItems: Item[] = boqItems.items; // build type error fix

  return (
    <BOQComplete
      travelDistance={travelDistance}
      createCustomItemButton={createCustomItemButton}
      preselectedOptionalItem={preselectedOptionalItem}
      excessAmount={excessAmount}
      jobFeeOptions={jobFeeOptions}
      boqDate={boqDate}
      vat={vat}
      iconUrl={iconUrl}
      initialValue={initialValue}
      invoiceHeading={invoiceHeading}
      lineItemsTableProps={{
        compulsoryItems: [], // Using all items here
        optionalItems: getBillingItemsForLimitedOptionalItemsBOQ(
          boqTemplates,
          boqItems,
          templateName
        ), // No optional/compulsory distinction
        columnNames: {
          description: 'Description',
          quantity: 'Quantity',
          unitPrice: 'Unit Price',
          total: 'Total',
        },
        onItemsChange: handleItemsChange,
      }}
      invoiceSummaryNotesProps={{
        notes: '',
      }}
      invoiceSummaryProps={{
        // vat: 15,
        subTotal: 0,
      }}
    />
  );
}
