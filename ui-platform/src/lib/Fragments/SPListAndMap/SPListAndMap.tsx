import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import styled from 'styled-components';
import ElementaryThemedMap, {
  ServiceProviderData,
} from '../../Components/Maps/ElementaryThemedMap/ElementaryThemedMap';
import { additionalFontStyling } from '../../Utilities';
import FormBuilder from '../form-builder/FormBuilder';
import { FormConfig } from '../form-builder/types/form.config';
import { Headings } from '../Headings';
import { OptionType } from '../OpenDropdownList/OpenDropdownList';

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  grid-template-rows: auto;
  grid-template-areas: 'SPList SPMap';
  gap: ${(props) => `${props.theme.GapXxl}`};
  height: fit-content;
  width: 100%;
  align-items: start;
  justify-items: center;
`;

const SPListArea = styled.div`
  grid-area: SPList;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  height: auto;
  width: 80%;
  gap: ${(props) => props.theme.GapXxl};
  align-self: start;
  position: sticky;
  top: 0;
`;

const SPMapArea = styled.div`
  grid-area: SPMap;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  align-items: center;
  justify-items: center;
  overflow: hidden;
  border-radius: ${(props) => props.theme.RadiusXl};
  height: 95vh;
  width: 100%;
`;

const SPListHeadings = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-items: center;
  gap: ${(props) => props.theme.GapMd};
  white-space: nowrap;
`;

const SubHeading = styled.p<{ useFontTransformer?: boolean }>`
  all: unset;
  font-family: ${(props) =>
    props.theme.DesktopFlowsParagraphs.value.fontFamily};
  font-size: ${(props) => props.theme.DesktopFlowsParagraphs.value.fontSize};
  ${(props) => additionalFontStyling(props.theme.FontWeightsInter1, true)};
  line-height: 16.94px;
  text-align: center;
`;

type Props = {
  joblocation?: { lat: number; lng: number };
  theme: 'light' | 'dark';
  heading?: string;
  search?: boolean;
  streetView?: boolean;
  mapSize?: 'full' | 'small';
  subHeading?: string;
  useFontTransformer?: boolean;
  options: OptionType[];
  location: string;
  name: string;
  // New optional props to support custom labels
  labelKey?: string;
  getOptionLabel?: (option: OptionType) => string;
};

/**
 * A component that renders a list of teams and a map for selecting registered team leaders.
 *
 * @param {Object} props
 * @prop {Object} joblocation - The job location, an object with `lat` and `lng` properties.
 * @prop {'light'|'dark'} theme - The theme for the component, either 'light' or 'dark'.
 * @prop {boolean} [search=false] - Whether to show a search bar in the list.
 * @prop {boolean} [streetView=false] - Whether to show street view in the map.
 * @prop {'full'|'small'} [mapSize='full'] - The size of the map, either 'full' or 'small'.
 * @prop {string} [heading='Select Registered Team Leaders'] - The heading for the list.
 * @prop {string} [subHeading] - The subheading for the list.
 */
export const SPListAndMap = ({
  joblocation,
  theme,
  search,
  streetView,
  mapSize = 'full',
  heading = 'Select Registered Team Leaders',
  subHeading,
  options,
  location,
  name,
  labelKey,
  getOptionLabel,
}: Props) => {
  // Form state management
  const methods = useForm({
    defaultValues: {
      [name]: null,
    },
  });

  const { watch } = methods;
  const selectedOption = watch(name) as OptionType | null;

  // Hold the selected pin location.
  const [selectedLocation, setSelectedLocation] = useState<{
    lat: number;
    lng: number;
  }>(joblocation || { lat: 0, lng: 0 });
  const [originalLocation, setOriginalLocation] = useState<{
    lat: number;
    lng: number;
  }>({
    lat: 0,
    lng: 0,
  });

  // State for popup control
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {
    const latLng = location.split(',');
    setOriginalLocation({
      lat: parseFloat(latLng[0]),
      lng: parseFloat(latLng[1]),
    });
  }, [location]);

  // Update selected location when option changes
  useEffect(() => {
    if (
      selectedOption &&
      typeof selectedOption === 'object' &&
      selectedOption.lat &&
      selectedOption.lng
    ) {
      setSelectedLocation({
        lat: selectedOption.lat,
        lng: selectedOption.lng,
      });
      setShowPopup(true);
    } else {
      setShowPopup(false);
    }
  }, [selectedOption]);

  // Memoize the route object to avoid re-creation on every render.
  const route = useMemo(
    () => ({
      origin: originalLocation,
      destination: selectedLocation,
    }),
    [originalLocation, selectedLocation]
  );

  // Extract service provider data from selected option
  const serviceProviderData: ServiceProviderData | undefined = useMemo(() => {
    if (selectedOption && typeof selectedOption === 'object') {
      return {
        id: selectedOption.id || 0,
        full_name: selectedOption.full_name,
        sp: selectedOption.sp,
        name: selectedOption.name,
        ...selectedOption,
      };
    }
    return undefined;
  }, [selectedOption]);

  // Memoize the FormBuilder config so it remains referentially equal unless dependencies change.
  const formConfig: FormConfig = useMemo(
    () => ({
      style: {},
      controls: [
        {
          type: 'open-dropdown',
          title: 'Open Dropdown',
          name: name,
          options: options,
          joblocation: selectedLocation, // or joblocation from props if applicable
          theme: theme,
          location: location,
          labelKey: labelKey,
          getOptionLabel: getOptionLabel
            ? getOptionLabel
            : // fallback: if no labelKey is provided, default to using 'sp'
              (option: any) => option[labelKey || 'sp'] || '',
          width: '100%',
        },
      ],
    }),
    [name, options, selectedLocation, theme, location, labelKey, getOptionLabel]
  );

  // Handle popup close
  const handlePopupClose = () => {
    setShowPopup(false);
  };

  return (
    <FormProvider {...methods}>
      <Container>
        <SPListArea>
          <SPListHeadings>
            <Headings
              headings={[
                { level: 2, children: heading, type: 'section-heading' },
              ]}
              layout={{}}
            />
            {subHeading && <SubHeading>{subHeading}</SubHeading>}
          </SPListHeadings>
          <FormBuilder defaultValues={{}} config={formConfig} />
        </SPListArea>
        <SPMapArea>
          <ElementaryThemedMap
            joblocation={selectedLocation}
            theme={theme}
            search={search}
            streetView={streetView}
            size={mapSize}
            route={route}
            serviceProviderData={serviceProviderData}
            showPopup={showPopup}
            onPopupClose={handlePopupClose}
          />
        </SPMapArea>
      </Container>
    </FormProvider>
  );
};
