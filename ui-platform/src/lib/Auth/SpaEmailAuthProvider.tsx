import { jwtDecode } from 'jwt-decode';
import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { SplashScreenLoader } from '../Components/SplashScreen';
import { useAppStore } from '../Engine/useAppStore';
import { db } from '../services/IndexedDbService';
import { logger } from '../Utilities/templateLiteralLogger';

export interface User {
  email: string;
  first_name: string;
  token: string;
  user_type: string;
  sp_token: string;
  staff_member: StaffMember;
}

export interface StaffMember {
  id: number;
  sp: any;
  edit_states: any[];
  username: string;
  full_name: string;
  contact_number: string;
  email_address: string;
  authorised: boolean;
  mid: any;
  max_auth: any;
  staff_type: number;
  location: number;
  branches: number[];
  roles: number[];
}

interface AuthConfig {
  apiBaseUrl: string;
  loginUrlPath?: string;
  loginPagePath?: string;
  fieldAccessUrl: string;
}

interface EmailAuthInstance {
  staff_member?: StaffMember;
  token?: string;
  refreshToken?: string;
  authenticated: boolean;
  tokenParsed?: {
    exp?: number;
    iat?: number;
    auth_time?: number;
    jti?: string;
    iss?: string;
    aud?: string;
    sub?: string;
    typ?: string;
    azp?: string;
    nonce?: string;
    session_state?: string;
    acr?: string;
    'allowed-origins'?: string[];
    realm_access?: {
      roles: string[];
    };
    resource_access?: {
      [key: string]: {
        roles: string[];
      };
    };
    scope?: string;
    email_verified?: boolean;
    name?: string;
    preferred_username?: string;
    given_name?: string;
    family_name?: string;
    email?: string;
  };
  login: (
    username: string,
    password: string,
    options?: { redirectUri?: string }
  ) => Promise<boolean>;
  logout: (options?: { redirectUri?: string }) => void;
  updateToken: (minValidity: number) => Promise<boolean>;
  init: (options?: any) => Promise<boolean>;
  isTokenExpired: (minValidity?: number) => boolean;
  redirectUri?: string;
}

interface IContext {
  keycloak: EmailAuthInstance | undefined;
  authenticated: boolean;
}

const EmailAuthContext = createContext<IContext>(null!);

interface Props {
  config: AuthConfig;
  children: React.ReactNode;
  fieldAccessUrl: string;
  debug?: boolean;
}

// Helper function to update user in IndexedDB only if changed
async function updateUserIfChanged(staff_member: StaffMember) {
  if (!staff_member?.id) return;
  try {
    const existingUser = await db.user.get(staff_member.id.toString());
    if (!existingUser || existingUser.id !== staff_member.id.toString()) {
      await db.user.put(
        { id: staff_member.id.toString() },
        staff_member.id.toString()
      );
    }
  } catch (error) {
    console.error('Failed to update user in IndexedDB:', error);
  }
}

export const SpaEmailAuthProvider = ({
  children,
  config,
  fieldAccessUrl,
  debug = false,
}: Props) => {
  const [authenticated, setAuthenticated] = useState(false);
  const [token, setToken] = useState<string>();
  const [refreshToken, setRefreshToken] = useState<string>();
  const [staff_member, setStaffMember] = useState<StaffMember>();
  const [isInitialized, setIsInitialized] = useState(false);
  const setState = useAppStore((state: any) => state.setState);
  const authInitialized = useRef(false);

  logger.configure({
    prefix: '🔍[Email Auth Provider]:',
    enabled: debug,
    options: { style: { color: '#b8781d', backgroundColor: '#def4fa' } },
  });
  const log = logger.log;

  useEffect(() => {
    const checkAuthStatus = async () => {
      const storedToken = localStorage.getItem('4sure.web.jwtToken');
      if (storedToken) {
        try {
          const jwtPack: any = jwtDecode(storedToken);
          const currentTime = Math.floor(Date.now() / 1000);

          if (jwtPack.exp && jwtPack.exp > currentTime) {
            const response2 = await fetch(
              `${config.apiBaseUrl}/v1/staff_action/get_staffmember`,
              {
                method: 'POST',
                body: JSON.stringify({ staff_id: jwtPack?.user?.id }),
                headers: {
                  'Content-Type': 'application/json',
                  Accept: 'application/json',
                  Authorization: `Token ${storedToken || token}`,
                },
              }
            );
            const staff_member: StaffMember = (await response2.json()).payload;

            await updateUserIfChanged(staff_member);

            setToken(storedToken);
            setRefreshToken(storedToken);
            setAuthenticated(true);
            setStaffMember(staff_member);
            setState({
              auth: {
                token: storedToken,
                refreshToken: storedToken,
                authenticated: true,
                staff_member,
                user: { ...jwtPack?.user, staff_member },
              },
            });
          } else {
            // Token expired
            localStorage.removeItem('4sure.web.jwtToken');
            setAuthenticated(false);
            setState({
              auth: {
                token: undefined,
                refreshToken: undefined,
                authenticated: false,
              },
            });
          }
        } catch (error) {
          console.error('Invalid token:', error);
          localStorage.removeItem('4sure.web.jwtToken');
          setAuthenticated(false);
        }
      } else {
        setAuthenticated(false);
      }
      setIsInitialized(true);
      authInitialized.current = true;
    };

    checkAuthStatus();
  }, [setState]);

  const login = async (
    username: string,
    password: string,
    options?: { redirectUri?: string }
  ) => {
    try {
      if (!username || !password) {
        window.location.href =
          options?.redirectUri || `${config.loginPagePath}`;
        return false;
      }

      const res = await fetch(`${config.apiBaseUrl}${config.loginUrlPath}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ user: { email: username, password } }),
      });

      if (!res.ok) {
        const errorResponse = await res.json();
        log`Error from server? ${{ res, feedback: errorResponse }}`;
        throw new Error(errorResponse?.reason || 'Login failed');
      }

      const data: {
        success: boolean;
        user: User;
        token: string;
        errors?: { detail: string };
      } = await res.json();
      log`Response data - ${{ data }}`;
      if (!data?.success) {
        throw new Error(data?.errors?.detail || 'Login failed');
      }

      const jwtPack: any = jwtDecode(data?.user.token);
      localStorage.setItem('4sure.web.jwtToken', data?.user.token);

      const response2 = await fetch(
        `${config.apiBaseUrl}/v1/staff_action/get_staffmember`,
        {
          method: 'POST',
          body: JSON.stringify({ staff_id: jwtPack?.user?.id }),
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: `Token ${data?.user?.token}`,
          },
        }
      );
      const staff_member: StaffMember = (await response2.json()).payload;
      await updateUserIfChanged(staff_member);
      setToken(data?.user?.token);
      setRefreshToken(data?.user?.token);
      setAuthenticated(true);
      setStaffMember(staff_member);
      setState({
        auth: {
          token: data?.user?.token,
          refreshToken: data?.user?.token,
          authenticated: true,
          staff_member,
        },
      });
      // window.location.href = '/workflow';
      return true;
    } catch (error) {
      console.error('Login error:', error);
      setAuthenticated(false);
      throw error;
    }
  };

  const logout = () => {
    setToken(undefined);
    setRefreshToken(undefined);
    setAuthenticated(false);
    setState({
      auth: null,
    });
    localStorage.removeItem('4sure.web.jwtToken');
    setStaffMember(undefined);
    window.location.href = config.loginPagePath || '/auth/login';
  };

  const updateToken = async (minValidity: number): Promise<boolean> => {
    if (!token) return false;

    try {
      const response = await fetch(`${config.apiBaseUrl}/v2/auth/user`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Token ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data: { user: User } = await response.json();
      const user = data.user;
      const jwtPack: any = jwtDecode(user?.token);
      localStorage.setItem('4sure.web.jwtToken', user.token);
      if (authenticated) {
        const response2 = await fetch(
          `${config.apiBaseUrl}/v1/staff_action/get_staffmember/`,
          {
            method: 'POST',
            body: JSON.stringify({ staff_id: jwtPack?.user?.id }),
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
              Authorization: `Token ${user.token}`,
            },
          }
        );
        const staff_member: StaffMember = (await response2.json()).payload;
        setToken(user.token);
        setRefreshToken(user.token);
        setStaffMember(staff_member);
        setState({
          auth: {
            ...user,
            authenticated: true,
            ...data,
            jwtPack,
            staff_member,
          },
        });

        return true;
      }
      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
      return false;
    }
  };

  const init = async (options?: any): Promise<boolean> => {
    return authenticated;
  };

  const value = useMemo(
    () => ({
      authenticated,
      keycloak: {
        staff_member,
        token,
        refreshToken,
        authenticated,
        login,
        logout,
        updateToken,
        init,
        tokenParsed: token ? JSON.parse(atob(token.split('.')[1])) : undefined,
        isTokenExpired: (minValidity = 0) => {
          if (!token) return true;
          try {
            const parsed = JSON.parse(atob(token.split('.')[1]));
            const exp = parsed.exp * 1000; // Convert to milliseconds
            return exp < new Date().getTime() + minValidity * 1000;
          } catch {
            return true;
          }
        },
      } as EmailAuthInstance,
    }),
    [authenticated, token, refreshToken]
  );

  if (!isInitialized) {
    return <SplashScreenLoader />;
  }

  return (
    <EmailAuthContext.Provider value={value}>
      {children}
    </EmailAuthContext.Provider>
  );
};

export const useSpaEmail = () => {
  const context = useContext(EmailAuthContext);
  if (!context) {
    throw new Error('useSpaEmail must be used within an SpaEmailAuthProvider');
  }
  return context;
};
