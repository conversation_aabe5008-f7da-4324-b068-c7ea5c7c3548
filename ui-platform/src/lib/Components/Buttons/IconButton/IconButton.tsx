import React, { ButtonHTMLAttributes, CSSProperties, ReactNode } from 'react';
import styled from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { IconButtonWrapper } from './IconButtonWrapper/IconButtonWrapper';

export interface IIconButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  // onClick?: () => void;
  disabled?: boolean;
  active?: boolean;
  icon?: IconTypes;
  iconColor?: string;
  wrapperStyle?: CSSProperties;
}

const Container = styled(({ icon, color, ...rest }) => {
  const { active, ...IconButtonProps } = rest;

  return (
    <button {...IconButtonProps}>{<Icon type={icon} color={color} />}</button>
  );
})`
  width: 32px;
  height: 32px;
  border: 1px solid ${(props) => props?.theme.ColorsBackgroundShell};
  box-sizing: border-box;
  overflow: hidden;
  cursor: pointer;
  align-items: center;
  border-radius: 104px;
  padding: var(--spacingxs);
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    background-color: ${(props) => props?.theme.ColorsUtilityColorFocus};
    cursor: default;
  }

  &:disabled {
    background: ${(props) => props?.theme.ColorsButtonColorPagenationDisabled};
    color: ${(props) => props?.theme.ColorsTypographyDisabled};
    border: 1px ${(props) => props?.theme.ColorsTypographyDisabled} solid;
    cursor: default;
  }

  && {
    ${(props) =>
      props?.active && {
        background: props?.theme.ColorsButtonColorPagenationActivated,
        cursor: 'default',
      }}
  }
`;

/**
 * A button with an icon that can be used for actions in various contexts.
 *
 * @param {IIconButtonProps} props
 * @param {string} props.icon The type of the icon to be displayed in the button.
 * @param {string} [props.iconColor] The color of the icon to be displayed in the button. Defaults to the primary color of the theme.
 * @param {React.ComponentProps<'button'>} [props] Other props to be passed to the button element.
 * @returns {React.ReactElement} A button element with an icon inside.
 */
export function IconButton({
  icon,
  iconColor,
  wrapperStyle,
  ...props
}: IIconButtonProps) {
  return (
    <IconButtonWrapper color={iconColor} style={wrapperStyle}>
      <Container icon={icon} {...props} />
    </IconButtonWrapper>
  );
}
