import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MenuList, MenuListItemConfig } from './MenuList';

const meta: Meta<typeof MenuList> = {
  title: 'Components/Menu/MenuList',
  component: MenuList,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    triggerIcon: {
      control: 'text',
      description: 'Icon to display on the trigger button',
    },
    orientation: {
      control: { type: 'radio' },
      options: ['left', 'right'],
      description: 'Orientation of the menu dropdown',
    },
    items: {
      control: 'object',
      description: 'Array of menu items to display',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleItems: MenuListItemConfig[] = [
  {
    id: 'profile',
    label: 'Profile',
    icon: 'user-01',
    href: '/profile',
    onClick: () => console.log('Profile clicked'),
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'edit-05',
    href: '/settings',
    onClick: () => console.log('Settings clicked'),
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: 'help-circle',
    href: '/help',
    onClick: () => console.log('Help clicked'),
  },
  {
    id: 'logout',
    label: 'Logout',
    icon: 'arrow-block-right',
    href: '/logout',
    onClick: () => console.log('Logout clicked'),
  },
];

export const Default: Story = {
  args: {
    items: sampleItems,
    triggerIcon: 'dots-vertical',
    orientation: 'left',
  },
};

export const RightOriented: Story = {
  args: {
    items: sampleItems,
    triggerIcon: 'dots-vertical',
    orientation: 'right',
  },
};

export const CustomTriggerIcon: Story = {
  args: {
    items: sampleItems,
    triggerIcon: 'menu-01',
    orientation: 'left',
  },
};

export const MinimalItems: Story = {
  args: {
    items: [
      {
        id: 'edit',
        label: 'Edit',
        icon: 'pencil-01',
        href: '/edit',
      },
      {
        id: 'delete',
        label: 'Delete',
        icon: 'trash-01',
        href: '/delete',
      },
    ],
    triggerIcon: 'dots-vertical',
    orientation: 'left',
  },
};

export const WithoutIcons: Story = {
  args: {
    items: [
      {
        id: 'option1',
        label: 'Option 1',
        href: '/option1',
      },
      {
        id: 'option2',
        label: 'Option 2',
        href: '/option2',
      },
      {
        id: 'option3',
        label: 'Option 3',
        href: '/option3',
      },
    ],
    triggerIcon: 'chevron-down',
    orientation: 'left',
  },
};
