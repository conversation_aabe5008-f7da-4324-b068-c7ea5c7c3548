import React, { useState, useRef, useEffect, ReactNode } from 'react';
import styled, { useTheme } from 'styled-components';
import { IconButton } from '../../Buttons/IconButton/IconButton';
import { Icon, IconTypes } from '../../Icons';

export interface MenuListItemConfig {
  id: string;
  label: string;
  icon?: IconTypes;
  href?: string;
  onClick?: () => void;
}

export interface MenuListProps {
  /** The icon to display on the trigger button */
  triggerIcon?: IconTypes;
  /** Array of menu items to display */
  items: MenuListItemConfig[];
  /** Additional styling for the menu container */
  additionalStyling?: string;
  /** Orientation of the menu dropdown */
  orientation?: 'left' | 'right';
  /** Gap between trigger and dropdown */
  dropdownGap?: string;
  /** Custom class name for the trigger button */
  triggerClassName?: string;
  /** Custom class name for the dropdown */
  dropdownClassName?: string;
}



const Container = styled.div<{ orientation: 'left' | 'right'; showMenu: boolean }>`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: ${(props) => (props.orientation === 'left' ? 'start' : 'end')};
  z-index: ${(props) => (props.showMenu ? 9999 : 'auto')};
`;

const TriggerButton = styled(IconButton)`
  background-color: transparent;
  border-color: transparent;
  &:hover {
    background-color: ${(props) => props.theme.ColorsButtonColorActionPanelHover};
  }
`;



const MenuDropdown = styled.div<{
  showMenu: boolean;
  dropdownGap?: string;
  additionalStyling?: string;
  orientation: 'left' | 'right';
}>`
  position: absolute;
  top: 40%;
  ${(props) => props.orientation === 'left' ? 'left: calc(100% - 15px)' : 'right: calc(100% - 15px)'};
  box-shadow: 0px 12px 23px 5px rgba(7, 14, 17, 0.25);
  backdrop-filter: blur(9px);
  border-radius: 4px;
  background-color: rgba(40, 48, 51, 0.84);
  border: 0.5px solid #696969;
  box-sizing: border-box;
  overflow: hidden;
  z-index: 2000;
  width: max-content;
  min-width: 200px;
  pointer-events: auto;
  
  animation-name: ${(props) => (props.showMenu ? 'slideDown' : 'slideUp')};
  animation-duration: 0.3s;
  animation-timing-function: ease-in-out;
  display: ${(props) => (props.showMenu ? 'block' : 'none')};
  opacity: ${(props) => (props.showMenu ? 1 : 0)};

  ${(props) => props.additionalStyling}

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateX(-10px);
      visibility: hidden;
    }
    to {
      opacity: 1;
      transform: translateX(0);
      visibility: visible;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateX(0);
      visibility: visible;
    }
    to {
      opacity: 0;
      transform: translateX(-10px);
      visibility: hidden;
    }
  }
`;

const MenuItemLink = styled.a`
  display: flex;
  align-items: center;
  gap: ${(props) => props.theme.GapSm};
  padding: ${(props) => props.theme.SpacingSm};
  text-decoration: none;
  color: ${(props) => props.theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
  border-radius: ${(props) => props.theme.RadiusXs};
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: ${(props) => props.theme.ColorsTextListHover};
    color: ${(props) => props.theme.ColorsTypographySecondary};
  }

  &:focus {
    outline: 2px solid ${(props) => props.theme.ColorsButtonColorActionPanelPrimary};
    outline-offset: -2px;
  }
`;

const MenuItemIcon = styled(Icon)`
  width: 20px;
  height: 20px;
  flex-shrink: 0;
`;

const MenuItemLabel = styled.span`
  flex: 1;
  white-space: nowrap;
`;

/**
 * MenuList component that displays a trigger icon which opens a dropdown menu
 * containing clickable link items with icons and labels.
 */
export const MenuList: React.FC<MenuListProps> = ({
  triggerIcon = 'dots-vertical',
  items,
  additionalStyling,
  orientation = 'left',
  dropdownGap,
  triggerClassName,
  dropdownClassName,
}) => {
  const [showMenu, setShowMenu] = useState<boolean>(false);
  const triggerRef = useRef<HTMLDivElement | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);
  const theme = useTheme();

  // Handle click outside to close menu and global menu close events
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setShowMenu(false);
      }
    };

    const handleCloseAllMenus = () => {
      setShowMenu(false);
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('closeAllMenus', handleCloseAllMenus);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('closeAllMenus', handleCloseAllMenus);
    };
  }, []);

  const handleTriggerClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    event.preventDefault();
    
    // Close any other open menus by dispatching a custom event
    if (!showMenu) {
      window.dispatchEvent(new CustomEvent('closeAllMenus'));
    }
    
    setShowMenu(!showMenu);
  };

  const handleItemClick = (item: MenuListItemConfig, event: React.MouseEvent) => {
    // Call custom onClick if provided
    if (item.onClick) {
      event.preventDefault();
      item.onClick();
    } else if (item.href) {
      // Allow default navigation behavior for href
      // Don't prevent default here to allow normal link navigation
    }
    // Close menu after item click
    setShowMenu(false);
  };

  return (
    <Container orientation={orientation} showMenu={showMenu} className={triggerClassName}>
        <div ref={triggerRef}>
          <TriggerButton
            icon={triggerIcon}
            color={theme.ColorsButtonColorActionPanelPrimary}
            active={showMenu}
            onClick={handleTriggerClick}
          />
        </div>
        
        <MenuDropdown
          ref={menuRef}
          showMenu={showMenu}
          dropdownGap={dropdownGap}
          additionalStyling={additionalStyling}
          orientation={orientation}
          className={dropdownClassName}
          data-testid="menu-list-dropdown"
        >
        {items.map((item) => (
          <MenuItemLink
            key={item.id}
            // href={item.href}
            onClick={(e) => {
              e.stopPropagation();
              handleItemClick(item, e);
            }}
          >
            {item.icon && (
              <MenuItemIcon
                type={item.icon}
                size={20}
                color={theme.ColorsIconColorTertiary}
              />
            )}
            <MenuItemLabel>{item.label}</MenuItemLabel>
          </MenuItemLink>
        ))}
      </MenuDropdown>
    </Container>
  );
};
