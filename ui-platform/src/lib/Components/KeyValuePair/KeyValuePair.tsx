import React from 'react';
import styled from 'styled-components';

const KeyValuePairContainer = styled.div`
  display: flex;
  justify-content: space-between;
  width: auto;
  margin: 0.3rem 0;

  &.align-default {
    .left-col {
      text-align: right;
    }

    .right-col {
      text-align: left;
    }
  }

  &.align-even {
    .left-col {
      text-align: center;
    }

    .right-col {
      text-align: center;
    }
  }

  &.align-ends {
    .left-col {
      text-align: left;
    }

    .right-col {
      text-align: right;
    }
  }

  &.align-left {
    justify-content: left;
    .left-col,
    .right-col {
      text-align: left;
    }
  }

  &.size-extra-small {
    font-size: 0.5rem;
  }

  &.size-small {
    font-size: 0.8rem;
  }

  &.size-medium {
    font-size: 1rem;
  }

  &.size-large {
    font-size: 1.2rem;
  }

  &.color-default {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
  }

  &.color-preferred {
    color: ${(props) => props.theme.ColorsUtilityColorFocus};
  }

  &.color-alternative {
    color: ${(props) => props.theme.ColorsUtilityColorWarning};
  }
  &.color-warning {
    color: ${(props) => props.theme.ColorsUtilityColorError};
  }
  &.color-proceed {
    color: ${(props) => props.theme.ColorsUtilityColorSuccess};
  }

  &.text-default {
    text-transform: capitalize;
  }

  &.text-none {
    text-transform: none;
  }
`;

const LeftCol = styled.div<{ keyColor?: string }>`
  flex: 1;
  text-align: right;
  padding-right: 1rem;
  width: 200px;
  color: ${(props) => props.keyColor || 'inherit'};
`;

const RightCol = styled.div<{ valueColor?: string; cosy?: boolean }>`
  flex: 1;
  text-align: left;
  padding-left: 1rem;
  width: ${props => props.cosy ? 'auto' : '400px'};
  color: ${(props) => props.valueColor || 'inherit'};
`;

interface KeyValuePairProps {
  numbering?: boolean;
  index?: number;
  keyName?: string;
  value?: any;
  styleClasses?: string;
  keyColor?: string;
  valueColor?: string;
  cosy?: boolean;
}

/**
 * Renders a key-value pair with optional numbering and heading.
 *
 * @param {object} props - The properties for the KeyValuePair component.
 * @param {boolean} [props.numbering=false] - Whether to display numbering for each item.
 * @param {number} [props.index=0] - The index of the KeyValuePair in a list.
 * @param {string} [props.keyName=''] - The key name of the KeyValuePair.
 * @param {any} [props.value] - The value of the KeyValuePair.
 * @param {string} [props.styleClasses=''] - The style classes for the KeyValuePair.
 * @param {string} [props.keyColor] - Optional color for the key text.
 * @param {string} [props.valueColor] - Optional color for the value text.
 * @param {boolean} [props.cosy=false] - Whether to remove the fixed width constraint on the right column.
 * @returns {JSX.Element} The rendered KeyValuePair component.
 */
export const KeyValuePair: React.FC<KeyValuePairProps> = ({
  numbering = false,
  index = 0,
  keyName = '',
  value,
  styleClasses = '',
  keyColor,
  valueColor,
  cosy = false,
}) => {
  /**
   * Checks the type of the given property.
   * @param {object | []} prop The property to be checked.
   * @returns {string} The type of the property: 'object', 'array', or ''.
   */
  const checkProp = (prop: [] | object) =>
    prop instanceof Object && !Array.isArray(prop)
      ? 'object'
      : Array.isArray(prop)
      ? 'array'
      : '';

  return (
    <KeyValuePairContainer className={styleClasses}>
      <LeftCol keyColor={keyColor}>{`${numbering ? `${index + 1}.` : ''} ${keyName}`}</LeftCol>
      <RightCol valueColor={valueColor} cosy={cosy}>
        {checkProp(value) === 'object'
          ? Object.keys(value).map((subkey) => (
              <div key={subkey}>{`${subkey}: ${value[subkey] || '-'}`}</div>
            ))
          : checkProp(value) === 'array'
          ? value.map((akey: any, i: any) => (
              <React.Fragment key={i}>
                {Object.keys(akey).map((subkey) => (
                  <div key={subkey}>{`${subkey}: ${akey[subkey] || '-'}`}</div>
                ))}
              </React.Fragment>
            ))
          : `${value || '-'}`}
      </RightCol>
    </KeyValuePairContainer>
  );
};

// export default KeyValuePair;
