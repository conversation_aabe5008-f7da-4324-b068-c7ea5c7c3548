import {
  <PERSON><PERSON><PERSON><PERSON>,
  Directions<PERSON><PERSON>er,
  GoogleMap,
  Info<PERSON><PERSON>ow,
  <PERSON><PERSON>,
  useJsApiLoader,
} from '@react-google-maps/api';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled, { css } from 'styled-components';
import Truck from '../../public/images/truck.png';

type Pin = {
  id: number;
  lat: number;
  lng: number;
  icon: string;
  [key: string]: any;
};

// Service provider data structure for popup content
export type ServiceProviderData = {
  id: number;
  full_name?: string;
  sp?: string;
  name?: string;
  [key: string]: any;
};

type MapComponentProps = {
  joblocation: {
    lat: number;
    lng: number;
  };
  theme: 'light' | 'dark';
  search?: boolean; // New prop for search functionality
  streetView?: boolean; // New prop for street view control
  size?: 'full' | 'small';
  route?: {
    origin: { lat: number; lng: number };
    destination: { lat: number; lng: number };
  };
  pins?: Pin[];
  // New props for popup functionality
  serviceProviderData?: ServiceProviderData;
  showPopup?: boolean;
  onPopupClose?: () => void;
};

const SearchWrapper = styled(({ mode, ...rest }) => <div {...rest} />)`
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
  width: 347px;
  max-width: 100%;

  input {
    width: 100%;
    border-radius: 5px;
    padding: 10px;
    border: 1px solid;
    ${(props) =>
      props.mode === 'light' &&
      css`
        color: ${(props) => props?.theme.ColorsTypographyPrimary};
        background: ${(props) => props?.theme.ColorsOverlaySurfaceOverlay};
        border-color: ${(props) => props?.theme.ColorsStrokesDefault};
      `}
    ${(props) =>
      props.mode === 'dark' &&
      css`
        color: ${(props) => props?.theme.ColorsTypographyPrimary};
        background: ${(props) => props?.theme.ColorsOverlaySurfaceOverlay};
        border-color: ${(props) => props?.theme.ColorsStrokesDefault};
      `}
  }
`;

// Styled components for the popup content
const PopupContent = styled.div<{ mode: 'light' | 'dark' }>`
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  max-width: 300px;
  font-family: ${(props) =>
    props?.theme?.DesktopFlowsParagraphs?.value?.fontFamily || 'inherit'};

  ${(props) =>
    props.mode === 'light' &&
    css`
      background: ${(props) =>
        props?.theme.ColorsOverlaySurfaceOverlay || '#ffffff'};
      color: ${(props) => props?.theme.ColorsTypographyPrimary || '#000000'};
      border: 1px solid
        ${(props) => props?.theme.ColorsStrokesDefault || '#e0e0e0'};
    `}

  ${(props) =>
    props.mode === 'dark' &&
    css`
      background: ${(props) =>
        props?.theme.ColorsOverlaySurfaceOverlay || '#2d2d2d'};
      color: ${(props) => props?.theme.ColorsTypographyPrimary || '#ffffff'};
      border: 1px solid
        ${(props) => props?.theme.ColorsStrokesDefault || '#404040'};
    `}
`;

const PopupTitle = styled.h3`
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
`;

const PopupDistance = styled.p`
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
`;

const PopupCloseButton = styled.button<{ mode: 'light' | 'dark' }>`
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  line-height: 1;

  ${(props) =>
    props.mode === 'light' &&
    css`
      color: ${(props) => props?.theme.ColorsTypographyPrimary || '#000000'};
      &:hover {
        background: ${(props) =>
          props?.theme.ColorsStrokesDefault || '#f0f0f0'};
      }
    `}

  ${(props) =>
    props.mode === 'dark' &&
    css`
      color: ${(props) => props?.theme.ColorsTypographyPrimary || '#ffffff'};
      &:hover {
        background: ${(props) =>
          props?.theme.ColorsStrokesDefault || '#404040'};
      }
    `}

  &:focus {
    outline: 2px solid
      ${(props) => props?.theme.ColorsStrokesDefault || '#0066cc'};
    outline-offset: 2px;
  }
`;

/**
 * A reusable, themed, Google Map component with search and street view capabilities.
 *
 * @param {MapComponentProps} props - The component props.
 * @param {object} props.joblocation - The job location coordinates.
 * @param {string} props.theme - The theme of the map.
 * @param {boolean} [props.search=false] - Whether to display the search bar.
 * @param {boolean} [props.streetView=false] - Whether to display the street view.
 * @param {string} [props.size='full'] - The size of the map.
 * @return {JSX.Element} The job map component.
 */
export const ElementaryThemedMap: React.FC<MapComponentProps> = ({
  joblocation,
  theme,
  search = false,
  streetView = false,
  size,
  route,
  pins,
  serviceProviderData,
  showPopup = false,
  onPopupClose,
}) => {
  const [lat, setLat] = useState(joblocation.lat);
  const [lng, setLng] = useState(joblocation.lng);
  const [mode, setMode] = useState(theme);
  const [address, setAddress] = useState('');
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [directions, setDirections] =
    useState<google.maps.DirectionsResult | null>(null);

  // Popup state management
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{
    lat: number;
    lng: number;
  } | null>(null);

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: 'AIzaSyA28JqiZDQ8_CYVbdLKsrA-l_E0iDkW0pw',
    libraries: ['places'],
  });

  const mapStyles = {
    light: [],
    dark: [
      {
        elementType: 'geometry',
        stylers: [{ color: '#212121' }],
      },
      {
        elementType: 'labels.text.fill',
        stylers: [{ color: '#cccccc' }],
      },
      {
        elementType: 'labels.text.stroke',
        stylers: [{ color: '#000000' }],
      },
      {
        featureType: 'administrative',
        elementType: 'geometry',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'administrative.land_parcel',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'administrative.neighborhood',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      {
        featureType: 'road',
        elementType: 'geometry',
        stylers: [{ color: '#cccccc' }],
      },
      {
        featureType: 'road',
        elementType: 'labels',
        stylers: [{ visibility: 'on' }],
      },
      // ... (other dark mode styles)
    ],
  };

  const mapSize = useMemo(() => {
    switch (size) {
      case 'full':
        return { width: '100%', height: '100%' };
      case 'small':
      default:
        return { width: '390px', height: '350px' };
    }
  }, [size]);

  const containerStyle = {
    width: '390px',
    height: '350px',
  };

  /**
   * Handles the event when the user selects a different place from the autocomplete suggestions.
   * It updates the latitude and longitude state variables with the selected place's coordinates.
   */
  const handlePlaceChanged = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current.getPlace();
      if (place.geometry) {
        const newLat = place.geometry.location?.lat();
        const newLng = place.geometry.location?.lng();
        if (newLat !== undefined && newLng !== undefined) {
          setLat(newLat);
          setLng(newLng);
        }
      }
    }
  };

  /**
   * Handles the event when the user presses the Enter key on the address input.
   * It uses the Google Maps Geocoder API to geocode the address and update the
   * latitude and longitude state variables with the geocoded coordinates.
   * If the geocoding is not successful, it shows an alert with the error message.
   * @param {React.KeyboardEvent<HTMLInputElement>} e The keyboard event
   */
  const handleKeyPress = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode({ address: address }, (results, status) => {
        if (status === 'OK' && results && results[0].geometry) {
          const newLat = results[0].geometry.location.lat();
          const newLng = results[0].geometry.location.lng();
          setLat(newLat);
          setLng(newLng);
        } else {
          alert(
            'Geocode was not successful for the following reason: ' + status
          );
        }
      });
    }
  };

  useEffect(() => {
    setLat(joblocation.lat);
    setLng(joblocation.lng);
  }, [joblocation]);

  useEffect(() => {
    setMode(theme);
  }, [theme]);

  // New effect to calculate and display the route if route prop is provided
  useEffect(() => {
    if (route && window.google) {
      const directionsService = new window.google.maps.DirectionsService();
      directionsService.route(
        {
          origin: route.origin,
          destination: route.destination,
          travelMode: window.google.maps.TravelMode.DRIVING,
        },
        (result, status) => {
          if (status === window.google.maps.DirectionsStatus.OK) {
            setDirections(result);
          } else {
            console.error('Error fetching directions', result);
          }
        }
      );
    }
  }, [route]);

  // Handle popup state based on showPopup prop
  useEffect(() => {
    if (showPopup && route?.destination) {
      setIsPopupOpen(true);
      setPopupPosition(route.destination);
    } else {
      setIsPopupOpen(false);
      setPopupPosition(null);
    }
  }, [showPopup, route?.destination]);

  // Handle marker click for destination marker
  const handleMarkerClick = useCallback(() => {
    if (route?.destination && serviceProviderData) {
      setIsPopupOpen(true);
      setPopupPosition(route.destination);
    }
  }, [route?.destination, serviceProviderData]);

  // Handle popup close
  const handlePopupClose = useCallback(() => {
    setIsPopupOpen(false);
    setPopupPosition(null);
    onPopupClose?.();
  }, [onPopupClose]);

  // Handle escape key to close popup
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isPopupOpen) {
        handlePopupClose();
      }
    };

    if (isPopupOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isPopupOpen, handlePopupClose]);

  // Extract distance from directions result
  const getDistanceText = useCallback(() => {
    if (directions?.routes?.[0]?.legs?.[0]?.distance?.text) {
      return directions.routes[0].legs[0].distance.text;
    }
    return null;
  }, [directions]);

  // Get service provider display name
  const getServiceProviderName = useCallback(() => {
    if (!serviceProviderData) return 'Service Provider';
    return (
      serviceProviderData.full_name ||
      serviceProviderData.sp ||
      serviceProviderData.name ||
      'Service Provider'
    );
  }, [serviceProviderData]);

  return isLoaded ? (
    <div style={{ position: 'relative', ...mapSize }}>
      <GoogleMap
        mapContainerStyle={size ? mapSize : containerStyle}
        center={{ lat, lng }}
        zoom={6} // Suburb-level zoom setting
        options={{
          styles: mapStyles[mode],
          mapTypeControl: false,
          fullscreenControl: false,
          streetViewControl: streetView,
        }}
      >
        {route?.origin && (
          <Marker
            position={route.origin}
            icon={{
              url: Truck,
            }}
            label="A"
            onClick={handleMarkerClick}
            clickable={!!serviceProviderData}
            cursor={serviceProviderData ? 'pointer' : 'default'}
          />
        )}
        {route?.destination && (
          <Marker
            position={route.destination}
            label="B"
            onClick={handleMarkerClick}
            clickable={!!serviceProviderData}
            cursor={serviceProviderData ? 'pointer' : 'default'}
          />
        )}
        {!route && <Marker position={{ lat, lng }} label="Job" />}
        {directions && <DirectionsRenderer directions={directions} />}

        {/* InfoWindow for service provider popup */}
        {isPopupOpen && popupPosition && serviceProviderData && (
          <InfoWindow
            position={popupPosition}
            onCloseClick={handlePopupClose}
            options={{
              pixelOffset: new window.google.maps.Size(0, -30),
              disableAutoPan: false,
            }}
          >
            <PopupContent mode={mode}>
              <PopupCloseButton
                mode={mode}
                onClick={handlePopupClose}
                aria-label="Close popup"
                type="button"
              >
                ×
              </PopupCloseButton>
              <PopupTitle>{getServiceProviderName()}</PopupTitle>
              {getDistanceText() && (
                <PopupDistance>Distance: {getDistanceText()}</PopupDistance>
              )}
            </PopupContent>
          </InfoWindow>
        )}
      </GoogleMap>
      {search && (
        <SearchWrapper mode={mode}>
          <Autocomplete
            onLoad={(ref) => (autocompleteRef.current = ref)}
            onPlaceChanged={handlePlaceChanged}
          >
            <input
              type="text"
              placeholder="Search Address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              onKeyPress={handleKeyPress}
            />
          </Autocomplete>
        </SearchWrapper>
      )}
    </div>
  ) : (
    <div
      style={{
        position: 'relative',
        ...mapSize,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      Loading...
    </div>
  );
};
export default ElementaryThemedMap;
