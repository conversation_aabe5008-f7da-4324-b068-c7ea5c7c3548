import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import ElementaryThemedMap from './ElementaryThemedMap';

const meta: Meta<typeof ElementaryThemedMap> = {
  component: ElementaryThemedMap,
  title: 'Components/Maps/ElementaryThemedMap',
};
export default meta;

type Story = StoryObj<typeof ElementaryThemedMap>;

export const ElementaryThemedMapStory: Story = {
  argTypes: {
    joblocation: {
      control: { type: 'object' },
      defaultValue: { lat: -26.2051, lng: 28.0471 },
    },
    theme: {
      control: { type: 'radio' },
      options: ['light', 'dark'],
      defaultValue: 'light',
    },
    search: {
      control: { type: 'boolean' },
      defaultValue: true,
    },
    streetView: {
      control: { type: 'boolean' },
      defaultValue: false,
    },
    showPopup: {
      control: { type: 'boolean' },
      defaultValue: false,
    },
  },
  args: {
    joblocation: { lat: -26.2051, lng: 28.0471 },
    theme: 'dark',
    search: true,
    streetView: false,
  },
};

export const WithPopup: Story = {
  args: {
    joblocation: { lat: -26.2051, lng: 28.0471 },
    theme: 'light',
    search: false,
    streetView: false,
    route: {
      origin: { lat: -26.2051, lng: 28.0471 },
      destination: { lat: -26.1514869, lng: 27.924998 },
    },
    serviceProviderData: {
      id: 109,
      full_name: 'Rodney Smith',
      sp: 'Service Provider Build & Cons (BigJoe)',
    },
    showPopup: true,
    onPopupClose: () => console.log('Popup closed'),
  },
};
