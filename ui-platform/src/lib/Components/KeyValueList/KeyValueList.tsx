import React, { useEffect, useState } from 'react';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import styled, { useTheme } from 'styled-components';
import {KeyValuePair} from '../KeyValuePair/KeyValuePair';
import {theme} from "../../utils/CustomTheme";

const KeyValueListContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem;
`;

const Heading = styled.h1`
  text-align: center;
  font-weight: ${(props) => props.theme.FontWeightsInter6};
  font-size: ${(props) => props.theme.FontSize4}px;
  color: ${(props) => props.color || 'inherit'};
`;

interface ColouredHeadingProps {
  headingColour?: 'default' | 'preferred' | 'alternative' | 'proceed' | 'warning';
  headingString: string;
}
interface KeyValueListProps {
  heading?: string;
  heading$?: Observable<string> | null | undefined;
  data$?: Observable<any>;
  width?: string;
  itemMargin?: string;
  align?: 'default' | 'even' | 'ends' | 'left';
  colour?: 'default' | 'preferred' | 'alternative' | 'proceed' | 'warning' ;
  numbering?: boolean;
  // colouredHeading?: any,
  colouredHeading?: ColouredHeadingProps,
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  textTransform?: 'default' | 'none' ;
  cosy?: boolean;
  itemKeyColors?: Record<string, string>;
  itemValueColors?: Record<string, string>;
}
/**
 * Renders a list of key-value pairs with optional numbering and heading.
 *
 * @param {Object} props - The properties for the KeyValueList component.
 * @param {boolean} [props.numbering=false] - Whether to display numbering for each item.
 * @param {string} [props.heading=''] - The heading for the list.
 * @param {Observable<string> | null | undefined} [props.heading$] - An observable for the heading.
 * @param {Observable<any>} [props.data$] - An observable for the data.
 * @param {string} [props.width=''] - The width of the list.
 * @param {string} [props.itemMargin=''] - The margin between each item.
 * @param {'default' | 'even' | 'ends' | 'left'} [props.align='default'] - The alignment of the list.
 * @param {'default' | 'preferred' | 'alternative' | 'proceed' | 'warning'} [props.colour='default'] - The color of the list.
 * @param {'small' | 'medium' | 'large' | 'extra-large'} [props.size='small'] - The size of the list.
 * @param {'default' | 'none'} [props.textTransform='default'] - The text transformation for the list.
 * @param {boolean} [props.cosy=false] - Whether to remove the fixed width constraint on the right column of key-value pairs.
 * @return {JSX.Element} The rendered KeyValueList component.
 */
export const KeyValueList: React.FC<KeyValueListProps> = ({
//   strapline = '',
  numbering = false,
  heading = '',
  heading$,
  data$,
  width = '',
  itemMargin = '',
  align = 'default',
  colouredHeading,
  colour = 'default',
  size = 'small',
  textTransform = 'default',
  cosy = false,
  itemKeyColors = {},
  itemValueColors = {},
  ...props
}) => {
  const [formattedData, setFormattedData] = useState([]);
  const [headingStr, setHeadingStr] = useState('');
  const styleClasses = `align-${align} size-${size} color-${colour} text-${textTransform}`;
  useEffect(() => {
    const subscription = heading$?.subscribe((heading) => setHeadingStr(heading));
    return () => subscription?.unsubscribe();
  }, [heading$]);
  console.log('THEME', theme)
  useEffect(() => {
    const subscription = data$
      ?.pipe(map((data) => (data instanceof Object && !Array.isArray(data) ? [data] : data)))
      .subscribe((data) => setFormattedData(data));
    return () => subscription?.unsubscribe();
  }, [data$]);

  /**
   * Returns a color from the theme based on the given key.
   * @param {string} colorKey - The key of the color to retrieve.
   * @returns {string} The color from the theme.
   */
  const getColorFromTheme = (colorKey: string): string => {
    console.log('', colorKey);
    switch (colorKey) {
      case 'preferred':
        return `#118ab2` // replace with your theme color
      case 'alternative':
        return '#b28511'; // replace with your theme color
      case 'proceed':
        return 'ff0000'; // replace with your theme color
      case 'warning':
        return '#b22411'; // replace with your theme color
      case 'default':
        return '#e5e5e5'
      default:
        return 'inherit'; // replace with your default theme color
    }
  };

  const resolveColor = (color?: string): string | undefined => {
    if (!color) return undefined;
    const themeKeys = ['default', 'preferred', 'alternative', 'proceed', 'warning'];
    return themeKeys.includes(color) ? getColorFromTheme(color) : color;
  };

  return (
    <div>
      {heading && !headingStr && <Heading>{heading}</Heading>} 
      {!headingStr && !colouredHeading && <Heading>{headingStr}</Heading>}
      {!headingStr && colouredHeading && colouredHeading.headingString && (
        <Heading color={colouredHeading.headingColour ? getColorFromTheme(colouredHeading?.headingColour): 'default'}>{colouredHeading.headingString}</Heading>
      )}
      {formattedData.map((list, i) => (
        <KeyValueListContainer key={i}>
          {Object.keys(list).map((key, index) => (
            <KeyValuePair
              key={key}
              numbering={numbering}
              index={index }
              keyName={key}
              value={list[key]}
              styleClasses={styleClasses}
              cosy={cosy}
              keyColor={resolveColor((itemKeyColors as any)[key])}
              valueColor={resolveColor((itemValueColors as any)[key])}
            />
          ))}
        </KeyValueListContainer>
      ))}
    </div>
  );
};

// export default KeyValueList;
