/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useCallback } from 'react';
import styled from 'styled-components';
import { JobAvailableCard } from '../../Cards/JobAvailableCard';
import { PaginationBar } from '../../Controllers/Pagination';
import './styles.css';
// import { ListItemProps, withItems } from '../../Widgets';
import { from } from 'rxjs';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount/PaginationPageCount';
import { Divider } from '../../Dividers/Divider';
import Filter from '../../Filter/Filter';
import { Heading } from '../../Heading/Heading';
import { Icon } from '../../Icons';
import { KeyValueList } from '../../KeyValueList/KeyValueList';
import ElementaryThemedMap from '../../Maps/ElementaryThemedMap/ElementaryThemedMap';
import { IAvailableJob } from '../../Models/IAvailableJob';
import { usePingWindow } from '../hooks/usePingWindow';
import { PingWindowContainer } from './PingWindowContainer/PingWindowContainer';
import { PingWindowOverlay } from './PingWindowOverlay/PingWindowOverlay';
import { useAvailableJobsStore } from '../../../Engine/hooks/';
import { JobAwardedOrLostCard } from '../../Cards/JobAwardedOrLostCard/JobAwardedOrLostCard';  
import { useCheckJobAvailabilityAndGetJobs } from '../../../Hooks/useCheckJobAvailabilityAndGetJobs';
// import { Loader } from '../../Loader/Loader';
import { StaffMember } from '../../../Auth';
import { useAppStore } from '../../../Engine';
import { Loader } from '../../Loader/Loader';






interface IPingWindowProps {
  // children: any;
  baseUrl: string;
  staffMember: StaffMember;
  width?: string;
  background?: string;
  availableJobs?: any[];
  filteredJobs?: any[];
  isOpen?: boolean;
  token?: string | null;
  showAwardedList?: boolean;
  showAvailableList?: boolean;
  skills?: Array<{
    id: number | string;
    name: string;
    active: boolean;
    mid: number | string;
  }>;
  showAwardedJobs?: boolean;
  showLostJobs?: boolean;
  shouldShowAwardedList?: boolean;
  shouldShowAvailableList?: boolean;
}

export const PingWindowAvailableJobsList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto; // Header, body, pagination
  height: 100%;
  // grid-gap: 2px;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 2px; // Consistent spacing between grid sections
`;

const PingWindowAvailableJobDetails = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  // width: 100%;
  display: grid;
  // grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Adjust the minmax parameters to control the width of the job cards */
  grid-auto-rows: min-content;
  border-radius: 4px;
  max-width: 798px;
  min-height: 500px;
  max-height: 500px;
  // background: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
  padding: 15px 15px 20px 15px;
`;
const PingWindowHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 1fr;
  // margin-bottom: 50px;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;
const PingWindowJobsAwardedList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: 0.2fr 1fr 1.8fr; // Header, body, pagination
  height: 100%;
  // grid-gap: 2px;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 17px; // Consistent spacing between grid sections
  > div:first-child {
    justify-self: center;
    align-self: right;
    text-align: right;
  }
  > div:nth-child(2) {
    display: grid;
    // padding-top: 40px;
    justify-self: center;
    align-self: center;
  }
  > div:nth-child(3) {
    display: grid;
    justify-self: center;
    align-self: center;
  }
`;
const PingWindowJobsAwardedListCloseButton = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  align-self: self-end;
  text-align: right;
  margin-bottom: 0;
  height: auto;
`;
const PingWindowJobsAwardedListIconHeader = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
   width: 100%;
  display: grid;
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 5px;
  height: auto;
  
  > div {
    display: grid;
    grid-template-rows: auto auto;
    justify-items: center;
    align-items: center;
    gap: 10px;
    text-align: center;
  }
  }
  
`;
const PingWindowJobsAwardedListBody = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  // Dynamically set grid columns based on which lists are populated
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 50px;
  height: auto;
  gap: 20px; // Add some spacing between columns
`;

const PingWindowFilter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  // > div:nth-child(2) {
  //   justify-self: flex-end;
  // }
`;
const PingWindowDetailsHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 5fr 1fr;
  box-sizing: border-box;
  // text-align: center;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;
const PingWindowDetailsBody = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  > div:nth-child(1) {
    justify-self: center;
    margin-right: 35px;
  }
  > div:nth-child(2) {
    justify-self: center;
    background: teal;
    width: auto;
    height: 350px;
  }
  box-sizing: border-box;
  padding: 30px 25px 20px 0;
  width: auto;
  // margin-right: 40px;
  margin: 50px 0 0 0;
`;

const PingWindowBody = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: repeat(
    5,
    minmax(78px, 78px)
  ); // Fixed height rows for up to 5 cards
  grid-auto-rows: 0; // Additional rows will have 0 height
  height: 100%;
  // margin-top: 17px;
  grid-gap: 4px;
  overflow-y: auto;
  min-height: 0;
  align-content: start; // Align items to the start of the grid cell
  padding-bottom: 4px;
  > * {
    transition: transform 0.3s ease-in-out;
  }

  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-track {
    background: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-thumb:hover {
    // background: ${(props) => props.theme.ColorsCardColorDashboardPositive};
    background: blue;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
`;
const PingWindowDetailsFooter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  box-sizing: border-box;
  padding: 5px 15px 5px 15px;
  margin-top: 10px;
`;

const IgnoreAllJobsWrapper = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid ;
  justify-content: space-evenly;
  box-sizing: border-box;
  padding: 5px 15px 5px 15px;
  margin-top: 10px;
`;

const PaginationWrapper = styled.div`
  width: 100%;
  align-self: end; // Align to bottom of grid cell
`;

/**
 * PingWindow Component
 * Displays a list of available jobs or job details with pagination, filtering, and options to accept/decline jobs.
 *
 * @component
 * @param {IPingWindowProps} props - The properties for the PingWindow component
 * @param {React.ReactNode} props.children - Child components passed to PingWindow
 * @param {string} [props.width] - Optional width for the component
 * @param {string} [props.background] - Optional background for the component
 * @param {number} [props.availableJobs] - Number of available jobs to display
 * @param {boolean} props.isOpen - Whether the PingWindow is open or closed
 *
 * @returns {React.FC<IPingWindowProps>} The PingWindow component
 *
 * Handles switching between job list view and job details view.
 * @param {boolean} data - The view state (true for list, false for details)
 *
 * Handles closing the PingWindow component.
 */


export const AvailableJobsPingWindow: React.FC<IPingWindowProps> = ({
  // isOpen: isOpenProp,
  availableJobs,
  token,
  baseUrl,
  staffMember,
  ...props
}) => {
  const {
    isPingWindowVisible,
    setIsPingWindowVisible,
    // activeConfig,
    // setActiveConfig,
  } = usePingWindow();
  // const [isOpen, setIsOpen] = useState(isPingWindowVisible);
  const [listView, setListView] = useState(true);
  const [selectedJobId, setSelectedJobId] = useState<number | string | null>(
    null
  );
  // const [jobs, setJobs]= useState<IAvailableJob[]>(availableJobs || [])
  const [animatingJobId, setAnimatingJobId] = useState<number | string | null>(
    null
  );
  const [jobDetails, setJobDetails] = useState<any>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const { filteredJobs, jobsAwarded, jobsLost, isOpen  } = useAvailableJobsStore();
  // const [isLoadingSkills, setIsLoadingSkills] = useState(true);

  // Add new state for tracking awarded list visibility
  const [isAwardedListVisible, setIsAwardedListVisible] = useState(true);
  const { registerJobInterest, isLoading } = useCheckJobAvailabilityAndGetJobs({ baseUrl, staffMember });
  const shouldShowAwardedList = jobsAwarded.length > 0 || jobsLost.length > 0;
  const allInfo = useAppStore((state: any) => state.allInfo);


  const [jobs, setJobs] = useState<IAvailableJob[]>(filteredJobs || []);
  const [activeFilters, setActiveFilters] = useState<{[key: string]: any}>({});
  const [sortOrder, setSortOrder] = useState<string>('newest'); // newest, oldest
  const [showIgnoreAllConfirmation, setShowIgnoreAllConfirmation] = useState(false);
  const [currentFilterType, setCurrentFilterType] = useState<string>(''); // Track current filter type

  const formatDateTime = (dateString: string, appointmentName: string) => {
    const date = new Date(dateString);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleTimeString('default', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
  };
  const parseLocation = (locationString: string) => {
    if (!locationString) return { lat: -26.206, lng: 26.206 }; // Default fallback
    const [lat, lng] = locationString.split(',').map(coord => parseFloat(coord.trim()));
    return { lat: lat || -26.206, lng: lng || 26.206 }; // Use fallback if parsing fails
  };

  const getJobDetailsData = (jobDetails: any, skills: Array<{id: number, name: string}> = []) => {
    if (!jobDetails) return from([{}]);
  
    console.log('JOB DETATILS', jobDetails);
    console.log('SKILLS in ping window', skills);
    return from([{
      'Suburb': jobDetails.suburb,
      'Type of job': jobDetails.skill,
      'Appointment': jobDetails.appointment,
      'Claim type': jobDetails.claim_type
    }]);
  };

  // Generate dynamic suburb filter options from available jobs
  const getSuburbFilterOptions = () => {
    const baseJobs = filteredJobs || [];
    const uniqueSuburbs = [...new Set(baseJobs.map(job => job.suburb).filter(Boolean))].sort();
    
    return [
      { text: 'All Suburbs', filterCondition: {name: 'All Suburbs', key: 'suburb', operator: 'contains', value: ''} },
      ...uniqueSuburbs.map(suburb => ({
        text: suburb,
        filterCondition: {name: suburb, key: 'suburb', operator: 'equals', value: suburb}
      }))
    ];
  };

  const sortJobs = useCallback((jobsList: IAvailableJob[], sortOrder: string) => {
    return [...jobsList].sort((a, b) => {
      // Sort by appointment.range_start date
      const dateA = new Date(a.appointment.range_start);
      const dateB = new Date(b.appointment.range_start);
      
      if (sortOrder === 'newest') {
        return dateB.getTime() - dateA.getTime(); // Newest appointment time first
      } else {
        return dateA.getTime() - dateB.getTime(); // Oldest appointment time first
      }
    });
  }, []);

  const applyFiltersAndSort = useCallback((jobsList: IAvailableJob[], filters: {[key: string]: any}, skills: any[], sortOrder: string) => {
    // First apply filters
    let filteredJobs = jobsList;
    
    if (Object.keys(filters).length > 0) {
      filteredJobs = jobsList.filter(job => {
        // Apply skill filter
        if (filters.skill) {
          const skill = skills.find((s:any) => s.id === Number(job.skill));
          const jobSkillName = skill?.name || 'Unknown Skill';
          if (jobSkillName.toLowerCase() !== filters.skill.toLowerCase()) {
            return false;
          }
        }

        // Add more filter types here as needed
        if (filters.suburb) {
          if (!job.suburb?.toLowerCase().includes(filters.suburb.toLowerCase())) {
            return false;
          }
        }

        return true;
      });
    }

    // Then apply sorting
    return sortJobs(filteredJobs, sortOrder);
  }, [sortJobs]);

  const handleFilterSelect = (filterItem: any) => {
    console.log('Filter selected:', filterItem);
    
    if (filterItem.filterCondition) {
      const { key, value } = filterItem.filterCondition;
      
      // Handle sorting separately
      if (key === 'sort') {
        setSortOrder(value);
        setCurrentFilterType('appointment');
        const baseJobs = filteredJobs || [];
        const filtered = applyFiltersAndSort(baseJobs, activeFilters, allInfo?.skills || [], value);
        setJobs(filtered);
        return;
      }
      
      // Update active filters
      const newFilters = { ...activeFilters };
      
      if (key === 'skill' && value) {
        newFilters.skill = value;
        setCurrentFilterType('skill');
      } else if (key === 'suburb' && value) {
        newFilters.suburb = value;
        setCurrentFilterType('suburb');
      } else {
        // Clear filter if no value
        delete newFilters[key];
        setCurrentFilterType('');
      }
      
      setActiveFilters(newFilters);
      
      // Apply filters and sorting to the job list
      const baseJobs = filteredJobs || [];
      const filtered = applyFiltersAndSort(baseJobs, newFilters, allInfo?.skills || [], sortOrder);
      setJobs(filtered);
    }
  };

  const handleSortToggle = () => {
    // Only allow sorting if current filter is NOT appointment (i.e., for suburb, skill, or no filter)
    if (currentFilterType !== 'appointment') {
      const newSortOrder = sortOrder === 'newest' ? 'oldest' : 'newest';
      setSortOrder(newSortOrder);
      
      const baseJobs = filteredJobs || [];
      const filtered = applyFiltersAndSort(baseJobs, activeFilters, allInfo?.skills || [], newSortOrder);
      setJobs(filtered);
    }
  };

  const handleIgnoreJob = async (jobId: number | string) => {
    try {
      await registerJobInterest(jobId, 0); // 0 for ignore
      setAnimatingJobId(jobId);
      setTimeout(() => {
        setJobs((prevJobs) => {
          const newJobs = prevJobs.filter((job) => job.id !== jobId);
          // calculate the new total number of pages
          const newTotalPages = Math.ceil(newJobs.length / 5);
          // If current page would be empty, move to previous page
          if (currentPage > newTotalPages && setCurrentPage) {
            setCurrentPage(Math.max(1, newTotalPages));
          }
          return newJobs;
        });
        setAnimatingJobId(null);
        setListView(true);
      }, 300);
    } catch (error) {
      console.error('Error ignoring job:', error);
      // Optionally handle error (show notification, etc.)
    }
  };

  const handleDeclineJob = async (jobId: number | string) => {
    try {
      await registerJobInterest(jobId, 0); // 0 for decline
      setAnimatingJobId(jobId);
      setTimeout(() => {
        setJobs((prevJobs) => {
          const newJobs = prevJobs.filter((job) => job.id !== jobId);
          // calculate the new total number of pages
          const newTotalPages = Math.ceil(newJobs.length / 5);
          // If current page would be empty, move to previous page
          if (currentPage > newTotalPages && setCurrentPage) {
            setCurrentPage(Math.max(1, newTotalPages));
          }
          return newJobs;
        });
        setAnimatingJobId(null);
        setListView(true);
      }, 300);
    } catch (error) {
      console.error('Error declining job:', error);
      // Optionally handle error (show notification, etc.)
    }
  };

  const handleAcceptJob = async (jobId: number | string) => {
    try {
      await registerJobInterest(jobId, 1); // 1 for accept
      setAnimatingJobId(jobId);
      setTimeout(() => {
        setJobs((prevJobs) => {
          const newJobs = prevJobs.filter((job) => job.id !== jobId);
          // calculate the new total number of pages
          const newTotalPages = Math.ceil(newJobs.length / 5);
          // If current page would be empty, move to previous page
          if (currentPage > newTotalPages && setCurrentPage) {
            setCurrentPage(Math.max(1, newTotalPages));
          }
          return newJobs;
        });
        setAnimatingJobId(null);
        setListView(true);
      }, 300);
    } catch (error) {
      console.error('Error accepting job:', error);
      // Optionally handle error (show notification, etc.)
    }
  };

  const handleIgnoreAllJobs = () => {
    setShowIgnoreAllConfirmation(true);
  };

  const handleConfirmIgnoreAll = () => {
    console.log('Confirming ignore all jobs - functionality to be implemented');
    setShowIgnoreAllConfirmation(false);
  };

  const handleCancelIgnoreAll = () => {
    setShowIgnoreAllConfirmation(false);
  };

  // Close handler for awarded list
  const handleAwardedListClose = () => {
    setIsAwardedListVisible(false);
  };

  // Add close handler for available jobs list
  const handleAvailableListClose = () => {
    setIsPingWindowVisible(false);
  };

  const handleClose = () => {
    setIsPingWindowVisible(false);
  };

  const registerInterestAndRemoveJob = async (
    job_id: number | string,
    interest: number,
    staffId?: string | number
  ) => {
    try {
      // const numericJobId = typeof job_id === 'string' ? parseInt(job_id) : job_id;
      await registerJobInterest(job_id, interest);
      setAnimatingJobId(job_id);
      // setJobs(jobs.filter((job) => job.id !== job_id));
      // Find the index of the job to remove
      // const jobindex = jobs.findIndex((job) => job.id === job_id);
      // After animation is complete, remove the job
      setTimeout(() => {
        setJobs((prevJobs) => {
          const newJobs = prevJobs.filter((job) => job.id !== job_id);
          // calculate the new total number of pages
          const newTotalPages = Math.ceil(newJobs.length / 5);
          // If current page would be empty, move to previous page
          if (currentPage > newTotalPages && setCurrentPage) {
            setCurrentPage(Math.max(1, newTotalPages));
          }
          // newJobs.splice(jobindex, 1);
          return newJobs;
        });
        setAnimatingJobId(null);
      }, 300);
    } catch (error) {
      console.error('Error registering job interest:', error);
    }

    setJobs((prevJobs) => prevJobs.filter((job) => job.id !== job_id));
  };

  const isJobVisible = (job: IAvailableJob) => {
    if (animatingJobId === job.id) {
      return true; // Keep the animating job visible during animation
    }

    // Calculate visible range for current page
    const startIndex = (currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    const jobIndex = jobs.findIndex((j) => j.id === job.id);

    return jobIndex >= startIndex && jobIndex < endIndex;
  };

  const data = from([
    {
      Suburb: 'Fourways',
      'Type of job': 'DStV L2 Installation',
      Appointment: '20-08-2024 Before 09:00 AM',
      'Request from Customer':
        'Please hoot when you get to the gate. Our bell does not work',
      'Claim type': 'Pricelock SP Own Stock',
    },
  ]);
  // useEffect(() => {
  //   if (typeof isOpenProp !== 'undefined') {
  //     setIsPingWindowVisible(isOpenProp);
  //   }
  // }, [isOpenProp, setIsPingWindowVisible]);
  useEffect(() => {
    console.log('Current jobsAwarded:', jobsAwarded);
    console.log('Current jobsLost:', jobsLost);
    console.log('shouldShowAwardedListTest:', shouldShowAwardedList);
  }, [jobsAwarded, jobsLost, shouldShowAwardedList]);

  // Effect to handle listView state based on shouldShowAwardedList
  useEffect(() => {
    if (shouldShowAwardedList) {
      setListView(false);
    }
  }, [shouldShowAwardedList]);

  // When availableJobs prop changes, update the jobs state and apply any active filters and sorting
  useEffect(() => {
    if (filteredJobs) {
      console.log('🔄 DEBUG: Filtered jobs received:', filteredJobs);
      console.log('🔄 DEBUG: Sample job structure:', filteredJobs[0]);
      const filtered = applyFiltersAndSort(filteredJobs, activeFilters, allInfo?.skills || [], sortOrder);
      setJobs(filtered);
    }
  }, [filteredJobs, activeFilters, allInfo?.skills, sortOrder, applyFiltersAndSort]);

  // Reset listView to true whenever the window visibility changes
  useEffect(() => {
    if (isPingWindowVisible) {
      setListView(true);
    }
  }, [isPingWindowVisible]);

  // Sync the ping window visibility with the store's isOpen state
  useEffect(() => {
    setIsPingWindowVisible(isOpen);
  }, [isOpen, setIsPingWindowVisible]);

  // useEffect(() => {
  //   console.log('isPingWindowVisible changed:', isPingWindowVisible);
  // }, [isPingWindowVisible]);

  async function handleDetailsClick(jobId: number | string) {
    console.log('Showing details for job:', jobId);
    setSelectedJobId(jobId); // Assuming the job data contains an id
    setListView(false);
    setIsLoadingDetails(true);

    try {
      const skills = allInfo?.skills || [];

      const selectedJob = jobs.find((job:any) => job.id === jobId);
      const getSkillNameById = (skillId: string | number | undefined) => {
        if (!skills || !skillId) return 'Unknown Skill';
        const skill = skills.find((s:any) => s.id === Number(skillId));
        return skill?.name || 'Unknown Skill';
      };
      if(!selectedJob) {
        setIsLoadingDetails(false);
        return;
      }

      // create jobDetials object from selected job
      const jobDetails = { 
        suburb: selectedJob.suburb,
        skill: getSkillNameById(selectedJob.skill),
        appointment: formatDateTime(selectedJob.appointment.range_start, selectedJob.appointment.appointment_name),
        claim_type: selectedJob.claim_type,
      }
      console.log('jobDetails', jobDetails);
      setJobDetails(jobDetails);
      setListView(false);
    } catch (error) {
      console.error('Error fetching job details:', error);
    } finally {
      setIsLoadingDetails(false);
    }
  }
  function handleBack() {
    setListView(true);
  }
  const { pages, currentPage, pageItems, setCurrentPage, ...rest } =
    useListPagination2({
      items: jobs,
      itemsPerPage: 5,
    });
  // Add loading check for main component
  if (isLoading) {
    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <Loader type="alert" text="Loading..." />
          </div>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }
  
  if (!isPingWindowVisible) return null;

  // Show awarded/lost jobs if either array has items

  if (shouldShowAwardedList && isPingWindowVisible && isAwardedListVisible) {
    const hasAwardedJobs = jobsAwarded.length > 0;
    const hasLostJobs = jobsLost.length > 0;
    const awardedOnly = hasAwardedJobs && !hasLostJobs;
    const lostOnly = !hasAwardedJobs && hasLostJobs;
    console.log('Rendering awarded/lost section:', {
      jobsAwarded,
      jobsLost,
      hasAwardedJobs,
      hasLostJobs,
      awardedOnly,
      lostOnly,
    });

    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <PingWindowJobsAwardedList>
            <PingWindowJobsAwardedListCloseButton>
              <div>
                <Icon
                  type="x-xircle"
                  onClick={handleAwardedListClose}
                  style={{ cursor: 'pointer' }}
                ></Icon>
              </div>
            </PingWindowJobsAwardedListCloseButton>
            <PingWindowJobsAwardedListIconHeader
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {jobsAwarded.length > 0 && (
                <div>
                  <Icon type="check-circle" size={80} color="green" />
                  <span>You have been awarded the following jobs:</span>
                </div>
              )}
              {jobsLost.length > 0 && (
                <div>
                  <Icon type="x-xircle" size={80} color="red" />
                  <span>You have NOT been awarded the following jobs:</span>
                </div>
              )}
            </PingWindowJobsAwardedListIconHeader>
            <PingWindowJobsAwardedListBody
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {hasAwardedJobs && (
                <div>
                  {jobsAwarded.map((job) => {
                    console.log('mapping awarded jobs', {
                      job,
                      mappedProps: {
                        job_id: job.id,
                        skill: job.skill,
                        area: job.area,
                        appointment_date: job.appointment.range_start,
                        time: job.appointment.appointment_name,
                      },
                    });
                    return (
                      <JobAwardedOrLostCard
                        key={job.id}
                        job_id={job.id}
                        skill={job.skill}
                        area={job.area}
                        appointment_date={job.appointment.range_start}
                        time={job.appointment.appointment_name}
                      />
                    );
                  })}
                </div>
              )}
              {hasLostJobs && (
                <div>
                  {jobsLost.map((job) => (
                    <JobAwardedOrLostCard
                      key={job.id}
                      job_id={job.id}
                      skill={job.skill}
                      area={job.area}
                      appointment_date={job.appointment.range_start}
                      time={job.appointment.appointment_name}
                    />
                  ))}
                </div>
              )}
            </PingWindowJobsAwardedListBody>
          </PingWindowJobsAwardedList>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }

  // Show ignore all confirmation view
  if (isPingWindowVisible && showIgnoreAllConfirmation) {
    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <PingWindowAvailableJobsList>
            <PingWindowHeader>
              <div>
                <h2 className="stripped">
                  Confirm Action
                </h2>
              </div>
              <div>
                <Icon
                  type="x-xircle"
                  onClick={handleCancelIgnoreAll}
                  style={{ cursor: 'pointer' }}
                ></Icon>
              </div>
            </PingWindowHeader>
            
            <div style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center', 
              padding: '40px 20px',
              gap: '30px',
              height: '100%'
            }}>
              <div style={{ 
                textAlign: 'center', 
                fontSize: '18px', 
                fontWeight: '500',
                color: 'inherit'
              }}>
                Are you sure you want to ignore all jobs?
              </div>
              
              <div style={{ 
                display: 'flex', 
                gap: '20px' 
              }}>
                <TextButton
                  btnValue="Yes, ignore all"
                  size="large"
                  actiontype="alternative"
                  onClick={handleConfirmIgnoreAll}
                />
                <TextButton
                  btnValue="Close"
                  size="large"
                  onClick={handleCancelIgnoreAll}
                />
              </div>
            </div>
          </PingWindowAvailableJobsList>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }

  return isPingWindowVisible && listView ? (
    <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobsList>
        <PingWindowHeader>
          <div>
            <h2 className="stripped">
              Indicate your availability for the following jobs:
            </h2>
          </div>
          <div>
            <Icon
              type="x-xircle"
              onClick={handleAvailableListClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowHeader>
        <PingWindowFilter>
     <div className="filter">
          <Filter
            onSelect={handleFilterSelect}
            filter={{
              buttonText: "Filter available jobs...",
              items: [
                { 
                  text: 'Skill', 
                  items: [
                    { text: 'All Skills', filterCondition: {name: 'All Skills', key: 'skill', operator: 'contains', value: ''} },
                    ...(allInfo?.skills || []).map((skill: any) => ({
                      text: skill.name,
                      filterCondition: {name: skill.name, key: 'skill', operator: 'equals', value: skill.name}
                    }))
                  ]
                },
                { 
                  text: 'Appointment Time', 
                  items: [
                    { text: 'Newest First', filterCondition: {name: 'Newest First', key: 'sort', operator: 'equals', value: 'newest'} },
                    { text: 'Oldest First', filterCondition: {name: 'Oldest First', key: 'sort', operator: 'equals', value: 'oldest'} }
                  ]
                },
                { 
                  text: 'Suburb', 
                  items: getSuburbFilterOptions()
                },
              ]
            }}
          ></Filter>
          {currentFilterType !== 'appointment' && (
            <div>
              <Icon 
                type="switch-vertical" 
                onClick={handleSortToggle}
                style={{ cursor: 'pointer' }}
              ></Icon>
            </div>
          )}
        </div>
    </PingWindowFilter>

        <PingWindowBody>
          {jobs.map(
            (job: IAvailableJob) =>
              isJobVisible(job) && (
                <JobAvailableCard
                  available_job={job}
                  key={job.id}
                  setDetailsView={() => handleDetailsClick(job.id)}
                  onInterestExpression={registerInterestAndRemoveJob}
                  isAnimating={animatingJobId === job.id}
                  isClicking={false}
                  declineClicking={false}
                  skills={allInfo?.skills || []}
                />
              )
          )}
        </PingWindowBody>

        <PaginationWrapper>
          <PaginationBar
            paginationItems={
              <PaginationPageCount
                pages={pages}
                currentPage={currentPage}
                {...rest}
              />
            }
          />
        </PaginationWrapper>
        <IgnoreAllJobsWrapper>
          <TextButton
            btnValue="Ignore all"
            size="large"
            onClick={() => handleIgnoreAllJobs()}
          ></TextButton>
        </IgnoreAllJobsWrapper>
      </PingWindowAvailableJobsList>
    </PingWindowContainer>
  </PingWindowOverlay>
) : (
  <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobDetails>
        <PingWindowDetailsHeader>
          <div>
            <h2 className="stripped">Job Information:</h2>
          </div>

          <div>
            <Icon
              type="x-xircle"
              onClick={handleClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowDetailsHeader>
        <PingWindowDetailsBody>
          <div style={{ textAlign: 'center', marginRight: '30px' }}>
            <Heading level={3}>Job Details</Heading>
            <KeyValueList data$={getJobDetailsData(jobDetails, allInfo?.skills)} cosy={true}></KeyValueList>
            {/* <Heading level={3}>What Matters</Heading>
            <span style={{ fontSize: '0.8rem' }}>
              
            </span> */}
          </div>
          <div>
            {(() => {
              const selectedJob = jobs.find(job => job.id === selectedJobId);
              console.log('🔍 DEBUG: Selected job for map:', selectedJob);
              console.log('🗺️ DEBUG: Job location field:', selectedJob?.location);
              console.log('📍 DEBUG: Parsed location:', parseLocation(selectedJob?.location || ''));
              return (
                <ElementaryThemedMap
                  joblocation={parseLocation(selectedJob?.location || '')}
                  theme="light"
                ></ElementaryThemedMap>
              );
            })()}
          </div>
        </PingWindowDetailsBody>
        <Divider size="fullWidth" background="grey" type="tabSmll"></Divider>
        <PingWindowDetailsFooter>
          <div>
            <TextButton
              btnValue="back"
              size="large"
              onClick={handleBack}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="ignore job"
              size="large"
              onClick={() => selectedJobId && handleIgnoreJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="decline job"
              size="large"
              actiontype="alternative"
              onClick={() => selectedJobId && handleDeclineJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="accept job"
              size="large"
              actiontype="preferred"
              onClick={() => selectedJobId && handleAcceptJob(selectedJobId)}
            ></TextButton>
          </div>
        </PingWindowDetailsFooter>
      </PingWindowAvailableJobDetails>
    </PingWindowContainer>
  </PingWindowOverlay>
);
};
