h2 {
  font-weight: 100;
}
h3 {
  font-weight: 300;
}
.stripped {
  margin: 0 0 0 0;
}
.headerTitle {
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 1fr;
  /* padding: 8px 0 15px 10px; */
  > div:nth-child(2) {
    padding-right: 20px;
    justify-self: flex-end;
  }
}
.headerDetailsTitle {
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 1fr;
  /* padding: 8px 0 15px 10px; */
  > div:nth-child(2) {
    padding-right: 20px;
    justify-self: flex-end;
  }
}
.filter {
  width: 100%;
  display: grid;
  grid-template-columns: 4fr 0.1fr;
  > div:first-child {
    padding: 20px 0 5px 15px;
    justify-self: flex-end;
    max-width: 50%;
  }
  > div:nth-child(2) {
    padding: 20px 15px 5px 15px;
    justify-self: flex-end;
    align-self: center;
  }
}
