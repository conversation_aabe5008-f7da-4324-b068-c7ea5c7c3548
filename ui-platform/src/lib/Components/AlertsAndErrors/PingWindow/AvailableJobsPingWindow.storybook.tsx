/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useCallback } from 'react';
import styled from 'styled-components';
import { JobAvailableCard } from '../../Cards/JobAvailableCard';
import { PaginationBar } from '../../Controllers/Pagination';
import './styles.css';
import { from } from 'rxjs';
import { TextButton } from '../../Buttons/TextButton/TextButton';
import {
  PaginationPageCount,
  useListPagination2,
} from '../../Controllers/Pagination/PaginationPageCount/PaginationPageCount';
import { Divider } from '../../Dividers/Divider';
import Filter from '../../Filter/Filter';
import { Heading } from '../../Heading/Heading';
import { Icon } from '../../Icons';
import { KeyValueList } from '../../KeyValueList/KeyValueList';
import ElementaryThemedMap from '../../Maps/ElementaryThemedMap/ElementaryThemedMap';
import { IAvailableJob } from '../../Models/IAvailableJob';
import { usePingWindow } from '../hooks/usePingWindow';
import { PingWindowContainer } from './PingWindowContainer/PingWindowContainer';
import { PingWindowOverlay } from './PingWindowOverlay/PingWindowOverlay';
import { useAvailableJobsStore } from '../../../Engine/hooks/';
import { JobAwardedOrLostCard } from '../../Cards/JobAwardedOrLostCard/JobAwardedOrLostCard';  
import { StaffMember } from '../../../Auth';
import { useAppStore } from '../../../Engine';
import { Loader } from '../../Loader/Loader';

// Import all the styled components from the main component
export const PingWindowAvailableJobsList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 100%;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 2px;
`;

const PingWindowAvailableJobDetails = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  display: grid;
  grid-auto-rows: min-content;
  border-radius: 4px;
  max-width: 798px;
  min-height: 500px;
  max-height: 500px;
  padding: 15px 15px 20px 15px;
`;

const PingWindowHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-columns: 5fr 1fr;
  margin-bottom: 5px;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;

const PingWindowJobsAwardedList = styled(({ open, ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-rows: 0.2fr 1fr 1.8fr;
  height: 100%;
  grid-auto-rows: min-content;
  border-radius: 4px;
  padding: 15px 15px 20px 15px;
  box-sizing: border-box;
  gap: 17px;
  > div:first-child {
    justify-self: center;
    align-self: right;
    text-align: right;
  }
  > div:nth-child(2) {
    display: grid;
    justify-self: center;
    align-self: center;
  }
  > div:nth-child(3) {
    display: grid;
    justify-self: center;
    align-self: center;
  }
`;

const PingWindowJobsAwardedListCloseButton = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: 1fr;
  align-self: self-end;
  text-align: right;
  margin-bottom: 0;
  height: auto;
`;

const PingWindowJobsAwardedListIconHeader = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
   width: 100%;
  display: grid;
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 5px;
  height: auto;
  
  > div {
    display: grid;
    grid-template-rows: auto auto;
    justify-items: center;
    align-items: center;
    gap: 10px;
    text-align: center;
  }
`;

const PingWindowJobsAwardedListBody = styled(({ ...rest }) => (
  <div {...rest}></div>
))`
  width: 100%;
  display: grid;
  grid-template-columns: ${(props) =>
    props.awardedOnly ? '1fr' : props.lostOnly ? '1fr' : '1fr 1fr'};
  margin-bottom: 50px;
  height: auto;
  gap: 20px;
`;

const PingWindowFilter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  height: auto;
`;

const PingWindowDetailsHeader = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: 5fr 1fr;
  box-sizing: border-box;
  height: auto;
  > div:nth-child(2) {
    justify-self: flex-end;
    align-self: center;
  }
`;

const PingWindowDetailsBody = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  > div:nth-child(1) {
    justify-self: center;
    margin-right: 35px;
  }
  > div:nth-child(2) {
    justify-self: center;
    background: teal;
    width: auto;
    height: 350px;
  }
  box-sizing: border-box;
  padding: 30px 25px 20px 0;
  width: auto;
  margin: 50px 0 0 0;
`;

const PingWindowBody = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid;
  grid-template-rows: repeat(5, minmax(78px, 78px));
  grid-auto-rows: 0;
  height: 100%;
  grid-gap: 4px;
  overflow-y: auto;
  min-height: 0;
  align-content: start;
  padding-bottom: 4px;
  > * {
    transition: transform 0.3s ease-in-out;
  }

  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-track {
    background: ${(props) => props.theme.ColorsCardColorJobCardPrimary};
    border-radius: 0 4px 4px 0;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: blue;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
`;

const PingWindowDetailsFooter = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  box-sizing: border-box;
  padding: 5px 15px 5px 15px;
  margin-top: 10px;
`;

const IgnoreAllJobsWrapper = styled(({ ...rest }) => <div {...rest}></div>)`
  width: 100%;
  display: grid ;
  justify-content: space-evenly;
  box-sizing: border-box;
  padding: 5px 15px 5px 15px;
  margin-top: 10px;
`;

const PaginationWrapper = styled.div`
  width: 100%;
  align-self: end;
`;

interface IPingWindowProps {
  baseUrl: string;
  staffMember: StaffMember;
  width?: string;
  background?: string;
  availableJobs?: any[];
  filteredJobs?: any[];
  isOpen?: boolean;
  token?: string | null;
  showAwardedList?: boolean;
  showAvailableList?: boolean;
  skills?: Array<{
    id: number | string;
    name: string;
    active: boolean;
    mid: number | string;
  }>;
  showAwardedJobs?: boolean;
  showLostJobs?: boolean;
  shouldShowAwardedList?: boolean;
  shouldShowAvailableList?: boolean;
  // Storybook specific props
  forceLoading?: boolean;
}

/**
 * Storybook-specific version of AvailableJobsPingWindow that doesn't use the problematic hook
 */
export const AvailableJobsPingWindowStorybook: React.FC<IPingWindowProps> = ({
  availableJobs,
  token,
  baseUrl,
  staffMember,
  forceLoading = false,
  ...props
}) => {
  const {
    isPingWindowVisible,
    setIsPingWindowVisible,
  } = usePingWindow();
  
  const [listView, setListView] = useState(true);
  const [selectedJobId, setSelectedJobId] = useState<number | string | null>(null);
  const [animatingJobId, setAnimatingJobId] = useState<number | string | null>(null);
  const [jobDetails, setJobDetails] = useState<any>(null);
  const { filteredJobs, jobsAwarded, jobsLost, isOpen } = useAvailableJobsStore();
  
  // Add new state for tracking awarded list visibility
  const [isAwardedListVisible, setIsAwardedListVisible] = useState(true);
  
  // MOCK the hook data instead of calling the real hook
  const mockHookData = {
    registerJobInterest: () => Promise.resolve(),
    isLoading: forceLoading, // Use prop to control loading
    availableJobs: [],
    localToken: 'mock-token'
  };
  
  const shouldShowAwardedList = jobsAwarded.length > 0 || jobsLost.length > 0;
  const allInfo = useAppStore((state: any) => state.allInfo);

  const [jobs, setJobs] = useState<IAvailableJob[]>(filteredJobs || []);
  const [activeFilters, setActiveFilters] = useState<{[key: string]: any}>({});
  const [sortOrder, setSortOrder] = useState<string>('newest'); // newest, oldest
  const [showIgnoreAllConfirmation, setShowIgnoreAllConfirmation] = useState(false);
  const [currentFilterType, setCurrentFilterType] = useState<string>(''); // Track current filter type

  const formatDateTime = (dateString: string, appointmentName: string) => {
    const date = new Date(dateString);
    const month = date.toLocaleString('default', { month: 'long' });
    const day = date.getDate();
    const time = date.toLocaleTimeString('default', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    return `${month} ${day} ${appointmentName.toLowerCase()} ${time}`;
  };

  const parseLocation = (locationString: string) => {
    if (!locationString) return { lat: -26.206, lng: 26.206 }; // Default fallback
    const [lat, lng] = locationString.split(',').map(coord => parseFloat(coord.trim()));
    return { lat: lat || -26.206, lng: lng || 26.206 }; // Use fallback if parsing fails
  };

  const getJobDetailsData = (jobDetails: any, skills: Array<{id: number, name: string}> = []) => {
    if (!jobDetails) return from([{}]);
  
    return from([{
      'Suburb': jobDetails.suburb,
      'Type of job': jobDetails.skill,
      'Appointment': jobDetails.appointment,
      'Claim type': jobDetails.claim_type
    }]);
  };

  const sortJobs = useCallback((jobsList: IAvailableJob[], sortOrder: string) => {
    return [...jobsList].sort((a, b) => {
      // Sort by appointment.range_start date
      const dateA = new Date(a.appointment.range_start);
      const dateB = new Date(b.appointment.range_start);
      
      if (sortOrder === 'newest') {
        return dateB.getTime() - dateA.getTime(); // Newest appointment time first
      } else {
        return dateA.getTime() - dateB.getTime(); // Oldest appointment time first
      }
    });
  }, []);

  const applyFiltersAndSort = useCallback((jobsList: IAvailableJob[], filters: {[key: string]: any}, skills: any[], sortOrder: string) => {
    // First apply filters
    let filteredJobs = jobsList;
    
    if (Object.keys(filters).length > 0) {
      filteredJobs = jobsList.filter(job => {
        // Apply skill filter
        if (filters.skill) {
          const skill = skills.find((s:any) => s.id === Number(job.skill));
          const jobSkillName = skill?.name || 'Unknown Skill';
          if (jobSkillName.toLowerCase() !== filters.skill.toLowerCase()) {
            return false;
          }
        }

        // Add more filter types here as needed
        if (filters.suburb) {
          if (!job.suburb?.toLowerCase().includes(filters.suburb.toLowerCase())) {
            return false;
          }
        }

        return true;
      });
    }

    // Then apply sorting
    return sortJobs(filteredJobs, sortOrder);
  }, [sortJobs]);

  // Generate dynamic suburb filter options from available jobs
  const getSuburbFilterOptions = () => {
    const baseJobs = filteredJobs || [];
    const uniqueSuburbs = [...new Set(baseJobs.map(job => job.suburb).filter(Boolean))].sort();
    
    return [
      { text: 'All Suburbs', filterCondition: {name: 'All Suburbs', key: 'suburb', operator: 'contains', value: ''} },
      ...uniqueSuburbs.map(suburb => ({
        text: suburb,
        filterCondition: {name: suburb, key: 'suburb', operator: 'equals', value: suburb}
      }))
    ];
  };



  const handleFilterSelect = (filterItem: any) => {
    console.log('Filter selected:', filterItem);
    
    if (filterItem.filterCondition) {
      const { key, value } = filterItem.filterCondition;
      
      // Handle sorting separately
      if (key === 'sort') {
        setSortOrder(value);
        setCurrentFilterType('appointment');
        const baseJobs = filteredJobs || [];
        const filtered = applyFiltersAndSort(baseJobs, activeFilters, allInfo?.skills || [], value);
        setJobs(filtered);
        return;
      }
      
      // Update active filters
      const newFilters = { ...activeFilters };
      
      if (key === 'skill' && value) {
        newFilters.skill = value;
        setCurrentFilterType('skill');
      } else if (key === 'suburb' && value) {
        newFilters.suburb = value;
        setCurrentFilterType('suburb');
      } else {
        // Clear filter if no value
        delete newFilters[key];
        setCurrentFilterType('');
      }
      
      setActiveFilters(newFilters);
      
      // Apply filters and sorting to the job list
      const baseJobs = filteredJobs || [];
      const filtered = applyFiltersAndSort(baseJobs, newFilters, allInfo?.skills || [], sortOrder);
      setJobs(filtered);
    }
  };

  const handleSortToggle = () => {
    // Only allow sorting if current filter is NOT appointment (i.e., for suburb, skill, or no filter)
    if (currentFilterType !== 'appointment') {
      const newSortOrder = sortOrder === 'newest' ? 'oldest' : 'newest';
      setSortOrder(newSortOrder);
      
      const baseJobs = filteredJobs || [];
      const filtered = applyFiltersAndSort(baseJobs, activeFilters, allInfo?.skills || [], newSortOrder);
      setJobs(filtered);
    }
  };

  const handleIgnoreJob = async (jobId: number | string) => {
    console.log('Mock: Ignoring job', jobId);
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  const handleDeclineJob = async (jobId: number | string) => {
    console.log('Mock: Declining job', jobId);
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  const handleAcceptJob = async (jobId: number | string) => {
    console.log('Mock: Accepting job', jobId);
    setAnimatingJobId(jobId);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== jobId));
      setAnimatingJobId(null);
      setListView(true);
    }, 300);
  };

  const handleIgnoreAllJobs = () => {
    setShowIgnoreAllConfirmation(true);
  };

  const handleConfirmIgnoreAll = () => {
    console.log('Mock: Confirming ignore all jobs - functionality to be implemented');
    setShowIgnoreAllConfirmation(false);
  };

  const handleCancelIgnoreAll = () => {
    setShowIgnoreAllConfirmation(false);
  };

  const handleAwardedListClose = () => {
    setIsAwardedListVisible(false);
  };

  const handleAvailableListClose = () => {
    setIsPingWindowVisible(false);
  };

  const handleClose = () => {
    setIsPingWindowVisible(false);
  };

  const registerInterestAndRemoveJob = async (job_id: number | string, interest: number) => {
    console.log('Mock: Registering interest', job_id, interest);
    setAnimatingJobId(job_id);
    setTimeout(() => {
      setJobs((prevJobs) => prevJobs.filter((job) => job.id !== job_id));
      setAnimatingJobId(null);
    }, 300);
  };

  const isJobVisible = (job: IAvailableJob) => {
    if (animatingJobId === job.id) {
      return true;
    }

    const startIndex = (currentPage - 1) * 5;
    const endIndex = startIndex + 5;
    const jobIndex = jobs.findIndex((j) => j.id === job.id);

    return jobIndex >= startIndex && jobIndex < endIndex;
  };

  // When availableJobs prop changes, update the jobs state and apply any active filters and sorting
  useEffect(() => {
    if (filteredJobs) {
      console.log('🔄 DEBUG (STORYBOOK): Filtered jobs received:', filteredJobs);
      console.log('🔄 DEBUG (STORYBOOK): Sample job structure:', filteredJobs[0]);
      const filtered = applyFiltersAndSort(filteredJobs, activeFilters, allInfo?.skills || [], sortOrder);
      setJobs(filtered);
    }
  }, [filteredJobs, activeFilters, allInfo?.skills, sortOrder, applyFiltersAndSort]);

  // Reset listView to true whenever the window visibility changes
  useEffect(() => {
    if (isPingWindowVisible) {
      setListView(true);
    }
  }, [isPingWindowVisible]);

  // Sync the ping window visibility with the store's isOpen state
  useEffect(() => {
    setIsPingWindowVisible(isOpen);
  }, [isOpen, setIsPingWindowVisible]);

  async function handleDetailsClick(jobId: number | string) {
    console.log('Showing details for job:', jobId);
    setSelectedJobId(jobId);
    setListView(false);

    try {
      const skills = allInfo?.skills || [];

      const selectedJob = jobs.find((job:any) => job.id === jobId);
      const getSkillNameById = (skillId: string | number | undefined) => {
        if (!skills || !skillId) return 'Unknown Skill';
        const skill = skills.find((s:any) => s.id === Number(skillId));
        return skill?.name || 'Unknown Skill';
      };
      if(!selectedJob) {
        return;
      }

      const jobDetails = { 
        suburb: selectedJob.suburb,
        skill: getSkillNameById(selectedJob.skill),
        appointment: formatDateTime(selectedJob.appointment.range_start, selectedJob.appointment.appointment_name),
        claim_type: selectedJob.claim_type,
      }
      setJobDetails(jobDetails);
      setListView(false);
    } catch (error) {
      console.error('Error fetching job details:', error);
    }
  }

  function handleBack() {
    setListView(true);
  }

  const { pages, currentPage, pageItems, setCurrentPage, ...rest } = useListPagination2({
    items: jobs,
    itemsPerPage: 5,
  });

  // Show loading if forced
  if (mockHookData.isLoading) {
    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
            <Loader type="alert" text="Loading..." />
          </div>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }
  
  if (!isPingWindowVisible) return null;

  // Show awarded/lost jobs if either array has items
  if (shouldShowAwardedList && isPingWindowVisible && isAwardedListVisible) {
    const hasAwardedJobs = jobsAwarded.length > 0;
    const hasLostJobs = jobsLost.length > 0;
    const awardedOnly = hasAwardedJobs && !hasLostJobs;
    const lostOnly = !hasAwardedJobs && hasLostJobs;

    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <PingWindowJobsAwardedList>
            <PingWindowJobsAwardedListCloseButton>
              <div>
                <Icon
                  type="x-xircle"
                  onClick={handleAwardedListClose}
                  style={{ cursor: 'pointer' }}
                ></Icon>
              </div>
            </PingWindowJobsAwardedListCloseButton>
            <PingWindowJobsAwardedListIconHeader
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {jobsAwarded.length > 0 && (
                <div>
                  <Icon type="check-circle" size={80} color="green" />
                  <span>You have been awarded the following jobs:</span>
                </div>
              )}
              {jobsLost.length > 0 && (
                <div>
                  <Icon type="x-xircle" size={80} color="red" />
                  <span>You have NOT been awarded the following jobs:</span>
                </div>
              )}
            </PingWindowJobsAwardedListIconHeader>
            <PingWindowJobsAwardedListBody
              awardedOnly={awardedOnly}
              lostOnly={lostOnly}
            >
              {hasAwardedJobs && (
                <div>
                  {jobsAwarded.map((job) => (
                    <JobAwardedOrLostCard
                      key={job.id}
                      job_id={job.id}
                      skill={job.skill}
                      area={job.area}
                      appointment_date={job.appointment.range_start}
                      time={job.appointment.appointment_name}
                    />
                  ))}
                </div>
              )}
              {hasLostJobs && (
                <div>
                  {jobsLost.map((job) => (
                    <JobAwardedOrLostCard
                      key={job.id}
                      job_id={job.id}
                      skill={job.skill}
                      area={job.area}
                      appointment_date={job.appointment.range_start}
                      time={job.appointment.appointment_name}
                    />
                  ))}
                </div>
              )}
            </PingWindowJobsAwardedListBody>
          </PingWindowJobsAwardedList>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }

  // Show ignore all confirmation view
  if (isPingWindowVisible && showIgnoreAllConfirmation) {
    return (
      <PingWindowOverlay visible={isPingWindowVisible}>
        <PingWindowContainer
          isOverlayVisible={isPingWindowVisible}
          setIsOverlayVisisble={setIsPingWindowVisible}
        >
          <PingWindowAvailableJobsList>
            <PingWindowHeader>
              <div>
                <h2 className="stripped">
                  Confirm Action
                </h2>
              </div>
              <div>
                <Icon
                  type="x-xircle"
                  onClick={handleCancelIgnoreAll}
                  style={{ cursor: 'pointer' }}
                ></Icon>
              </div>
            </PingWindowHeader>
            
            <div style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center', 
              padding: '40px 20px',
              gap: '30px',
              height: '100%'
            }}>
              <div style={{ 
                textAlign: 'center', 
                fontSize: '18px', 
                fontWeight: '500',
                color: 'inherit'
              }}>
                Are you sure you want to ignore all jobs?
              </div>
              
              <div style={{ 
                display: 'flex', 
                gap: '20px' 
              }}>
                <TextButton
                  btnValue="Yes, ignore all"
                  size="large"
                  actiontype="alternative"
                  onClick={handleConfirmIgnoreAll}
                />
                <TextButton
                  btnValue="Close"
                  size="large"
                  onClick={handleCancelIgnoreAll}
                />
              </div>
            </div>
          </PingWindowAvailableJobsList>
        </PingWindowContainer>
      </PingWindowOverlay>
    );
  }

  return isPingWindowVisible && listView ? (
    <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobsList>
        <PingWindowHeader>
          <div>
            <h2 className="stripped">
              Indicate your availability for the following jobs:
            </h2>
          </div>
          <div>
            <Icon
              type="x-xircle"
              onClick={handleAvailableListClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowHeader>
        <PingWindowFilter>
     <div className="filter">
          <Filter
            onSelect={handleFilterSelect}
            filter={{
              buttonText: "Filter available jobs...",
              items: [
                { 
                  text: 'Skill', 
                  items: [
                    { text: 'All Skills', filterCondition: {name: 'All Skills', key: 'skill', operator: 'contains', value: ''} },
                    ...(allInfo?.skills || []).map((skill: any) => ({
                      text: skill.name,
                      filterCondition: {name: skill.name, key: 'skill', operator: 'equals', value: skill.name}
                    }))
                  ]
                },
                { 
                  text: 'Appointment Time', 
                  items: [
                    { text: 'Newest First', filterCondition: {name: 'Newest First', key: 'sort', operator: 'equals', value: 'newest'} },
                    { text: 'Oldest First', filterCondition: {name: 'Oldest First', key: 'sort', operator: 'equals', value: 'oldest'} }
                  ]
                },
                { 
                  text: 'Suburb', 
                  items: getSuburbFilterOptions()
                },
              ]
            }}
          ></Filter>
          {currentFilterType !== 'appointment' && (
            <div>
              <Icon 
                type="switch-vertical" 
                onClick={handleSortToggle}
                style={{ cursor: 'pointer' }}
              ></Icon>
            </div>
          )}
        </div>
    </PingWindowFilter>

        <PingWindowBody>
          {jobs.map(
            (job: IAvailableJob) =>
              isJobVisible(job) && (
                <JobAvailableCard
                  available_job={job}
                  key={job.id}
                  setDetailsView={() => handleDetailsClick(job.id)}
                  onInterestExpression={registerInterestAndRemoveJob}
                  isAnimating={animatingJobId === job.id}
                  isClicking={false}
                  declineClicking={false}
                  skills={allInfo?.skills || []}
                />
              )
          )}
        </PingWindowBody>

        <PaginationWrapper>
          <PaginationBar
            paginationItems={
              <PaginationPageCount
                pages={pages}
                currentPage={currentPage}
                {...rest}
              />
            }
          />
        </PaginationWrapper>
        <IgnoreAllJobsWrapper>
          <TextButton
            btnValue="Ignore all"
            size="large"
            onClick={() => handleIgnoreAllJobs()}
          ></TextButton>
        </IgnoreAllJobsWrapper>
      </PingWindowAvailableJobsList>
    </PingWindowContainer>
  </PingWindowOverlay>
) : (
  <PingWindowOverlay visible={isPingWindowVisible}>
    <PingWindowContainer
      isOverlayVisible={isPingWindowVisible}
      setIsOverlayVisisble={setIsPingWindowVisible}
    >
      <PingWindowAvailableJobDetails>
        <PingWindowDetailsHeader>
          <div>
            <h2 className="stripped">Job Information:</h2>
          </div>

          <div>
            <Icon
              type="x-xircle"
              onClick={handleClose}
              style={{ cursor: 'pointer' }}
            ></Icon>
          </div>
        </PingWindowDetailsHeader>
        <PingWindowDetailsBody>
          <div style={{ textAlign: 'center', marginRight: '30px' }}>
            <Heading level={3}>Job Details</Heading>
            <KeyValueList data$={getJobDetailsData(jobDetails, allInfo?.skills)} cosy={true}></KeyValueList>
          </div>
          <div>
            {(() => {
              const selectedJob = jobs.find(job => job.id === selectedJobId);
              console.log('🔍 DEBUG (STORYBOOK): Selected job for map:', selectedJob);
              console.log('🗺️ DEBUG (STORYBOOK): Job location field:', selectedJob?.location);
              console.log('📍 DEBUG (STORYBOOK): Parsed location:', parseLocation(selectedJob?.location || ''));
              return (
                <ElementaryThemedMap
                  joblocation={parseLocation(selectedJob?.location || '')}
                  theme="light"
                ></ElementaryThemedMap>
              );
            })()}
          </div>
        </PingWindowDetailsBody>
        <Divider size="fullWidth" background="grey" type="tabSmll"></Divider>
        <PingWindowDetailsFooter>
          <div>
            <TextButton
              btnValue="back"
              size="large"
              onClick={handleBack}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="ignore job"
              size="large"
              onClick={() => selectedJobId && handleIgnoreJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="decline job"
              size="large"
              actiontype="alternative"
              onClick={() => selectedJobId && handleDeclineJob(selectedJobId)}
            ></TextButton>
          </div>
          <div>
            <TextButton
              btnValue="accept job"
              size="large"
              actiontype="preferred"
              onClick={() => selectedJobId && handleAcceptJob(selectedJobId)}
            ></TextButton>
          </div>
        </PingWindowDetailsFooter>
      </PingWindowAvailableJobDetails>
    </PingWindowContainer>
  </PingWindowOverlay>
);
};
