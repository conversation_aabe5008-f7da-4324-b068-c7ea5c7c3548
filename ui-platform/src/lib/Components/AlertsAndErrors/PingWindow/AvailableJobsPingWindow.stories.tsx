import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { AvailableJobsPingWindowStorybook } from './AvailableJobsPingWindow.storybook';
import { useAvailableJobsStore } from '../../../Engine/hooks';
import { useAppStore } from '../../../Engine';
import { IAvailableJob } from '../../Models';
import { useState, useEffect } from 'react';

// 🧪 Mock Skills
const mockSkills = [
  { id: 1, name: 'Plumbing', active: true, mid: 'H2902' },
  { id: 2, name: 'Electrical', active: true, mid: 'H2903' },
  { id: 3, name: 'Carpentry', active: true, mid: 'H2904' },
  { id: 4, name: 'Painting', active: true, mid: 'H2905' }
];

// 📦 Mock Jobs
const mockAvailableJobs: IAvailableJob[] = [
  {
    id: 1201,
    appointments: [],
    appointment: {
      id: 1201,
      after_hours: false,
      appointment_name: 'Before',
      appointment_type_id: '5',
      range_start: '2025-02-25T16:00:00',
      range_end: '2025-02-25T16:00:00',
    },
    location: '-26.204350, 28.047314',
    mid: '',
    address: '12 Windmill Road, Albertaville, 12345',
    skill: 1,
    customer: 'John Doe',
    cellnumber: '0801234567',
    area: 'Gauteng',
    claim_type: 'Bathroom Renovation',
    suburb: 'Roodepoort',
  },
  {
    id: 1202,
    appointments: [],
    appointment: {
      id: 1202,
      after_hours: false,
      appointment_name: 'At',
      appointment_type_id: '6',
      range_start: '2025-02-22T16:00:00',
      range_end: '2025-02-22T16:00:00',
    },
    location: '-25.746681, 28.188480',
    mid: '',
    address: '34 Elm Street, Springfield, 12345',
    skill: 2,
    appointment_date: 'October 14, 2024',
    customer: 'Jane Smith',
    cellnumber: '0809876543',
    area: 'Gauteng',
    claim_type: 'Bathroom Renovation',
    suburb: 'Roodepoort',
  },
  {
    id: 1203,
    appointments: [],
    appointment: {
      id: 1203,
      after_hours: true,
      appointment_name: 'Evening',
      appointment_type_id: '7',
      range_start: '2025-02-26T18:30:00',
      range_end: '2025-02-26T20:30:00',
    },
    location: '-26.195246, 28.034088',
    mid: '',
    address: '78 Oak Avenue, Johannesburg, 2001',
    skill: 3,
    customer: 'Michael Brown',
    cellnumber: '0823456789',
    area: 'Gauteng',
    claim_type: 'Garage Door Repair',
    suburb: 'Sandton',
  },
  {
    id: 1204,
    appointments: [],
    appointment: {
      id: 1204,
      after_hours: false,
      appointment_name: 'Morning',
      appointment_type_id: '3',
      range_start: '2025-02-27T09:00:00',
      range_end: '2025-02-27T11:00:00',
    },
    location: '-25.744340, 28.238890',
    mid: '',
    address: '156 Main Street, Pretoria, 0002',
    skill: 4,
    customer: 'Sarah Johnson',
    cellnumber: '0714567890',
    area: 'Gauteng',
    claim_type: 'Storm Damage',
    suburb: 'Roodepoort',
  },
  {
    id: 1205,
    appointments: [],
    appointment: {
      id: 1205,
      after_hours: false,
      appointment_name: 'Afternoon',
      appointment_type_id: '4',
      range_start: '2025-02-28T14:00:00',
      range_end: '2025-02-28T16:00:00',
    },
    location: '-25.858950, 28.183240',
    mid: '',
    address: '42 Garden Lane, Centurion, 0157',
    skill: 1,
    customer: 'David Wilson',
    cellnumber: '0845678901',
    area: 'Gauteng',
    claim_type: 'Bathroom Renovation',
    suburb: 'Sandton',
  },
  {
    id: 1206,
    appointments: [],
    appointment: {
      id: 1206,
      after_hours: true,
      appointment_name: 'Emergency',
      appointment_type_id: '8',
      range_start: '2025-02-24T20:00:00',
      range_end: '2025-02-24T22:00:00',
    },
    location: '-26.101060, 28.230120',
    mid: '',
    address: '289 Industrial Road, Kempton Park, 1619',
    skill: 2,
    customer: 'Lisa Anderson',
    cellnumber: '0756789012',
    area: 'Gauteng',
    claim_type: 'Storm Damage',
    suburb: 'Kempton Park',
  },
  {
    id: 1207,
    appointments: [],
    appointment: {
      id: 1207,
      after_hours: false,
      appointment_name: 'Weekend',
      appointment_type_id: '9',
      range_start: '2025-03-01T10:00:00',
      range_end: '2025-03-01T12:00:00',
    },
    location: '-26.097780, 27.982350',
    mid: '',
    address: '67 Sunset Boulevard, Randburg, 2194',
    skill: 3,
    customer: 'Robert Martinez',
    cellnumber: '0867890123',
    area: 'Gauteng',
    claim_type: 'Malicious Damage',
    suburb: 'Sandton',
  },
  {
    id: 1208,
    appointments: [],
    appointment: {
      id: 1208,
      after_hours: false,
      appointment_name: 'Standard',
      appointment_type_id: '5',
      range_start: '2025-03-03T13:00:00',
      range_end: '2025-03-03T15:00:00',
    },
    location: '-26.269720, 28.122160',
    mid: '',
    address: '123 Pine Street, Alberton, 1449',
    skill: 4,
    customer: 'Emma Thompson',
    cellnumber: '0778901234',
    area: 'Gauteng',
    claim_type: 'Malicious Damage',
    suburb: 'Alberton',
  },
  {
    id: 1209,
    appointments: [],
    appointment: {
      id: 1209,
      after_hours: false,
      appointment_name: 'Priority',
      appointment_type_id: '2',
      range_start: '2025-03-04T08:00:00',
      range_end: '2025-03-04T10:00:00',
    },
    location: '-26.188330, 28.320580',
    mid: '',
    address: '501 Valley View, Benoni, 1501',
    skill: 1,
    customer: 'James Rodriguez',
    cellnumber: '0689012345',
    area: 'Gauteng',
    claim_type: 'Bathroom Renovation',
    suburb: 'Sandton',
  },
  {
    id: 1210,
    appointments: [],
    appointment: {
      id: 1210,
      after_hours: true,
      appointment_name: 'Late Evening',
      appointment_type_id: '8',
      range_start: '2025-03-05T19:00:00',
      range_end: '2025-03-05T21:00:00',
    },
    location: '-26.212580, 28.262140',
    mid: '',
    address: '95 Heritage Drive, Boksburg, 1459',
    skill: 2,
    customer: 'Amanda Clarke',
    cellnumber: '0790123456',
    area: 'Gauteng',
    claim_type: 'Storm Damage',
    suburb: 'Boksburg',
  }
];

// 🎯 Mock Results
const mockAwardedJobs = [
  {
    id: "10981",
    skill: "Plumbing",
    area: "Downtown",
    appointment: {
      range_start: "2024-03-20",
      appointment_name: "14:00-16:00"
    }
  },
  {
    id: "10982",
    skill: "Electrical",
    area: "Midtown",
    appointment: {
      range_start: "2024-03-21",
      appointment_name: "09:00-11:00"
    }
  }
];

const mockLostJobs = [
  {
    id: "10983",
    skill: "Carpentry",
    area: "Uptown",
    appointment: {
      range_start: "2024-03-22",
      appointment_name: "13:00-15:00"
    }
  }
];

// ⏱ Hook
const useDelayedOpen = (delay: number, openInitial: boolean) => {
  const [isOpen, setIsOpen] = useState(false);
  useEffect(() => {
    if (openInitial) {
      const timeout = setTimeout(() => setIsOpen(true), delay);
      return () => clearTimeout(timeout);
    } else {
      setIsOpen(false);
    }
  }, [delay, openInitial]);
  return isOpen;
};

// 🧱 Meta
const meta: Meta<typeof AvailableJobsPingWindowStorybook> = {
  title: 'Components/AlertsAndErrors/PingWindow/AvailableJobsPingWindow',
  component: AvailableJobsPingWindowStorybook,
  parameters: {
    layout: 'centered',
  },
};
export default meta;
type Story = StoryObj<typeof AvailableJobsPingWindowStorybook>;

// 🎁 Wrapper
const PingWindowWrapper = (args: any) => {
  const isOpen = useDelayedOpen(1500, args.isOpen);

  useEffect(() => {
    useAvailableJobsStore.setState({
      availableJobs: args.availableJobs,
      jobsAwarded: args.showAwardedJobs ? mockAwardedJobs : [],
      jobsLost: args.showLostJobs ? mockLostJobs : [],
      filteredJobs: args.availableJobs,
      isOpen: isOpen,
    });

    // Mock the useAppStore with allInfo including skills using setDynamicState
    const appStore = useAppStore.getState();
    appStore.setDynamicState('allInfo', {
      skills: mockSkills,
    });
  }, [args.availableJobs, args.showAwardedJobs, args.showLostJobs, isOpen]);

  return (
    <AvailableJobsPingWindowStorybook
      {...args}
      isOpen={isOpen}
      staffMember={{
        id: 1,
        full_name: 'Mock User',
        sp: { id: 10, skills: mockSkills },
        edit_states: [],
        username: 'mockuser',
        contact_number: '0123456789',
        email_address: '<EMAIL>',
        authorised: true,
        mid: null,
        max_auth: null,
        staff_type: 1,
        location: 1,
        branches: [1],
        roles: [1],
      } as any}
      baseUrl="https://mock-api.example.com"
      token="mock-token"
    />
  );
};

// 📚 Stories
export const Default: Story = {
  args: {
    isOpen: false,
    availableJobs: mockAvailableJobs,
    showAwardedJobs: true,
    showLostJobs: true,
    shouldShowAwardedList: true,
  },
  render: (args) => <PingWindowWrapper {...args} />,
};
export const OpenWithAvailableJobs: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    availableJobs: mockAvailableJobs,
    shouldShowAwardedList: false,
    showAwardedJobs: false,
    showLostJobs: false
  }
};

export const WithAvailableJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: false,
    showLostJobs: false,
    shouldShowAwardedList: false,
  },
};

export const WithAwardedJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: true,
    showLostJobs: false,
    shouldShowAwardedList: true,
  },
};

export const WithLostJobsOnly: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    showAwardedJobs: false,
    showLostJobs: true,
    shouldShowAwardedList: true,
  },
};

export const LoadingState: Story = {
  ...Default,
  args: {
    ...Default.args,
    isOpen: true,
    availableJobs: mockAvailableJobs,
    shouldShowAwardedList: false,
    showAwardedJobs: false,
    showLostJobs: false,
    forceLoading: true, // This will show the loading spinner
  },
  parameters: {
    docs: {
      description: {
        story: 'Shows the loading spinner when the useCheckJobAvailabilityAndGetJobs hook is in loading state.'
      }
    }
  }
};
