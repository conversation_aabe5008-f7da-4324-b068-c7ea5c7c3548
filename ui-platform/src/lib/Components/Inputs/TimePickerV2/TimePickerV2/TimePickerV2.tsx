import React, { useCallback, useEffect, useState } from 'react';
import styled, { css } from 'styled-components';
import { ScrollableContent } from '../../../Scrollbar/Scrollbar';
import { maskTime } from './maskTime';

// export interface TimePickerProps {
//   mode?: any;
//   selected?: Date | DateRange | Date[];
//   numberOfMonths?: number;
//   onSelect?: (date?: Date | DateRange | Date[]) => void;
//   disablePast?: boolean;
//   disableFuture?: boolean;
//   disabledDates?: string[];
//   weekendSelectable?: boolean;
// }

const TimeSelectContainer = styled.div`
  cursor: pointer;
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
  gap: ${(props) => props.theme.SpacingXs};
  padding: ${(props) => props.theme.SpacingSm};
`;

const MinutesContainer = styled(ScrollableContent)`
  max-height: 130px;
  overflow-y: auto;
  grid-column: 2;
  grid-row: 1;
  height: 130px;
  display: grid;
  justify-content: center;
`;

const HoursContainer = styled(ScrollableContent)`
  max-height: 130px;
  overflow-y: auto;
  grid-column: 1;
  grid-row: 1;
  height: 130px;
  display: grid;
  justify-content: center;
`;

const TimeOption = styled.b<{ isSelected?: boolean }>`
  display: grid;
  justify-content: center;
  padding: 5px;
  // gap: 1rem;
  cursor: pointer;
  ${({ isSelected }) =>
    isSelected
      ? css`
          color: ${(props) => props?.theme.ColorsUtilityColorFocus};
        `
      : css`
          &:hover {
            color: ${(props) => props?.theme.ColorsUtilityColorFocus};
          }
        `}
`;

/**
 * TimePickerV2 is a component that displays a list of hours and minutes that the user can select from.
 * The component will call the onSelect function with the selected time as a string in the format "HH:mm"
 * whenever the user selects a new time. The selected time can be set using the selected prop.
 * The component will render a list of hours from 0 to 23 and a list of minutes in 5-minute intervals.
 * The user can select any of the hours and any of the valid minutes. The valid minutes can be set using the validMinutes prop.
 * The component will render a scrollbar if the list of minutes is longer than the available space.
 */
export const TimePickerV2: React.FC<{
  onSelect?: (time: string) => void;
  selected?: string;
  validMinutes?: number[];
}> = ({ onSelect, selected, validMinutes = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55] }) => {
  const [selectedHour, setSelectedHour] = useState<number | null>(null);
  const [selectedMinute, setSelectedMinute] = useState<number | null>(null);

  useEffect(() => {
    if (selected) {
      setSelectedHour(maskTime(selected).hours);
      setSelectedMinute(maskTime(selected).minutes);
    }
  }, [selected]);

  useEffect(() => {
    if (
      selectedHour !== null &&
      selectedHour !== undefined &&
      selectedMinute !== null &&
      selectedMinute !== undefined &&
      onSelect
    ) {
      const time =
        `${selectedHour.toString().padStart(2, '0')}` +
        ':' +
        `${selectedMinute.toString().padStart(2, '0')}`;
      onSelect(time);
    }
  }, [selectedHour, selectedMinute]);

  const handleHourClick = useCallback(
    (hour: number) => {
      setSelectedHour(hour);
    },
    [setSelectedHour]
  );

  const handleMinuteClick = useCallback(
    (minute: number) => {
      setSelectedMinute(minute);
    },
    [setSelectedMinute]
  );

  return (
    <TimeSelectContainer>
      <HoursContainer>
        {[...Array(24)].map((_, index) => (
          <TimeOption
            key={index}
            onClick={() => handleHourClick(index)}
            isSelected={selectedHour === index}
          >
            {index.toString().padStart(2, '0')}
          </TimeOption>
        ))}
      </HoursContainer>

      <MinutesContainer>
        {validMinutes.map((minute) => (
          <TimeOption
            key={minute}
            onClick={() => handleMinuteClick(minute)}
            isSelected={selectedMinute === minute}
          >
            {minute.toString().padStart(2, '0')}
          </TimeOption>
        ))}
      </MinutesContainer>
    </TimeSelectContainer>
  );
};
