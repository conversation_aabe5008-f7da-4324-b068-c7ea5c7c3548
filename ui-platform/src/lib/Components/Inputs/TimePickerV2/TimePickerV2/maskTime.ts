type MinuteInterval = number | number[];

export function maskTime(
  input: string,
  validMinutes?: MinuteInterval
): { time: string; hours: number; minutes: number } {
  let validMinutesArray: number[];

  // Validate and normalize validMinutes input
  if (validMinutes !== undefined) {
    if (typeof validMinutes === 'number') {
      validMinutesArray = Array.from(
        { length: Math.floor(60 / validMinutes) },
        (_, i) => i * validMinutes
      );
    } else if (Array.isArray(validMinutes)) {
      validMinutesArray = validMinutes
        .filter((num) => num >= 0 && num < 60)
        .sort((a, b) => a - b);
    } else {
      throw new Error(
        'Invalid validMinutes type. Must be a number or array of numbers.'
      );
    }
  } else {
    // Default valid minutes matching component default
    validMinutesArray = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55];
  }

  const hoursRegex = /^\d{2}$/;
  const timeRegex = /^(\d{2}):(\d{2})$/;

  if (timeRegex.test(input)) {
    // Input already has a colon
    const [, hours, minutes] = input.match(timeRegex)!;

    const hoursInt = parseInt(hours);
    const minutesInt = parseInt(minutes);
  
    if (!hoursRegex.test(hours) || hoursInt > 23) {
      throw new Error('Invalid hours. Hours must be between 00 and 23.');
    }
  
    // Allow any valid minute if no specific validation is provided
    if (validMinutes !== undefined && !validMinutesArray.includes(minutesInt)) {
      throw new Error(
        `Invalid minutes. Minutes must be one of: ${validMinutesArray.join(', ')}`
      );
    }
  }

  if (
    typeof input !== 'string' ||
    (!input.includes(':') && input.length !== 4) ||
    (input.includes(':') && input.length !== 5)
  ) {
    throw new Error('Input must be a string of exactly 4 characters');
  }

  const hours = input.slice(0, 2);
  const minutes = input.slice(-2);

  const hoursInt = parseInt(hours);
  const minutesInt = parseInt(minutes);
  
  if (!hoursRegex.test(hours) || hoursInt > 23) {
    throw new Error('Invalid hours. Hours must be between 00 and 23.');
  }
  
  if (minutesInt < 0 || minutesInt > 59) {
    throw new Error('Invalid minutes. Minutes must be between 00 and 59.');
  }
  
  // Only validate against specific minutes if validMinutes is provided
  if (validMinutes !== undefined && !validMinutesArray.includes(minutesInt)) {
    throw new Error(
      `Invalid minutes. Minutes must be one of: ${validMinutesArray.join(', ')}`
    );
  }

  return {
    time: `${hours}:${minutes}`,
    hours: parseInt(hours),
    minutes: parseInt(minutes),
  };
}
