import { useMemo, useState } from 'react';
import { Date<PERSON><PERSON><PERSON>, DayPicker, Matcher } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import styled from 'styled-components';

export interface IDatePickerProps {
  mode?: any;
  selected?: Date | DateRange | Date[];
  numberOfMonths?: number;
  onSelect?: (date?: Date | DateRange | Date[]) => void;
  disablePast?: boolean;
  disableFuture?: boolean;
  disabledDates?: string[];
  weekendSelectable?: boolean;
  cellSize?: 'default' | 'compact';
}

const StyledDayPicker = styled(DayPicker)<{
  selectedColor?: string;
  cellSize?: 'default' | 'compact';
}>`
  background-color: ${(props) =>
    props?.theme.ColorsButtonColorModuleActionsPrimary};
  border-radius: ${(props) => props?.theme.RadiusXs};
  padding: ${(props) => props?.theme.SpacingSm};
  width: max-content;
  margin: 0 !important;

  --variable-rdp-cell-size: ${(props) =>
    props?.cellSize === 'compact' ? '30px' : '40px'};

  .rdp-month {
  }

  .rdp {
    margin: ${(props) =>
      props?.cellSize === 'compact' ? '0' : '0.5rem'} !important;
  }

  .rdp-button_reset {
    color: ${(props) => props?.theme.ColorsIconColorTertiary};
  }

  .rdp-day_selected {
    background-color: ${(props) =>
      props?.theme.ColorsUtilityColorFocus} !important;
    color: #fff;
  }

  .rdp-cell {
    width: var(--variable-rdp-cell-size) !important;
    height: var(--variable-rdp-cell-size) !important;
  }

  .rdp-day {
    width: var(--variable-rdp-cell-size) !important;
    height: var(--variable-rdp-cell-size) !important;
  }
`;

/**
 * Renders a date picker component with customizable options.
 *
 * @param {IDatePickerProps} props - The properties for the date picker.
 * @param {'single' | 'multiple' | 'range'} props.mode - The mode of the date picker ('single' or 'range').
 * @param {Date | Date[] | DateRange | undefined} props.selected - The selected date or date range.
 * @param {(date: Date | Date[] | DateRange | undefined) => void} props.onSelect - The callback function when a date is selected.
 * @param {number} [props.numberOfMonths=1] - The number of months to display in the date picker.
 * @param {boolean} [props.disablePast=true] - Whether to disable past dates.
 * @param {boolean} [props.disableFuture=false] - Whether to disable future dates.
 * @param {string[]} [props.disabledDates=[]] - An array of disabled dates in string format.
 * @param {boolean} [props.weekendSelectable=false] - Whether weekends are selectable.
 * @return {JSX.Element} The rendered date picker component.
 */
export const DatePicker = ({
  mode = 'single',
  selected,
  onSelect,
  numberOfMonths = 1,
  disablePast = true,
  disableFuture = false,
  disabledDates = [],
  weekendSelectable = false,
  cellSize = 'default',
}: IDatePickerProps) => {
  const [selectedDate, setSelectedDate] = useState<
    Date | Date[] | DateRange | undefined
  >(selected);

  const weekendDisabled = useMemo(() => {
    if (!weekendSelectable) {
      return { dayOfWeek: [0, 6] };
    }
  }, [weekendSelectable]);

  const pastDisabled = useMemo(() => {
    if (disablePast) {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Normalize to start of day
      return { before: today };
    }
  }, [disablePast]);

  const futureDisabled = useMemo(() => {
    if (disableFuture) {
      const today = new Date();
      today.setHours(23, 59, 59, 999); // Normalize to end of day
      return { after: today };
    }
  }, [disableFuture]);

  const disabledDays = useMemo(() => {
    return disabledDates.map((date) => new Date(date));
  }, [disabledDates]);

  const disabledMatchers = useMemo(() => {
    const matchers = [
      weekendDisabled,
      pastDisabled,
      futureDisabled,
      ...(disabledDays || []),
    ].filter((item) => !!item);
    return matchers;
  }, [weekendDisabled, pastDisabled, futureDisabled, disabledDays]);

  const handleSelect = (date: any) => {
    setSelectedDate(date);
    if (onSelect) {
      onSelect(date);
    }
  };

  return (
    <StyledDayPicker
      mode={mode}
      selected={selectedDate}
      disabled={disabledMatchers as Matcher[]}
      onSelect={handleSelect}
      numberOfMonths={numberOfMonths}
      cellSize={cellSize}
    />
  );
};
