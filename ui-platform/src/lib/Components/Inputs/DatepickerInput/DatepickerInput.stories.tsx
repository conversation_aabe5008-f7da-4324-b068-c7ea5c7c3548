import { <PERSON>a, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import { action } from '@storybook/addon-actions';
import { DatepickerInput } from './DatepickerInput';

const meta: Meta<typeof DatepickerInput> = {
  title: 'Components/Inputs/DatePicker/DatepickerInput',
  component: DatepickerInput,
  parameters: {
    docs: {
      description: {
        component: 'A comprehensive date picker input component that supports single date, multiple dates, and date range selection with timezone-safe handling.',
      },
    },
  },
  argTypes: {
    mode: {
      control: { type: 'select' },
      options: ['single', 'multiple', 'range'],
      description: 'Date selection mode',
    },
    iconPosition: {
      control: { type: 'select' },
      options: ['left', 'right', 'none'],
      description: 'Position of the icon',
    },
    state: {
      control: { type: 'select' },
      options: ['default', 'display-only'],
      description: 'Input state',
    },
    disablePast: {
      control: { type: 'boolean' },
      description: 'Disable past dates',
    },
    disableFuture: {
      control: { type: 'boolean' },
      description: 'Disable future dates',
    },
    weekendSelectable: {
      control: { type: 'boolean' },
      description: 'Allow weekend selection',
    },
  },
  decorators: [
    (Story, context) => {
      const { control } = useForm({
        defaultValues: {
          [context.args.name || 'date']: context.args.selected,
        },
      });
      
      return (
        <div style={{ padding: '20px', maxWidth: '400px' }}>
          <Story args={{ ...context.args, control }} />
        </div>
      );
    },
  ],
};

export default meta;
type Story = StoryObj<typeof DatepickerInput>;

export const Default: Story = {
  args: {
    name: 'date',
    placeholder: 'Select a date',
    mode: 'single',
    label: 'Date',
    instructions: 'Choose your preferred date',
    onDateChange: action('date-changed'),
  },
};

export const WithPreselectedDate: Story = {
  args: {
    name: 'preselected-date',
    placeholder: 'Select a date',
    mode: 'single',
    label: 'Birth Date',
    selected: new Date('2024-01-15'),
    instructions: 'Your birth date is pre-selected',
    onDateChange: action('date-changed'),
  },
};

export const SingleDateWithIcon: Story = {
  args: {
    name: 'single-date',
    mode: 'single',
    placeholder: 'Select date',
    label: 'Event Date',
    icon: 'alarm-clock',
    iconPosition: 'left',
    instructions: 'Select the event date',
    onDateChange: action('date-changed'),
  },
};

export const MultipleDates: Story = {
  args: {
    name: 'multiple-dates',
    mode: 'multiple',
    placeholder: 'Select multiple dates',
    label: 'Available Dates',
    icon: 'alarm-clock',
    iconPosition: 'right',
    instructions: 'Select all available dates',
    onDateChange: action('dates-changed'),
  },
};

export const DateRange: Story = {
  args: {
    name: 'date-range',
    mode: 'range',
    placeholder: 'Select date range',
    label: 'Vacation Period',
    icon: 'alarm-clock',
    iconPosition: 'left',
    instructions: 'Select your vacation start and end dates',
    numberOfMonths: 2,
    onDateChange: action('range-changed'),
  },
};

export const WithValidationError: Story = {
  args: {
    name: 'error-date',
    mode: 'single',
    placeholder: 'Select a date',
    label: 'Required Date',
    fieldError: {
      type: 'required',
      message: 'This field is required',
    },
    instructions: 'This field shows an error state',
    onDateChange: action('date-changed'),
  },
};

export const DisablePastDates: Story = {
  args: {
    name: 'future-date',
    mode: 'single',
    placeholder: 'Select future date',
    label: 'Appointment Date',
    disablePast: true,
    instructions: 'Only future dates are selectable',
    onDateChange: action('date-changed'),
  },
};

export const DisableFutureDates: Story = {
  args: {
    name: 'past-date',
    mode: 'single',
    placeholder: 'Select past date',
    label: 'Historical Date',
    disableFuture: true,
    instructions: 'Only past dates are selectable',
    onDateChange: action('date-changed'),
  },
};

export const WeekendDisabled: Story = {
  args: {
    name: 'weekday-date',
    mode: 'single',
    placeholder: 'Select weekday',
    label: 'Business Date',
    weekendSelectable: false,
    instructions: 'Weekends are disabled',
    onDateChange: action('date-changed'),
  },
};

export const DisplayOnlyState: Story = {
  args: {
    name: 'display-date',
    mode: 'single',
    placeholder: 'Display only',
    label: 'Read-only Date',
    state: 'display-only',
    selected: new Date('2024-01-15'),
    instructions: 'This date is in display-only mode',
    onDateChange: action('date-changed'),
  },
};

export const WithSecondaryField: Story = {
  args: {
    name: 'primary-date',
    mode: 'single',
    placeholder: 'Select a date',
    label: 'Date with Secondary Field',
    secondaryName: 'date-only',
    setSecondaryValue: action('secondary-value-set'),
    instructions: 'This will also update a secondary field with date-only format',
    onDateChange: action('date-changed'),
  },
};

export const MultipleMonths: Story = {
  args: {
    name: 'multi-month-date',
    mode: 'single',
    placeholder: 'Select from multiple months',
    label: 'Multi-Month View',
    numberOfMonths: 3,
    instructions: 'Calendar shows 3 months at once',
    onDateChange: action('date-changed'),
  },
};

export const CustomDisabledDates: Story = {
  args: {
    name: 'custom-disabled',
    mode: 'single',
    placeholder: 'Select available date',
    label: 'Custom Disabled Dates',
    disabledDates: ['2024-01-01', '2024-01-15', '2024-01-25'],
    instructions: 'Specific dates are disabled',
    onDateChange: action('date-changed'),
  },
};

export const TimezoneTestCase: Story = {
  args: {
    name: 'timezone-test',
    mode: 'single',
    placeholder: 'Test timezone handling',
    label: 'Timezone Test',
    secondaryName: 'timezone-safe-date',
    setSecondaryValue: (name: string, value: string) => {
      action('secondary-value-set')(name, value);
      console.log(`Secondary field ${name} set to: ${value}`);
    },
    instructions: 'Test that dates are not shifted by timezone conversion',
    onDateChange: (date) => {
      action('date-changed')(date);
      console.log('Selected date:', date);
    },
  },
};
