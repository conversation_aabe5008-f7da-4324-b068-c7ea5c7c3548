import React from 'react';
import styled from 'styled-components';
import { Icon } from '../../Icons';

export interface ISearchInputProps {
  placeholder: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit?: (e: React.MouseEvent<SVGElement, MouseEvent> | React.KeyboardEvent<HTMLInputElement>) => void;
  className?: string;
  value: any;
  isLoading?: boolean;
  onClear?: () => void;
}

const Container = styled(({ ...rest }) => <div {...rest}></div>)`
  display: grid;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  // width: 100%; //removed these values for messageView use
  // height: 100%;
  padding: ${(props) => props?.theme.SpacingSm};
  gap: ${(props) => props?.theme.SpacingXs};
  border-radius: ${(props) => props?.theme.RadiusXs};
  border: 1px solid
    ${(props) =>
      props.isError
        ? props?.theme?.ColorsUtilityColorError
        : props?.theme.ColorsStrokesFocus};
  background: ${(props) =>
    props.isError
      ? props?.theme?.ColorsInputsError
      : props?.theme.ColorsInputsPrimary};
`;

const Input = styled(({ ...rest }) => <input {...rest} />)`
  all: unset;
  width: 100%;
  color: ${(props) => props?.theme.ColorsTypographyPrimary};
  font-size: ${(props) => props.theme.FontSize4}px;
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-weight: ${(props) => props.theme.FontWeightsInter3};
  word-wrap: break-word;
  border: none;
  border-radius: 4px;
  background: ${(props) => props.theme.ColorsInputsPrimary};

  &::placeholder {
    color: ${(props) => props?.theme.ColorsTypographyPrimary};
  }
`;

/**
 * A SearchInput component that renders a search input box with a
 * magnifying glass icon. The component renders a Container component
 * with an Input component and an Icon component inside. The Container
 * component is customizable with the className prop.
 *
 * @param {string} [placeholder] - The placeholder text for the input
 * @param {function} [onChange] - The callback function to be executed
 * when the input value changes
 * @param {function} [onSubmit] - The callback function to be executed
 * when the search icon is clicked or Enter key is pressed
 * @param {string} [className] - The class name to be applied to the
 * Container component
 * @param {boolean} [isLoading] - Whether the search is currently in progress
 * @returns {ReactElement} - The SearchInput component
 */
export const SearchInput = ({
  placeholder,
  onChange,
  onSubmit,
  className,
  value,
  isLoading = false,
  onClear,
}: ISearchInputProps) => {
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    // Stop propagation to prevent panel closing
    event.stopPropagation();
    
    if (event.key === 'Enter' && onSubmit) {
      event.preventDefault();
      onSubmit(event);
    }
  };

  const handleClear = () => {
    if (onClear) {
      onClear();
    } else if (onChange) {
      // Create a synthetic event to clear the input
      const syntheticEvent = {
        target: { value: '' },
      } as React.ChangeEvent<HTMLInputElement>;
      onChange(syntheticEvent);
    }
  };

  const showClearIcon = value && value.toString().length > 0;

  // Handle click events on the container to prevent propagation
  const handleContainerClick = (event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
  };

  return (
    <Container className={className} onClick={handleContainerClick}>
      <Input 
        placeholder={placeholder} 
        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
          e.stopPropagation();
          if (onChange) onChange(e);
        }} 
        value={value} 
        onKeyDown={handleKeyDown}
      />
      {showClearIcon && (
        <Icon 
          type="close" 
          color="white" 
          onClick={(e) => {
            e.stopPropagation();
            handleClear();
          }} 
          style={{ 
            cursor: 'pointer',
            marginRight: '4px'
          }} 
        />
      )}
      <Icon 
        type={isLoading ? "loading" : "search-sm"} 
        color="white" 
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!isLoading && onSubmit) onSubmit(e);
        }} 
        style={{ 
          cursor: (!isLoading && onSubmit) ? 'pointer' : 'default',
          animation: isLoading ? 'spin 1s linear infinite' : 'none'
        }} 
      />
    </Container>
  );
};
