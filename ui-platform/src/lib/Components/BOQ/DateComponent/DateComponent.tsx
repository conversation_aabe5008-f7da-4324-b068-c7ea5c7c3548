import React, { useState } from 'react';
import {
  DateR<PERSON><PERSON>,
  Day<PERSON>icker,
  SelectRangeEvent<PERSON>andler,
  SelectSingleEventHandler,
} from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import styled from 'styled-components';
import { svgs } from '../../Icons/svgs';

interface IDatePickerProps {
  mode: 'single' | 'range';
  selected?: Date | DateRange;
  onSelect?: (date: Date | DateRange) => void;
}

const StyledDayPicker = styled(DayPicker)`
  .rdp {
    margin: 0 !important;
  }
  .rdp-month {
    background-color: ${({ theme }) =>
      theme?.ColorsButtonColorModuleActionsPrimary};
    border-radius: ${({ theme }) => theme?.RadiusXs};
    padding: ${({ theme }) => theme?.SpacingLg};
    position: absolute;
    z-index: 10;
  }

  .rdp-button_reset {
    color: ${({ theme }) => theme?.ColorsIconColorTertiary};
  }

  .rdp-day_selected {
    background-color: ${({ theme }) =>
      theme?.ColorsUtilityColorFocus} !important;
    color: ${(props) => props?.theme?.ColorsStrokesDefault} !important;
  }
`;

const DatePicker = ({ mode, selected, onSelect }: IDatePickerProps) => {
  const [selectedDate, setSelectedDate] = useState<
    Date | DateRange | undefined
  >(selected);

  const handleSelectSingle: SelectSingleEventHandler = (date) => {
    setSelectedDate(date);
    if (onSelect) {
      onSelect(date as Date);
      //   setDropdownOpen((prev) => !prev);
    }
  };

  const handleSelectRange: SelectRangeEventHandler = (range) => {
    setSelectedDate(range);
    if (onSelect) {
      onSelect(range as DateRange);
    }
  };

  return (
    // <>
    <StyledDayPicker
      mode={mode as any}
      selected={selectedDate}
      onSelect={onSelect as SelectRangeEventHandler} // build type error fix
      // onSelect={mode === 'single' ? handleSelectSingle as any : handleSelectRange as any}
    />
    // </>
  );
};

const DropdownIcon = styled.svg<{ isSelected: boolean }>`
  width: 24px;
  height: 24px;
  position: absolute;
  right: 8px;
  z-index: 9;
  cursor: pointer;
`;

const Container = styled.div`
  width: 183px;
  height: 61px;
  display: grid;
  grid-template-rows: 1fr;
  gap: 4px;
`;

const TextHere = styled.div`
  font-size: ${(props) => props.theme.FontSize3}px;
  font-weight: ${(props) => props.theme.FontWeightsInter1};
  align-self: stretch;
`;

const InputBox = styled.div`
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
  width: 138px;
  height: 22px;
  padding: 8px;
  cursor: pointer;
`;

const InputHereWrapper = styled.div`
  display: grid;
  align-items: center;
`;

const InputHere = styled.div`
  position: relative;
`;

const DatePickerDropdown = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
`;

/**
 * DateComponent to display the date picker dropdown and selected date.
 * Toggles the dropdown for the date picker.
 * Handles date selection and closes the dropdown.
 *
 * DatePicker component to display a day picker.
 * @param {IDatePickerProps} props - The props for the DatePicker component.
 */
const DateComponent = ({
  selected,
  onSelect,
}: {
  selected?: Date;
  onSelect?: ((date: Date | DateRange) => void) | ((date: Date) => void);
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    selected ? selected : new Date()
  );
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleDateSelect = (date: Date | DateRange) => {
    console.log('Selected Date:', date);
    setSelectedDate(date as Date);
    setIsOpen(false);
    onSelect && onSelect(date as Date); // build type error fix
  };

  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
  };

  const iconType = isOpen ? 'chevron-up' : 'chevron-down';
  const icon = svgs[iconType];

  return (
    <Container>
      <TextHere>Date</TextHere>
      <InputBox onClick={toggleDropdown}>
        <InputHereWrapper>
          <InputHere>
            {selectedDate?.toLocaleDateString() || 'Select Date'}
            <DropdownIcon
              isSelected={isOpen}
              viewBox={icon.viewBox}
              onClick={handleInputClick}
            >
              <path
                d={icon.paths[0].d}
                stroke={isOpen ? '#c4c4c4' : icon.paths[0].stroke}
                strokeLinecap={icon.paths[0].strokeLinecap}
                strokeLinejoin={icon.paths[0].strokeLinejoin}
                fill={icon.paths[0].fill}
              />
            </DropdownIcon>
          </InputHere>
        </InputHereWrapper>
      </InputBox>
      {isOpen && (
        <DatePickerDropdown>
          <DatePicker
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
          />
        </DatePickerDropdown>
      )}
    </Container>
  );
};

export default DateComponent;
