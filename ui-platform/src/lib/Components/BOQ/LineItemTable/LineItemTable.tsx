import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { svgs } from '../../Icons';
import LineItemModify from '../LineItemModify/LineItemModify';
import { additionalFontStyling } from '../../../Utilities';

import defaultBoqIcon from '../../ClientLogos/boq-line-item.svg';
import boqBettersure from '../../ClientLogos/boq-bettersure-logo.svg';

export interface LineItem {
  id: number;
  description: string;
  quantity?: number;
  optionalItem?: boolean;
  unitPrice?: number;
  useFontTransformer?: boolean;
}

export interface LineItemsTableProps {
  iconUrl?: string;
  compulsoryItems?: LineItem[];
  optionalItems: LineItem[];
  columnNames: {
    description: string;
    quantity: string;
    unitPrice: string;
    total: string;
  };
  onItemsChange: (items: LineItem[]) => void;
  items?: LineItem[];
  createCustomItemButton?: boolean;
}

// Map of keyword → imported SVG
const logoMap: Record<string, string> = {
  bettersure: boqBettersure,
  // anotherkeyword: anotherLogoImport
};

const TableContainer = styled.div`
  display: grid;
  grid-template-columns: minmax(300px, 654px) repeat(3, minmax(57px, 150px));
  max-width: 880px;
  min-width: 481px;
  width: 100%;
  margin: 20px auto;
  border-radius: 4px;
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  box-sizing: border-box;
  padding: 0px 8px 0px;
  text-align: center;
  font-size: ${(props) => props.theme.FontSize3}px;
  color: ${(props) => props.theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};

  @media (max-width: 768px) {
    grid-template-columns: 1fr repeat(3, minmax(50px, 100px));
  }
`;

const TableHeader = styled.div<{ useFontTransformer?: boolean }>`
  display: contents;

  & > div:first-child {
    text-align: left;
    padding-left: 15px;
  }

  & > div {
    color: ${(props) => props.theme.ColorsTypographyPrimary};
    background-color: ${(props) => props.theme.ColorsInputsPrimary};
    padding: 8px;
    height: 35px;
    display: grid;
    align-items: center;
    text-align: center;
    border-right: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
    ${(props) =>
      additionalFontStyling(
        props.theme.FontWeightsInter6,
        props.useFontTransformer
      )}

    &:last-child {
      border-right: none;
    }
  }
`;

const TableRow = styled.div<{ compulsoryItem?: boolean; addItems?: boolean }>`
  display: contents;
  position: relative;

  background-color: ${(props) =>
    props.compulsoryItem ? '#383c3e' : props.theme.ColorsInputsPrimary};

  & > div {
    padding: 4px 8px;
    height: 32px;
    background-color: ${(props) =>
      props.compulsoryItem ? '#383c3e' : props.theme.ColorsBackgroundModule};
    color: ${(props) =>
      props.compulsoryItem ? '#979a9d' : props.theme.ColorsTypographyPrimary};
    border-bottom: 0.75rem solid ${(props) => props.theme.ColorsInputsPrimary};
    border-right: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
    display: grid;
    align-items: center;
    text-align: center;
    gap: 8px;
    // position: relative;

    /* Pseudo-element to create cross effect */
    // &::before {
    //   content: "";
    //   position: absolute;
    //   bottom: -12px;
    //   right: -1px;
    //   // width: 10px;
    //   // height: 10px;
    //   border-left: 1px solid #8b8b8b;
    //   border-top: 0.75rem solid #4a5055;
    // }

    &:nth-last-child(1) {
      border-right: none;
      // border-left: 1px solid ${(props) => props.theme.ColorsStrokesGrey};
      position: relative; //new
    }

    ${({ compulsoryItem }) =>
      !compulsoryItem &&
      `
      &:last-child {
        // border-right: none;
        // border-bottom: none;
        // height: 25px;
        // background-color: transparent;
        color: none;
      position: relative;  //new

      }
      &:nth-last-child(2) {
        // border-right: none;
        border-right: 1px solid #8b8b8b ;

      }`}

    ${({ addItems }) =>
      addItems &&
      `
      &:nth-last-child(2) {
        border-right: 1px solid #8b8b8b ;
      }
      `}

     &:first-child {
      grid-template-columns: 1fr auto;
      text-align: left;
    }
  }
`;

const EditableInput = styled.input`
  background-color: ${(props) => props.theme.ColorsBackgroundModule};
  cursor: text;
  outline: none;
  color: #a5a5a5;
  border: none;
  width: 100%;
  text-align: center;

  -moz-appearance: textfield;
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
`;

const Info = styled.div<{ compulsoryItem?: boolean }>`
  height: 100%;
  width: 100%;
  // position: relative;
  border-radius: 4px;
  background-color: ${(props) =>
    props.compulsoryItem ? '#383c3e' : props.theme.ColorsInputsPrimary};
  display: grid;
  align-items: center;
  padding: 0px 8px;
  box-sizing: border-box;
  text-align: left;
  color: ${(props) =>
    props.compulsoryItem ? '#979a9d' : props.theme.ColorsTypographyPrimary};
`;

const Icon = styled.svg`
  width: 24px;
  height: 24px;
  cursor: pointer;
  min-width: 24px;
`;

const RemoveIcon = styled.div`
  cursor: pointer;
  color: #b24111 !important;
  position: absolute;
  top: 21px !important;
  right: -43px !important;
  width: 28px;
  height: 28px !important;
  display: flex;
  justify-content: center;
  align-items: center;

  transform: translateY(-50%);
  font-size: ${(props) =>
    props.theme.FontSize4}px; /*should be replaced with an icon */
  background-color: transparent !important;
  align-self: stretch;
  border-radius: 104px;
  border: 1px solid #b24111 !important;
  align-items: center;
  justify-content: center;
`;

const DescriptionImage = styled.img`
  width: 24px;
  height: 24px;
  object-fit: cover;
  min-width: 24px;
`;

/**
 * Format a number as currency, using the South African Rand (R) symbol,
 * and inserting commas as thousand separators.
 *
 * @param {number} value - the number to format
 * @returns {string} the formatted currency string
 */

const formatCurrency = (value: number): string => {
  return `R${value.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
};

/**
 * A table component for displaying line items in a bill of quantities.
 *
 * @remarks
 * This component is used to display line items in a bill of quantities.
 * It also provides the ability to add or remove line items from the table.
 *
 * @param {LineItemsTableProps} props - The props for the component.
 * @property {LineItem[]} compulsoryItems - The list of compulsory line items to display in the table.
 * @property {LineItem[]} optionalItems - The list of optional line items to display in the table.
 * @property {object} columnNames - An object containing the column names for the table.
 * @property {string} iconUrl - The URL of the icon to display in the description column.
 * @property {(items: LineItem[]) => void} onItemsChange - A function to call when the items in the table change.
 * @property {LineItem[]} items - The list of items to display in the table.
 * @returns {React.FC<LineItemsTableProps>} - The rendered component.
 */
const LineItemsTable: React.FC<LineItemsTableProps> = ({
  compulsoryItems,
  optionalItems,
  columnNames,
  iconUrl,
  onItemsChange,
  createCustomItemButton,
  items = [],
}) => {
  const [optionalLineItems, setOptionalLineItems] =
    useState<LineItem[]>(optionalItems);

  // Resolve icon source
  const resolvedIconUrl =
    (iconUrl && logoMap[iconUrl.toLowerCase()]) || defaultBoqIcon;

  const onSelect = (item: LineItem | LineItem[]) => {
    if (Array.isArray(item)) {
      const filteredItems = optionalLineItems.filter(
        (i) => !item.some((selectedItem) => selectedItem.id === i.id)
      );
      setOptionalLineItems(filteredItems);

      const updatedItems = [...items, ...item];
      onItemsChange(updatedItems);
    } else {
      const filterOutItem = optionalLineItems.filter((i) => i.id !== item.id);
      setOptionalLineItems(filterOutItem);

      const updatedItems = [...items, item];
      onItemsChange(updatedItems);
    }
  };

  const onRemove = (item: LineItem) => {
    const filterOutItem = [...items].filter((i) => i.id !== item.id);
    onItemsChange(filterOutItem);
    setOptionalLineItems((prev) => [...prev, item]);
  };

  const handleQuantityChange = (item: LineItem, newQuantity: number) => {
    if (newQuantity >= 0) {
      const quantityChange = items.map((i) =>
        i.id === item.id ? { ...i, quantity: newQuantity } : i
      );
      onItemsChange(quantityChange);
    }
  };

  return (
    <TableContainer>
      <TableHeader>
        <div>{columnNames.description}</div>
        <div>{columnNames.quantity}</div>
        <div>{columnNames.unitPrice}</div>
        <div>{columnNames.total}</div>
      </TableHeader>
      {items.map((item, index) => (
        <TableRow key={index} compulsoryItem={!item.optionalItem}>
          <div>
            <Info compulsoryItem={!item.optionalItem}>{item.description}</Info>
            {resolvedIconUrl ? (
              <DescriptionImage src={resolvedIconUrl} alt="icon" />
            ) : (
              <Icon viewBox={svgs['menu-01'].viewBox}>
                <path {...svgs['menu-01'].paths[0]} />
              </Icon>
            )}
          </div>
          <div>
            {item.optionalItem ? (
              <EditableInput
                type="number"
                value={item.quantity}
                onChange={(e) =>
                  handleQuantityChange(item, parseInt(e.target.value))
                }
              />
            ) : (
              item.quantity
            )}
          </div>
          <div>{formatCurrency(item.unitPrice || 0)}</div>
          <div>
            {formatCurrency((item.quantity || 1) * (item.unitPrice || 0))}
            {item.optionalItem && (
              <RemoveIcon onClick={() => onRemove(item)}>X</RemoveIcon>
            )}
          </div>
        </TableRow>
      ))}
      {optionalLineItems.length > 0 && (
        <TableRow compulsoryItem={optionalLineItems.length <= 0} addItems>
          <div>
            <LineItemModify
              items={optionalLineItems || []}
              onSelect={onSelect}
              createCustomItemButton={createCustomItemButton}
            />
          </div>
          <div></div>
          <div></div>
          <div></div>
        </TableRow>
      )}
    </TableContainer>
  );
};

export default LineItemsTable;
