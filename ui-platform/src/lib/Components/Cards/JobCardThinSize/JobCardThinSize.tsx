import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { MappedJob } from '../../../Engine/models/mapped-job';
import { ActionConfig } from '../../../Engine/models';
import { MenuList } from '../../Menu';
import { PermissionsIndicator } from '../PermissionsIndicator/PermissionsIndicator';

interface SPWorkflowJobCardListViewProps {
  jobCardNumberPrefix: string;
  job?: MappedJob;
  LinkRouter: any;
  getMenuItems: (job: any) => { icon: string; label: string; path: string }[];
  callClientAction: (config: ActionConfig) => void;
  notesLink?: string;
}

const OuterContainer = styled.div`
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 4fr;
  border-radius: 4px;
  margin-top: 1px;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary || '#232323'};
  height: auto;
  text-align: left;
  font-size: ${(props) => props.theme.FontSize3 || '1rem'}px;
  color: ${(props) => props.theme.ColorsTypographyPrimary || '#bdbdbd'};
  font-family: ${(props) => props.theme.FontFamiliesInter || 'Inter, sans-serif'};
  box-sizing: border-box;
  align-items: center;
  gap: 16px;
  @media (max-width: 768px) {
    grid-template-columns: 1fr 2fr;
  }
  @media (max-width: 414px) {
    grid-template-columns: 1fr 2fr;
  }
`;

const LeftSection = styled.div`
  display: grid;
  grid-template-columns: 1fr 2fr;
  align-items: center;
  gap: 10px;
  height: 100%;
  padding-left: 18px;
  padding-right: 6px;
`;

const CenterSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-width: 48px;
  width: 48px;
  height: 100%;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 1px;
    background: ${(props) => props.theme.ColorsInputsPrimaryStroke || '#444'};
    border-radius: 1px;
    z-index: 0;
  }
`;

const PermissionIndicatorWrapper = styled.div<{ hasIndicator?: boolean }>`
  position: relative;
  z-index: 1;
  background: ${(props) => props?.theme.ColorsCardColorJobCardPrimary || '#232323'};
  border-radius: 50%;
  padding: ${(props) => (props.hasIndicator ? '2px' : '0')};
  display: flex;
  align-items: center;
  justify-content: center;
`;

const RightSection = styled.div`
  display: grid;
  grid-template-columns: auto minmax(60px, auto) minmax(60px, auto) 1fr auto;
  align-items: center;
  gap: 40px;
  width: 100%;
  border-left: solid 1px ${(props) => props.theme.ColorsInputsPrimaryStroke || '#444'};
`;

const NumberCell = styled.div`
  font-size: 0.92rem;
  color: #bdbdbd;
  letter-spacing: 1px;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
`;

const CustomerCell = styled.div`
  font-weight: 600;
  font-size: 1rem;
  color: #e0e0e0;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 120px;
  vertical-align: middle;
`;

const SkillCell = styled.div`
  font-size: 0.92rem;
  color: #bdbdbd;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
`;

const StateCell = styled.div`
  font-size: 0.92rem;
  color: #bdbdbd;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
`;

const DescriptionCell = styled.div`
  font-size: 0.92rem;
  color: #bdbdbd;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 180px;
  vertical-align: middle;
`;

const ContextMenuCell = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
`;

const PermissionCell = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

const IsolatedContextMenuWrapper = styled.div`
  isolation: isolate;
  position: relative;
  z-index: 1000;
  .job-card-dropdown {
    isolation: isolate;
    position: absolute;
    right: 0;
    top: 100%;
    z-index: 1001;
    background-color: rgba(40, 48, 51, 0.84);
    border-radius: 4px;
    box-shadow: 0px 12px 23px 5px rgba(7, 14, 17, 0.25);
    backdrop-filter: blur(9px);
    border: 0.5px solid #696969;
    &:hover {
      background-color: rgba(40, 48, 51, 0.93);
    }
  }
`;

const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return screenSize;
};

export const JobCardThinSize: React.FC<SPWorkflowJobCardListViewProps> = ({
  jobCardNumberPrefix,
  job,
  LinkRouter,
  getMenuItems,
  callClientAction,
  notesLink,
}) => {
  const navigate = useNavigate();
  const screenSize = useScreenSize();
  const isMobile = screenSize.width < 414;
  const isTablet = screenSize.width <= 769 && screenSize.width >= 414;
  const isDesktop = screenSize.width > 769;

  if (!job) return null;
  const number = job.claim?.mid || job.mid || job.id || '-';
  const customer = job.customer || '-';
  const skill = job.skillName || '-';
  const state = job.stateTextDisplay || '-';
  const description = (job as any).description || '';

  const MenuListDisplay = () => {
    return (
      <MenuList
        items={getMenuItems(job).map((item: any) => ({
          id: item.label,
          label: item.label,
          icon: item.icon,
          href: item.path,
          onClick: async () => {
            if (item.path) {
              navigate(item.path);
            } else if (item.onClick) {
              for (const cf of item.onClick) {
                await callClientAction(cf);
              }
            }
          },
        }))}
        orientation="right"
        triggerIcon="dots-vertical"
      />
    );
  };

  if (isMobile) {
    return (
      <OuterContainer style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', padding: '8px 12px' }}>
          <NumberCell>{number}</NumberCell>
          <CustomerCell>{customer}</CustomerCell>
        </div>
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8, padding: '0 12px 8px 12px' }}>
          {(
            <PermissionIndicatorWrapper hasIndicator={!!job?.permissionGranted}>
              {job?.permissionGranted && (
                <PermissionsIndicator color="green" size="" position="" />
              )}
            </PermissionIndicatorWrapper>
          )}
          {skill && <SkillCell>{skill}</SkillCell>}
          {state && <StateCell>{state}</StateCell>}
        </div>
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8, padding: '0 12px 8px 12px' }}>
          <DescriptionCell style={{ flex: 1 }}>{description ? description : ""}</DescriptionCell>
          <ContextMenuCell>
            <IsolatedContextMenuWrapper>
              <MenuListDisplay />
            </IsolatedContextMenuWrapper>
          </ContextMenuCell>
        </div>
      </OuterContainer>
    );
  } else if (isTablet) {
    return (
      <OuterContainer style={{ gridTemplateColumns: '1fr 2fr' }}>
        <LeftSection>
          <NumberCell>{number}</NumberCell>
          <CustomerCell>{customer}</CustomerCell>
        </LeftSection>
        <RightSection>
          {(
            <PermissionIndicatorWrapper hasIndicator={!!job?.permissionGranted}>
              {job?.permissionGranted && (
                <PermissionsIndicator color="green" size="" position="" />
              )}
            </PermissionIndicatorWrapper>
          )}
          {skill && <SkillCell>{skill}</SkillCell>}
          {state && <StateCell>{state}</StateCell>}
          <DescriptionCell>{description ? description : ""}</DescriptionCell>
          <ContextMenuCell>
            <IsolatedContextMenuWrapper>
              <MenuListDisplay />
            </IsolatedContextMenuWrapper>
          </ContextMenuCell>
        </RightSection>
      </OuterContainer>
    );
  } else {
    // Desktop
    return (
      <OuterContainer>
        <LeftSection>
          <NumberCell>{number}</NumberCell>
          <CustomerCell>{customer}</CustomerCell>
        </LeftSection>
        <RightSection>
          {(
            <PermissionIndicatorWrapper hasIndicator={!!job?.permissionGranted}>
              {job?.permissionGranted && (
                <PermissionsIndicator color="green" size="" position="" />
              )}
            </PermissionIndicatorWrapper>
          )}
          {skill && <SkillCell>{skill}</SkillCell>}
          {state && <StateCell>{state}</StateCell>}
          <DescriptionCell>{description ? description : ""}</DescriptionCell>
          <ContextMenuCell>
            <IsolatedContextMenuWrapper>
              <MenuListDisplay />
            </IsolatedContextMenuWrapper>
          </ContextMenuCell>
        </RightSection>
      </OuterContainer>
    );
  }
};

