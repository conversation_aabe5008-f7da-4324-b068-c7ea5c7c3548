import React from 'react';
import styled from 'styled-components';

export interface LinkCardItemProps {
  label: string;
  selected?: boolean;
  onClick: () => void;
  value?: string | number;
}
const LinkCardDropdownItem = styled.div<{
  selected?: boolean;
  value?: string | number;
}>`
  width: 100%;
  height: 33px;
  padding: 8px 16px 8px 16px;
  margin: 0px;
  gap: 0px;
  opacity: 0px;
  display: grid;
  grid-template-columns: 5fr 4fr 2.8fr;
  border-radius: 4px;
  background: ${({ selected, theme }) =>
    selected
      ? theme.ColorsControllersSecondary
      : theme.ColorsControllersDefault};
  box-sizing: border-box;
  margin-bottom: 4px;
  cursor: pointer;
  border: ${({ selected, theme }) =>
    selected ? `2px solid ${theme.ColorsUtilityColorFocus}` : '2px solid transparent'};
  transition: border-color 0.15s ease, background 0.15s ease;
  &:hover {
    background: rgba(125, 125, 125, 0.6);
  }
`;

const LinkCardLabel = styled.div<{ selected?: boolean }>`
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-weight: ${(props) => props.theme.FontWeightsInter6};
  font-size: ${(props) => props.theme.FontSize3}px;
  text-align: left;
  position: relative;
  display: inline-block;
  white-space: nowrap;
  color: ${({ selected, theme }) =>
    selected ? theme.ColorsUtilityColorFocus : theme.ColorsTypographyPrimary};
`;

/**
 * A single item in a LinkCardsDropdown. Clicking on this item will cause the
 * `onClick` function to be called.
 *
 * @param {string} [label=''] The text to display in the item.
 * @param {function} onClick The function to call when the item is clicked.
 */
export const LinkCardItem = ({
  label = '',
  onClick,
  selected = false,
  value,
}: LinkCardItemProps) => {
  return (
    <LinkCardDropdownItem
      onClick={onClick}
      selected={selected}
      value={selected ? value : ''}
    >
      <LinkCardLabel selected={selected}>{label}</LinkCardLabel>
    </LinkCardDropdownItem>
  );
};
