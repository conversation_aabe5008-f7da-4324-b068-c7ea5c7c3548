// Type declaration merging for template functions
// Consuming projects can augment this module to add their own functions and get IntelliSense.

import type { TemplateFunctionMap } from '../lib/Engine/helpers/template-function-registry';

export declare namespace UIPlatformTemplateFunctions {
  /**
   * Base interface for custom template functions.
   * Extend this via declaration merging in consuming apps:
   *
   * declare module '@4-sure/ui-platform' {
   *   interface CustomTemplateFunctions {
   *     fieldOps: {
   *       calculateSPCommission: (amount: number, rate: number) => number;
   *       formatCurrency: (value: number, currency: string) => string;
   *     };
   *   }
   * }
   */
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface CustomTemplateFunctions {}
}

declare module '@4-sure/ui-platform' {
  /**
   * Merge this interface from consuming projects to get IntelliSense on their custom functions.
   * Example:
   * declare module '@4-sure/ui-platform' {
   *   interface CustomTemplateFunctions {
   *     myApp: { calculateTax: (amount: number, rate: number) => number };
   *   }
   * }
   */
  // eslint-disable-next-line @typescript-eslint/no-empty-interface
  export interface CustomTemplateFunctions {}

  export type RegisteredTemplateFunctions = TemplateFunctionMap & CustomTemplateFunctions;
}

