import { render, screen } from '@testing-library/react';
import { ThemeProvider } from 'styled-components';
import ElementaryThemedMap from './ElementaryThemedMap';

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    DirectionsService: jest.fn(),
    DirectionsStatus: { OK: 'OK' },
    TravelMode: { DRIVING: 'DRIVING' },
    Size: jest.fn(),
  },
};

// Mock the Google Maps loader
jest.mock('@react-google-maps/api', () => ({
  useJsApiLoader: () => ({ isLoaded: true }),
  GoogleMap: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="google-map">{children}</div>
  ),
  Marker: ({ label, onClick }: { label: string; onClick?: () => void }) => (
    <div data-testid={`marker-${label}`} onClick={onClick}>
      Marker {label}
    </div>
  ),
  InfoWindow: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="info-window">{children}</div>
  ),
  DirectionsRenderer: () => <div data-testid="directions-renderer" />,
  Autocomplete: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="autocomplete">{children}</div>
  ),
}));

// Mock theme object
const mockTheme = {
  ColorsTypographyPrimary: '#000000',
  ColorsOverlaySurfaceOverlay: '#ffffff',
  ColorsStrokesDefault: '#e0e0e0',
  DesktopFlowsParagraphs: {
    value: {
      fontFamily: 'Arial, sans-serif',
    },
  },
};

// Set up global window.google mock
beforeAll(() => {
  (global as any).window.google = mockGoogleMaps;
});

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={mockTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('ElementaryThemedMap', () => {
  const defaultProps = {
    joblocation: { lat: -26.2051, lng: 28.0471 },
    theme: 'light' as const,
  };

  it('should render successfully', () => {
    renderWithTheme(<ElementaryThemedMap {...defaultProps} />);
    expect(screen.getByTestId('google-map')).toBeInTheDocument();
  });

  it('should render job marker when no route is provided', () => {
    renderWithTheme(<ElementaryThemedMap {...defaultProps} />);
    expect(screen.getByTestId('marker-Job')).toBeInTheDocument();
  });

  it('should render destination marker when route is provided', () => {
    const route = {
      origin: { lat: -26.2051, lng: 28.0471 },
      destination: { lat: -26.1514869, lng: 27.924998 },
    };
    
    renderWithTheme(
      <ElementaryThemedMap {...defaultProps} route={route} />
    );
    
    expect(screen.getByTestId('marker-B')).toBeInTheDocument();
  });

  it('should render popup when showPopup is true and serviceProviderData is provided', () => {
    const route = {
      origin: { lat: -26.2051, lng: 28.0471 },
      destination: { lat: -26.1514869, lng: 27.924998 },
    };
    
    const serviceProviderData = {
      id: 109,
      full_name: 'Rodney Smith',
      sp: 'Service Provider Build & Cons (BigJoe)',
    };

    renderWithTheme(
      <ElementaryThemedMap
        {...defaultProps}
        route={route}
        serviceProviderData={serviceProviderData}
        showPopup={true}
      />
    );

    expect(screen.getByTestId('info-window')).toBeInTheDocument();
    expect(screen.getByText('Rodney Smith')).toBeInTheDocument();
  });

  it('should not render popup when showPopup is false', () => {
    const route = {
      origin: { lat: -26.2051, lng: 28.0471 },
      destination: { lat: -26.1514869, lng: 27.924998 },
    };
    
    const serviceProviderData = {
      id: 109,
      full_name: 'Rodney Smith',
      sp: 'Service Provider Build & Cons (BigJoe)',
    };

    renderWithTheme(
      <ElementaryThemedMap
        {...defaultProps}
        route={route}
        serviceProviderData={serviceProviderData}
        showPopup={false}
      />
    );

    expect(screen.queryByTestId('info-window')).not.toBeInTheDocument();
  });

  it('should render search component when search prop is true', () => {
    renderWithTheme(
      <ElementaryThemedMap {...defaultProps} search={true} />
    );
    
    expect(screen.getByTestId('autocomplete')).toBeInTheDocument();
  });

  it('should use fallback service provider name when full_name is not available', () => {
    const route = {
      origin: { lat: -26.2051, lng: 28.0471 },
      destination: { lat: -26.1514869, lng: 27.924998 },
    };
    
    const serviceProviderData = {
      id: 109,
      sp: 'Service Provider Build & Cons (BigJoe)',
    };

    renderWithTheme(
      <ElementaryThemedMap
        {...defaultProps}
        route={route}
        serviceProviderData={serviceProviderData}
        showPopup={true}
      />
    );

    expect(screen.getByText('Service Provider Build & Cons (BigJoe)')).toBeInTheDocument();
  });
});
